package storagesystem

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

const S3URLPrefix = "s3://"

type S3FileStorage struct {
	Client *minio.Client
}

func NewS3FileStorage(endpointURL, accessKeyID, secretAccessKey string) (*S3FileStorage, error) {
	// Parse the endpoint URL to extract scheme and host
	parsedURL, err := url.Parse(endpointURL)
	if err != nil {
		return nil, fmt.Errorf("invalid S3 endpoint URL: %w", err)
	}

	// Validate scheme
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return nil, fmt.Errorf("S3 endpoint must use http:// or https:// scheme, got: %s", parsedURL.Scheme)
	}

	// Determine if TLS should be used based on scheme
	secure := parsedURL.Scheme == "https"

	// Extract host (includes port if specified)
	host := parsedURL.Host
	if host == "" {
		return nil, fmt.Errorf("S3 endpoint URL must include a host")
	}

	minioClient, err := minio.New(host, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKeyID, secretAccessKey, ""),
		Secure: secure,
	})
	if err != nil {
		return nil, err
	}

	return &S3FileStorage{
		Client: minioClient,
	}, nil
}

func (s *S3FileStorage) Name() string {
	return "s3"
}

func (s *S3FileStorage) PreSignedPutUrl(ctx context.Context, bucket, fileUrl, contentType string, expireDuration time.Duration) (string, error) {
	reqParams := make(map[string]string)
	if contentType != "" {
		reqParams["Content-Type"] = contentType
	}
	u, err := s.Client.PresignedPutObject(ctx, bucket, fileUrl, expireDuration)
	return u.String(), err
}

func (s *S3FileStorage) PreSignedGetUrl(ctx context.Context, bucket, fileUrl string, expireDuration time.Duration) (string, error) {
	u, err := s.Client.PresignedGetObject(ctx, bucket, fileUrl, expireDuration, nil)
	return u.String(), err
}

func (s *S3FileStorage) CopyFile(ctx context.Context, srcBucket, srcFileUrl, destBucket, destFileUrl string) error {
	if _, err := s.Client.CopyObject(ctx, minio.CopyDestOptions{
		Bucket: destBucket,
		Object: destFileUrl,
	}, minio.CopySrcOptions{
		Bucket: srcBucket,
		Object: srcFileUrl,
	}); err != nil {
		return err
	}
	return nil
}

func (s *S3FileStorage) GetUrl(ctx context.Context, bucket, fileUrl string) string {
	return S3URLPrefix + bucket + "/" + fileUrl
}

func (s *S3FileStorage) Delete(ctx context.Context, bucket, fileUrl string) error {
	return s.Client.RemoveObject(ctx, bucket, fileUrl, minio.RemoveObjectOptions{})
}
