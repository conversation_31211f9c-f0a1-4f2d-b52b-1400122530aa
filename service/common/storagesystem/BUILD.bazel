load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "storagesystem",
    srcs = [
        "filestorage.go",
        "gcsstorage.go",
        "s3storage.go",
        "storage_settings.go",
    ],
    importpath = "sentioxyz/sentio/service/common/storagesystem",
    visibility = ["//visibility:public"],
    deps = [
        "//common/log",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@com_github_minio_minio_go_v7//pkg/credentials",
        "@com_google_cloud_go_storage//:storage",
    ],
)

go_test(
    name = "storagesystem_test",
    srcs = ["filestorage_test.go"],
    embed = [":storagesystem"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
