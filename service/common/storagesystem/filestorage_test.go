package storagesystem

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFileStorageSystem_S3Upload(t *testing.T) {
	ctx := context.Background()

	// Test with S3 endpoint set - should use S3 storage
	t.Run("S3 endpoint set - should use S3 storage", func(t *testing.T) {
		t.SkipNow()
		settings := &StorageSettings{
			S3Endpoint:    "http://localhost:9000",
			S3AccessKeyID: "myminioadmin",
			S3SecretKey:   "minio-secret-key-change-me",
			Bucket:        "test-bucket",
			UploadPrefix:  "uploads",
			Prefix:        "files",
		}

		fs := &FileStorageSystem{
			Settings: settings,
		}

		fileID := "test-file-123"
		contentType := "application/json"

		// Test NewUploadFile method
		file, err := fs.NewUploadFile(ctx, fileID, contentType)
		require.NoError(t, err)
		require.NotNil(t, file)

		// Verify file object properties
		expectedObject := fmt.Sprintf("%s/%s", settings.UploadPrefix, fileID)
		assert.Equal(t, expectedObject, file.GetObject())
		assert.Equal(t, settings.Bucket, file.GetBucket())
		assert.NotNil(t, file.Engine)

		// Test that we can generate a presigned upload URL
		uploadURL, err := file.PreSignedUploadUrl(ctx, 5*time.Minute)
		require.NoError(t, err)
		assert.Contains(t, uploadURL, "localhost:9000")
		assert.Contains(t, uploadURL, settings.Bucket)
		assert.Contains(t, uploadURL, fileID)
		assert.Contains(t, file.GetUrl(ctx), "s3://")
	})

	// Test with empty S3 endpoint - should use GCS storage
	t.Run("S3 endpoint empty - should use GCS storage", func(t *testing.T) {
		t.SkipNow()
		settings := &StorageSettings{
			S3Endpoint:        "",
			GcsServiceAccount: "<EMAIL>",
			Bucket:            "gcs-bucket",
			UploadPrefix:      "uploads",
			Prefix:            "files",
		}

		fs := &FileStorageSystem{
			Settings: settings,
		}

		fileID := "test-file-gcs"
		contentType := "image/png"

		// Test NewUploadFile method - this might fail due to GCS credentials
		// but we're testing the storage selection logic
		file, err := fs.NewUploadFile(ctx, fileID, contentType)

		// We expect this to attempt GCS, which may fail without proper credentials
		// The key is that it should NOT attempt S3 when S3Endpoint is empty
		if err != nil {
			// Error should be GCS-related, not S3-related
			assert.NotContains(t, err.Error(), "minio")
			assert.NotContains(t, err.Error(), "S3")
		} else {
			// If it succeeds, verify the file properties
			require.NotNil(t, file)
			assert.Contains(t, file.GetUrl(ctx), "gcs://")
		}
	})
}

func TestFileStorageSystem_S3UploadFlow(t *testing.T) {
	t.SkipNow()
	ctx := context.Background()
	settings := &StorageSettings{
		S3Endpoint:    "http://localhost:9000",
		S3AccessKeyID: "myminioadmin",
		S3SecretKey:   "minio-secret-key-change-me",
		Bucket:        "test-bucket-flow",
		UploadPrefix:  "uploads",
		Prefix:        "files",
	}

	fs := &FileStorageSystem{
		Settings: settings,
	}

	fileID := "test-upload-flow"
	contentType := "application/json"

	// Test complete upload flow
	t.Run("Complete S3 upload flow", func(t *testing.T) {
		// Step 1: Create upload file
		uploadFile, err := fs.NewUploadFile(ctx, fileID, contentType)
		require.NoError(t, err)
		require.NotNil(t, uploadFile)

		// Verify upload file properties
		expectedUploadObject := fmt.Sprintf("%s/%s", settings.UploadPrefix, fileID)
		assert.Equal(t, expectedUploadObject, uploadFile.GetObject())
		assert.Equal(t, settings.Bucket, uploadFile.GetBucket())

		// Test that we can generate presigned URLs
		uploadURL, err := uploadFile.PreSignedUploadUrl(ctx, 5*time.Minute)
		require.NoError(t, err)
		assert.Contains(t, uploadURL, "localhost:9000")

		downloadURL, err := uploadFile.PreSignedDownloadUrl(ctx, 5*time.Minute)
		require.NoError(t, err)
		assert.Contains(t, downloadURL, "localhost:9000")

		// Step 2: Test finalize upload (moves from upload prefix to main prefix)
		finalizedFile, err := fs.FinalizeUpload(ctx, fileID)
		require.NoError(t, err)
		require.NotNil(t, finalizedFile)

		// Verify finalized file properties
		expectedFinalObject := fmt.Sprintf("%s/%s", settings.Prefix, fileID)
		assert.Equal(t, expectedFinalObject, finalizedFile.GetObject())
		assert.Equal(t, settings.Bucket, finalizedFile.GetBucket())

		// Test that we can generate presigned URL for finalized file
		finalDownloadURL, err := finalizedFile.PreSignedDownloadUrl(ctx, 5*time.Minute)
		require.NoError(t, err)
		assert.Contains(t, finalDownloadURL, "localhost:9000")
		assert.Contains(t, finalDownloadURL, settings.Prefix)
		assert.Contains(t, finalDownloadURL, fileID)
	})
}

func TestFileStorageSystem_GetFromUrl(t *testing.T) {
	ctx := context.Background()
	settings := &StorageSettings{
		S3Endpoint:    "http://localhost:9000",
		S3AccessKeyID: "myminioadmin",
		S3SecretKey:   "minio-secret-key-change-me",
		Bucket:        "test-bucket",
		UploadPrefix:  "uploads",
		Prefix:        "files",
	}

	fs := &FileStorageSystem{
		Settings: settings,
	}

	t.Run("Valid S3 URL", func(t *testing.T) {
		url := "s3://test-bucket/path/to/file.json"
		file, err := fs.GetFromUrl(ctx, url)
		require.NoError(t, err)
		require.NotNil(t, file)

		assert.Equal(t, "test-bucket", file.GetBucket())
		assert.Equal(t, "path/to/file.json", file.GetObject())
		assert.Equal(t, "s3", file.Engine.Name())
	})

	t.Run("Valid GCS URL", func(t *testing.T) {
		t.SkipNow()
		url := "gcs://gcs-bucket/path/to/file.json"
		file, err := fs.GetFromUrl(ctx, url)
		require.NoError(t, err)
		require.NotNil(t, file)

		assert.Equal(t, "gcs-bucket", file.GetBucket())
		assert.Equal(t, "path/to/file.json", file.GetObject())
		assert.Equal(t, "gcs", file.Engine.Name())
	})

	t.Run("Invalid URL format", func(t *testing.T) {
		url := "invalid-url"
		_, err := fs.GetFromUrl(ctx, url)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid storage URL")
	})

	t.Run("Unsupported protocol", func(t *testing.T) {
		url := "azure://bucket/file.json"
		_, err := fs.GetFromUrl(ctx, url)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported storage protocol")
	})

	t.Run("S3 URL but no S3 configured", func(t *testing.T) {
		// Use settings without S3 endpoint
		fsNoS3 := &FileStorageSystem{
			Settings: &StorageSettings{
				S3Endpoint: "", // Empty to simulate no S3 config
			},
		}

		url := "s3://bucket/file.json"
		_, err := fsNoS3.GetFromUrl(ctx, url)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "S3 storage is not  configured")
	})
}
