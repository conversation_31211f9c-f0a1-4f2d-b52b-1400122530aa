package storagesystem

import (
	"cloud.google.com/go/storage"
	"context"
	"fmt"
	"sentioxyz/sentio/common/log"
	"time"
)

const GcsURLPrefix = "gcs://"

type GCSFileStorage struct {
	gcsServiceAccount string
	client            *storage.Client
}

func NewGCSFileStorage(gcsServiceAccount string) *GCSFileStorage {
	ctx := context.Background()
	client, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatale(err)
	}
	return &GCSFileStorage{
		gcsServiceAccount: gcsServiceAccount,
		client:            client,
	}
}

func (g *GCSFileStorage) Name() string {
	return "gcs"
}

func (g *GCSFileStorage) PreSignedPutUrl(ctx context.Context, bucket, fileUrl, contentType string, expireDuration time.Duration) (string, error) {

	gcsClient := g.client

	opts := &storage.SignedURLOptions{
		GoogleAccessID: g.gcsServiceAccount,
		Method:         "PUT",
		Expires:        time.Now().Add(expireDuration),
		Headers: []string{
			"Content-Type:" + contentType,
		}, Scheme: storage.SigningSchemeV4,
	}
	return gcsClient.Bucket(bucket).SignedURL(fileUrl, opts)
}

func (g *GCSFileStorage) PreSignedGetUrl(ctx context.Context, bucket, fileUrl string, expireDuration time.Duration) (string, error) {
	gcsClient := g.client
	opts := &storage.SignedURLOptions{
		GoogleAccessID: g.gcsServiceAccount,
		Method:         "GET",
		Expires:        time.Now().Add(expireDuration),
		Scheme:         storage.SigningSchemeV4,
	}
	return gcsClient.Bucket(bucket).SignedURL(fileUrl, opts)
}

func (g *GCSFileStorage) CopyFile(ctx context.Context, srcBucket, srcFileUrl, destBucket, destFileUrl string) error {
	src := g.client.Bucket(srcBucket).Object(srcFileUrl)
	dst := g.client.Bucket(destBucket).Object(destFileUrl)
	copier := dst.CopierFrom(src)
	if _, err := copier.Run(ctx); err != nil {
		return err
	}

	return nil
}

func (g *GCSFileStorage) GetUrl(ctx context.Context, bucket, fileUrl string) string {
	return fmt.Sprintf("%s%s/%s", GcsURLPrefix, bucket, fileUrl)
}

func (g *GCSFileStorage) Delete(ctx context.Context, bucket, fileUrl string) error {
	gcsClient, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatale(err)
	}

	obj := gcsClient.Bucket(bucket).Object(fileUrl)
	if err := obj.Delete(ctx); err != nil {
		return fmt.Errorf("failed to delete object %s from bucket %s: %w", fileUrl, bucket, err)
	}
	return nil
}
