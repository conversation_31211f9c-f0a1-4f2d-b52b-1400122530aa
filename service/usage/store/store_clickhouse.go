package store

import (
	"context"
	"fmt"
	"github.com/ClickHouse/clickhouse-go/v2"
	"sentioxyz/sentio/common/clickhouse/helper"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/period"
	"sentioxyz/sentio/service/usage/store/models"
	"strings"
	"time"
)

type ClickhouseStore struct {
	client  clickhouse.Conn
	cluster string
}

func NewClickhouseStorage(ctx context.Context, dialDBDsn string) (s *ClickhouseStore, err error) {
	s = &ClickhouseStore{}
	s.client, s.cluster, err = setupDialClickhouseDB(ctx, dialDBDsn)
	if err != nil {
		return
	}
	return
}

const (
	rawTableName   = "dialogue"
	dailyTableName = "dialogue_1d"
	mvName         = "dialogue_1d_mv"
)

func setupDialClickhouseDB(ctx context.Context, dsn string) (clickhouse.Conn, string, error) {
	_, logger := log.FromContext(ctx)
	option, err := clickhouse.ParseDSN(dsn)
	if err != nil {
		return nil, "", err
	}
	conn, err := clickhouse.Open(option)
	if err != nil {
		return nil, "", err
	}
	cluster, err := helper.AutoGetCluster(ctx, conn)
	if err != nil {
		return nil, "", err
	}
	var onClusterPart string
	if cluster != "" {
		onClusterPart = fmt.Sprintf("ON CLUSTER '%s'", cluster)
	}

	// raw table
	sql := fmt.Sprintf("CREATE TABLE IF NOT EXISTS %s %s ("+
		"request_time DateTime, "+
		"owner_id String, "+
		"owner_type String, "+
		"project_id String, "+
		"user_id String, "+
		"sku String, "+
		"custom_tags Map(String, String), "+
		"cost UInt64, "+
		"units UInt64, "+
		"succeed UInt64, "+
		"latency_ms UInt32 "+
		") "+
		"ENGINE %s "+
		"PARTITION BY toYYYYMMDD(request_time) "+
		"ORDER BY (request_time,owner_id,owner_type,project_id,user_id,sku,custom_tags) "+
		"TTL request_time + INTERVAL 7 DAY "+
		"SETTINGS index_granularity = 8192",
		rawTableName,
		onClusterPart,
		helper.BuildEngine(cluster != "", "MergeTree"))
	if err = conn.Exec(ctx, sql); err != nil {
		logger.With("sql", sql).Errorfe(err, "create %s failed", rawTableName)
		return nil, "", fmt.Errorf("create %s failed: %w", rawTableName, err)
	}
	// daily agg table
	sql = fmt.Sprintf("CREATE TABLE IF NOT EXISTS %s %s ("+
		"request_date Date, "+
		"owner_id String, "+
		"owner_type String, "+
		"project_id String, "+
		"user_id String, "+
		"sku String, "+
		"custom_tags Map(String, String), "+
		"cost AggregateFunction(sum, UInt64), "+
		"units AggregateFunction(sum, UInt64), "+
		"succeed AggregateFunction(sum, UInt64), "+
		"total AggregateFunction(sum, UInt64), "+
		"latency_ms_max AggregateFunction(max, UInt32), "+
		"latency_ms_sum AggregateFunction(sum, UInt64) "+
		") "+
		"ENGINE %s"+
		"PARTITION BY request_date "+
		"ORDER BY (request_date,owner_id,owner_type,project_id,user_id,sku,custom_tags) "+
		"SETTINGS index_granularity = 8192",
		dailyTableName,
		onClusterPart,
		helper.BuildEngine(cluster != "", "AggregatingMergeTree"))
	if err = conn.Exec(ctx, sql); err != nil {
		logger.With("sql", sql).Errorfe(err, "create %s failed", dailyTableName)
		return nil, "", fmt.Errorf("create %s failed: %w", dailyTableName, err)
	}
	// mv from raw to daily table
	sql = fmt.Sprintf("CREATE MATERIALIZED VIEW IF NOT EXISTS %s %s "+
		"TO %s "+
		"AS SELECT "+
		"toDate(request_time) AS request_date, "+
		"owner_id, "+
		"owner_type, "+
		"project_id, "+
		"user_id, "+
		"sku, "+
		"custom_tags, "+
		"sumState(cost) AS cost, "+
		"sumState(units) AS units, "+
		"sumState(succeed) AS succeed, "+
		"sumState(toUInt64(1)) AS total, "+
		"maxState(latency_ms) AS latency_ms_max, "+
		"sumState(toUInt64(latency_ms)) AS latency_ms_sum "+
		"FROM %s "+
		"GROUP BY request_date,owner_id,owner_type,project_id,user_id,sku,custom_tags",
		mvName, onClusterPart, dailyTableName, rawTableName)
	if err = conn.Exec(ctx, sql); err != nil {
		logger.With("sql", sql).Errorfe(err, "create %s failed", mvName)
		return nil, "", fmt.Errorf("create %s failed: %w", mvName, err)
	}
	return conn, cluster, nil
}

func (s *ClickhouseStore) Save(ctx context.Context, dials []*models.Dialogue) error {
	_, logger := log.FromContext(ctx)
	batch, err := s.client.PrepareBatch(ctx, fmt.Sprintf("INSERT INTO %s ("+
		"request_time,"+
		"owner_id,"+
		"owner_type,"+
		"project_id,"+
		"user_id,"+
		"sku,"+
		"custom_tags,"+
		"cost,"+
		"units,"+
		"succeed,"+
		"latency_ms"+
		")", rawTableName))
	if err != nil {
		logger.Errorfe(err, "prepare batch failed")
		return err
	}
	for _, dial := range dials {
		err = batch.Append(
			dial.RequestTime,
			dial.OwnerID,
			dial.OwnerType,
			dial.ProjectID,
			dial.UserID,
			dial.SKU,
			dial.CustomTags,
			dial.Cost,
			dial.Units,
			dial.Succeed,
			dial.LatencyMs)
		if err != nil {
			logger.Errorfe(err, "batch append failed, dial: %#v", dial)
			return err
		}
	}
	if err = batch.Send(); err != nil {
		logger.Errorfe(err, "batch send failed")
		return err
	}
	if err = batch.Close(); err != nil {
		logger.Errorfe(err, "batch close failed")
		return err
	}
	return nil
}

func (s *ClickhouseStore) Statistic(
	ctx context.Context,
	ownerID, ownerType string,
	statTags models.DialogueTags,
	splitCustomTags bool,
	queryPeriod period.Period,
	start, end time.Time,
	ranking bool,
	offset int,
	limit int,
) ([]models.Stat, error) {
	startAt := time.Now()
	_, logger := log.FromContext(ctx)
	if _, someDay := queryPeriod.Div(period.Day); ranking && !someDay {
		return nil, fmt.Errorf("ranking period should be an integral multiple of one day")
	}
	crossOwners := ownerID == models.TagValueAll || ownerID == models.TagValueEach ||
		ownerType == models.TagValueAll || ownerType == models.TagValueEach
	if !ranking && crossOwners {
		return nil, fmt.Errorf("owner requires exact value")
	}

	where := "request_date >= ? AND request_date < ?"
	whereArgs := []any{start, end}
	var timeWindowField string
	if queryPeriod == period.Day {
		timeWindowField = "request_date AS time_window"
	} else {
		timeWindowField = fmt.Sprintf("toStartOfInterval(request_date, %s) AS time_window", queryPeriod.PGInterval())
	}
	selectFields := []string{timeWindowField}
	groupByFields := []string{"time_window"}
	orderBy := []string{"time_window DESC"}
	if ranking {
		orderBy = []string{"cost DESC"}
	}

	checkField := func(fieldName, value string) {
		switch value {
		case models.TagValueAll:
		case models.TagValueEach:
			selectFields = append(selectFields, fieldName)
			groupByFields = append(groupByFields, fieldName)
			orderBy = append(orderBy, fieldName)
		default:
			selectFields = append(selectFields, fieldName)
			where = where + " AND " + fieldName + " = ?"
			whereArgs = append(whereArgs, value)
			groupByFields = append(groupByFields, fieldName)
		}
	}

	checkField("owner_id", ownerID)
	checkField("owner_type", ownerType)
	checkField("project_id", statTags.ProjectID)
	checkField("user_id", statTags.UserID)
	checkField("sku", statTags.SKU)

	if splitCustomTags {
		selectFields = append(selectFields, "custom_tags")
		groupByFields = append(groupByFields, "custom_tags")
		orderBy = append(orderBy, "custom_tags")
	}

	var limiterPart string
	if ranking {
		if limit > 0 {
			limiterPart = limiterPart + fmt.Sprintf(" LIMIT %d", limit)
		}
		if offset > 0 {
			limiterPart = limiterPart + fmt.Sprintf(" OFFSET %d", offset)
		}
	}

	sql := fmt.Sprintf("SELECT "+
		"%s, "+
		"sumMerge(cost) AS cost, "+
		"sumMerge(succeed) AS succeed, "+
		"sumMerge(units) AS units, "+
		"sumMerge(total) AS total, "+
		"maxMerge(latency_ms_max) AS latency_ms_max, "+
		"sumMerge(latency_ms_sum) AS latency_ms_sum "+
		"FROM %s "+
		"WHERE %s "+
		"GROUP BY %s "+
		"ORDER BY %s"+
		"%s",
		strings.Join(selectFields, ","),
		dailyTableName,
		where,
		strings.Join(groupByFields, ", "),
		strings.Join(orderBy, ", "),
		limiterPart,
	)
	logger = logger.With("sql", sql, "args", whereArgs)
	rows, err := s.client.Query(ctx, sql, whereArgs...)
	if err != nil {
		logger.Errorfe(err, "execute clickhouse query failed")
		return nil, err
	}
	var row struct {
		TimeWindow   time.Time         `ch:"time_window"`
		OwnerID      string            `ch:"owner_id"`
		OwnerType    string            `ch:"owner_type"`
		ProjectID    string            `ch:"project_id"`
		UserID       string            `ch:"user_id"`
		SKU          string            `ch:"sku"`
		CustomTags   map[string]string `ch:"custom_tags"`
		Cost         uint64            `ch:"cost"`
		Units        uint64            `ch:"units"`
		Succeed      uint64            `ch:"succeed"`
		Total        uint64            `ch:"total"`
		LatencyMsMax uint32            `ch:"latency_ms_max"`
		LatencyMsSum uint64            `ch:"latency_ms_sum"`
	}
	var stat []models.Stat
	for rows.Next() {
		if err = rows.ScanStruct(&row); err != nil {
			logger.Errorfe(err, "executed clickhouse query but scan row #%d failed", len(stat))
			return nil, err
		}
		stat = append(stat, models.Stat{
			TimeWindow: row.TimeWindow,
			OwnerID:    row.OwnerID,
			OwnerType:  row.OwnerType,
			DialogueTags: models.DialogueTags{
				ProjectID:  row.ProjectID,
				UserID:     row.UserID,
				SKU:        row.SKU,
				CustomTags: row.CustomTags,
			},
			Cost:         row.Cost,
			Units:        row.Units,
			Succeed:      row.Succeed,
			Total:        row.Total,
			LatencyMsMax: row.LatencyMsMax,
			LatencyMsAvg: uint32(row.LatencyMsSum / row.Total),
		})
	}
	logger.LogTimeUsed(startAt, time.Second*3, "executed clickhouse query", "result", len(stat))
	return stat, nil
}
