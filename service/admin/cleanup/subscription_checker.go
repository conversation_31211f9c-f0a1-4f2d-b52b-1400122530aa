package cleanup

import (
	"context"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/billing/models"
	"sentioxyz/sentio/service/billing/repository"
	commonModels "sentioxyz/sentio/service/common/models"
	protoscommon "sentioxyz/sentio/service/common/protos"
	models2 "sentioxyz/sentio/service/usage/store/models"
	"time"
)

type SubscriptionChecker struct {
	db         *gorm.DB
	repository repository.BillingRepository
}

func NewSubscriptionChecker(db *gorm.DB) *SubscriptionChecker {
	return &SubscriptionChecker{
		db:         db,
		repository: repository.NewRepository(db),
	}
}

func (c *SubscriptionChecker) Cleanup(ctx context.Context) {
	logger := log.WithContext(ctx)

	// Check all active subscriptions for tier mismatch and trial expiration
	var activeSubscriptions []*models.Subscription
	err := c.db.Preload("Account").Preload("Plan").
		Where("status = ?", models.SubscriptionStatusActive).
		Find(&activeSubscriptions).Error

	if err != nil {
		logger.Errore(err)
		return
	}

	for _, subscription := range activeSubscriptions {
		// Skip if subscription has no plan or account
		plan := subscription.Plan
		account := subscription.Account
		if plan == nil || account == nil {
			continue
		}

		account.GetOwner(c.db)
		tier := int32(protoscommon.Tier_FREE)
		if account.OwnerAsOrg != nil {
			err := account.OwnerAsOrg.GetTier(c.db)
			if err != nil {
				logger.Errore(err)
				continue
			}
			tier = account.OwnerAsOrg.Tier
		} else if account.OwnerAsUser != nil {
			err := account.OwnerAsUser.GetTier(c.db)
			if err != nil {
				logger.Errore(err)
				continue
			}
			tier = account.OwnerAsUser.Tier
		}
		userTier := protoscommon.Tier_name[tier]

		// Case 1: Check if plan tier matches account tier
		if plan.TierDefaultPlan != "" && plan.TierDefaultPlan != userTier {
			err = c.updateAccountTier(ctx, account, plan.TierDefaultPlan)
			if err != nil {
				logger.Errorfe(err, "Error updating account tier for account: %s", account.Name)
			} else {
				logger.Infof("Updated account %s tier to %s to match subscription plan, tier was %s",
					account.Name, plan.TierDefaultPlan, userTier)
			}
		} else if plan.TierDefaultPlan == "" && userTier == protoscommon.Tier_name[int32(protoscommon.Tier_FREE)] {
			// if plan's default tier is not set, set account tier to dev
			err = c.updateAccountTier(ctx, account, protoscommon.Tier_name[int32(protoscommon.Tier_DEV)])
			if err != nil {
				logger.Errorfe(err, "Error updating account tier to dev for account: %s", account.Name)
			} else {
				logger.Infof("Updated account %s tier to dev as plan's default tier is not set", account.Name)
			}
		}

		// Case 2: Check if plan is trial and has expired
		if plan.IsTrial && plan.TrialDurationDays != nil {
			// Calculate expiration time based on when subscription started
			startTime := subscription.StartedAt
			if startTime == nil {
				startTime = &subscription.CreatedAt
			}

			trialDuration := time.Duration(24*(*plan.TrialDurationDays)) * time.Hour
			expirationTime := startTime.Add(trialDuration)

			// If trial has expired
			if time.Now().After(expirationTime) {
				err = c.expireSubscription(ctx, subscription)
				if err != nil {
					logger.Errorfe(err, "Error expiring trial subscription: %s", subscription.ID)
				} else {
					logger.Infof("Trial ended, expired subscription %s", subscription.ID)
				}
			}
		}
	}

	for _, subscription := range activeSubscriptions {
		account := subscription.Account
		if account.UsageOverCapLimit != nil {
			c.verifyAccountLimit(ctx, account, subscription)
		}
	}
}

func (c *SubscriptionChecker) verifyAccountLimit(ctx context.Context, account *commonModels.Account, sub *models.Subscription) {
	logger := log.WithContext(ctx)
	limiter := models2.OwnerLimiter{}
	tx := c.db.WithContext(ctx)
	err := tx.Where("owner_id = ?", account.OwnerID).First(&limiter).Error
	if err != nil {
		logger.Errore(err, "Failed to find account usage over cap limit")
		return
	}

	limit := account.UsageOverCapLimit
	cost := decimal.Zero
	if sub.Plan.UnitPrice.IsPositive() {
		cost = limit.Div(sub.Plan.UnitPrice).Add(decimal.NewFromInt(sub.Plan.UnitCap))
	} else {
		cost = decimal.NewFromInt(sub.Plan.UnitCap)
	}

	if cost.BigInt().Uint64() != limiter.Cost {
		logger.Infof("Updating account %s usage over cap limit from %d to %d", account.ID, limiter.Cost, cost.BigInt().Uint64())
		limiter.Cost = cost.BigInt().Uint64()
		if err = tx.Save(&limiter).Error; err != nil {
			logger.Errore(err, "Failed to update account usage over cap limit")
			return
		}
	}

}

func (c *SubscriptionChecker) updateAccountTier(ctx context.Context, account *commonModels.Account, newTier string) error {
	return c.db.Transaction(func(tx *gorm.DB) error {
		return c.repository.UpdateTier(tx, account, newTier)
	})
}

func (c *SubscriptionChecker) expireSubscription(ctx context.Context, subscription *models.Subscription) error {
	return c.db.Transaction(func(tx *gorm.DB) error {
		subscription.Status = models.SubscriptionStatusEnded
		now := time.Now()
		subscription.EndedAt = &now
		err := tx.Save(&subscription).Error
		if err != nil {
			return err
		}
		err = c.repository.UpdateTier(tx, subscription.Account, protoscommon.Tier_name[int32(protoscommon.Tier_FREE)])
		if err != nil {
			return err
		}
		return nil
	})
}
