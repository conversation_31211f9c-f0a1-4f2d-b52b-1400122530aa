load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "cleanup",
    srcs = [
        "chain_state_cleaner.go",
        "cleanup_job.go",
        "custom_chain_cleaner.go",
        "driverjob_cleaner.go",
        "event_cleaner.go",
        "leak_cleaner.go",
        "notification_cleaner.go",
        "project_cleaner.go",
        "simulation_cleaner.go",
        "subscription_checker.go",
    ],
    importpath = "sentioxyz/sentio/service/admin/cleanup",
    visibility = ["//visibility:public"],
    deps = [
        "//common/log",
        "//common/timescale",
        "//common/utils",
        "//k8s/client",
        "//k8s/controllers",
        "//service/alert/models",
        "//service/analytic/repository/models",
        "//service/billing/models",
        "//service/billing/repository",
        "//service/common/auth",
        "//service/common/models",
        "//service/common/protos",
        "//service/common/repository",
        "//service/common/rpc",
        "//service/contract/protos",
        "//service/exports/models",
        "//service/mvcontroller/usermv",
        "//service/processor/models",
        "//service/processor/protos",
        "//service/solidity/models",
        "//service/solidity/protos",
        "//service/usage/store/models",
        "//service/web/clients",
        "//service/web/models",
        "//service/web/repository",
        "@com_github_pkg_errors//:errors",
        "@com_github_samber_lo//:lo",
        "@com_github_shopspring_decimal//:decimal",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@io_opentelemetry_go_otel//:otel",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_x_net//context",
    ],
)
