// https://grpc-ecosystem.github.io/grpc-gateway/docs/mapping/customizing_openapi_output/
// https://github.com/grpc-ecosystem/grpc-gateway/blob/main/examples/internal/proto/examplepb/a_bit_of_everything.proto

syntax = "proto3";

package web_service;

option go_package = "sentioxyz/sentio/service/web/protos";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/visibility.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

import "service/common/protos/common.proto";
import "service/insights/protos/insights_service.proto";
import "service/analytic/protos/analytic_service.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Sentio API";
    description: "Sentio Open API for query data";
  }
  host: "app.sentio.xyz"
  //  base_path: "/api/v1"
  schemes: HTTPS
    security_definitions: {
    security: {
      key: "ApiKeyHeaderAuth";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "api-key";
      }
    },
    security: {
      key: "ApiKeyQueryAuth";
      value: {
        type: TYPE_API_KEY;
        in: IN_QUERY;
        name: "api-key";
      }
    }
  }
  security: [
    {
      security_requirement: {
        key: "ApiKeyHeaderAuth";
        value: {};
      }
    },
    {
      security_requirement: {
        key: "ApiKeyQueryAuth";
        value: {};
      }
    },{
      security_requirement: {}
    }
  ]
};

message Dashboard {
  string id = 1;
  string name = 2;
  string project_id = 3;
  string description = 4;
  google.protobuf.Timestamp  created_at = 5;
  google.protobuf.Timestamp  updated_at = 6;
  map<string, Panel> panels = 7;
  message Layouts {
    message Layout {
      string i = 1;
      int32 x = 2;
      int32 y = 3;
      int32 w = 4;
      int32 h = 5;
    }
    repeated Layout layouts = 1;
  }
  message ResponsiveLayouts {
    map<string, Layouts> responsiveLayouts = 1;
  }
  ResponsiveLayouts layouts = 8;
  message Extra {
    map<string, TemplateVariable> template_variables = 1;
    message TemplateVariable{
      string field = 1;
      string default_value = 2;
      string sourceName = 3;
      repeated string options = 4;
    }
    repeated TemplateView template_views = 2;
    message TemplateView {
      map<string, string> values = 1;
    }
  }
  Extra extra = 9;
  DashboardSharing sharing = 10;
  bool default = 11;
  DashboardVisibility visibility = 12;
  string owner_id = 13;
  enum DashboardVisibility {
    INTERNAL = 0;
    PRIVATE = 1;
    PUBLIC = 2;
  }
  repeated string tags = 14;
  string url = 15;
  string project_owner = 16;
  string project_slug = 17;
  repeated string create_panels = 18;
  repeated string edit_panels = 19;
}

message Panel {
  string id = 1;
  string name = 2;
  string dashboard_id = 3;
  Chart chart = 4;
  common.UserInfo creator = 5;
  common.UserInfo updater = 6;
}

message Note {
  string content = 1;
  enum FontSize {
    MD = 0;
    SM = 1;
    LG = 2;
    XL = 3;
    XXL = 4;
  }
  enum Alignment {
    LEFT = 0;
    CENTER = 1;
    RIGHT = 2;
  }
  enum VerticalAlignment {
    TOP = 0;
    MIDDLE = 1;
    BOTTOM = 2;
  }
  FontSize font_size = 2;
  Alignment text_align = 3;
  VerticalAlignment vertical_align = 4;
  string background_color = 5;
  string text_color = 6;
}

message EventLogsConfig {
  message TimeRangeOverride {
    bool enabled = 1;
    common.TimeRange timeRange = 2;
  }
  common.EventLogConfig columns_config = 1;
  TimeRangeOverride time_range_override = 2;
  string query = 3;
  string sourceName = 4;
}

message Chart {
  ChartType type = 1;
  enum ChartType {
    LINE = 0;
    AREA = 1;
    BAR = 2;
    BAR_GAUGE = 3;
    TABLE = 4;
    QUERY_VALUE = 5;
    PIE = 6;
    NOTE = 15;
    SCATTER = 16;
  }
  repeated common.Query queries = 2;
  repeated common.Formula formulas = 3;
  ChartConfig config = 4;
  Note note = 5;
  DataSourceType datasource_type = 6;
  repeated common.SegmentationQuery segmentation_queries = 7;
  repeated insights_service.QueryRequest.Query insights_queries = 8;
  EventLogsConfig event_logs_config = 9;
  common.RetentionQuery retention_query = 10;
  string sql_query = 11;
  string sql_query_id = 12;
  analytic_service.ExecuteEngine sql_execute_engine = 13;

  enum DataSourceType {
    METRICS = 0;
    NOTES = 1;
    ANALYTICS = 2;
    INSIGHTS = 3;
    EVENTS = 4;
    RETENTION = 5;
    SQL = 6;
  }
}

message ChartConfig {
  message YAxisConfig {
    string min = 1;
    string max = 2;
    bool scale = 3;
    string stacked = 4;
    string column = 5;
    string name = 6;
  }

  message XAxisConfig {
    string type = 1;
    string min = 2;
    string max = 3;
    bool scale = 4;
    string name = 5;
    string column = 6;
    Sort sort = 7;
    string format = 8;
  }

  message LabelConfigColumn {
    string name = 1;
    bool show_label = 2;
    bool show_value = 3;
  }

  message LabelConfig {
    repeated LabelConfigColumn columns = 1;
    string alias = 2;
  }

  message BarGaugeConfig {
    Direction direction = 1;
    Calculation calculation = 2;
    Sort sort = 3;
  }
  enum Direction {
    HORIZONTAL = 0;
    VERTICAL = 1;
  }
  enum Calculation {
    LAST = 0;
    FIRST = 1;
    MEAN = 2;
    TOTAL = 3;
    ALL = 4;
    MIN = 5;
    MAX = 6;
  }
  message Sort {
    SortBy sortBy = 1;
    bool orderDesc = 2;
  }
  enum SortBy {
    ByName = 0;
    ByValue = 1;
  }
  message ValueConfig {
    ValueFormatter value_formatter = 1;
    bool show_value_label = 2;
    int32 max_significant_digits = 3;
    string dateFormat = 4;
    repeated MappingRule mapping_rules = 5;
    Style style = 6;
    int32 max_fraction_digits = 7;
    enum Style {
      Standard = 0;
      Compact = 1;
      Scientific = 2;
      Percent = 3;
      Currency = 4;
      None = 5;
    }
    int32 precision = 8;
    string currency_symbol = 9;
    bool tooltip_total = 10;
  }
  message MappingRule {
    string comparison = 1;
    double value = 2;
    string text = 3;
    ColorTheme color_theme = 4;
  }
  enum ValueFormatter {
    NumberFormatter = 0;
    DateFormatter = 1;
    StringFormatter = 2;
  }
  message TimeRangeOverride {
    bool enabled = 1;
    common.TimeRange timeRange = 2;
    CompareTime compare_time = 3;
  }

  message CompareTime {
    common.Duration ago = 1;
  }

  message TableConfig {
    Calculation calculation = 1;
    //    map<string, bool> sort_by = 2;
    map<string, bool> show_columns = 3;
    repeated ColumnSort sort_columns = 4;
    repeated string column_orders = 5;
    map<string, int32> column_widths = 6;
    bool show_plain_data = 7;
    map<string, Calculation> calculations = 8;
    map<string, ValueConfig> value_configs = 9;
    int32 row_limit = 10;
  }
  message ColumnSort {
    string column = 1;
    bool order_desc = 2;
  }
  message QueryValueConfig {
    ColorTheme color_theme = 1;
    bool show_background_chart = 3;
    Calculation calculation = 4;
    Calculation series_calculation = 5;
  }
  message ColorTheme {
    string text_color = 1;
    string background_color = 2;
    string theme_type = 3;
  }
  message PieConfig {
    PieType pie_type = 1;
    enum PieType {
      Pie = 0;
      Donut = 1;
    }
    bool show_percent = 2;
    bool show_value = 3;
    Calculation calculation = 4;
  }
  enum MarkerType {
    LINE = 0;
    AREA = 1;
    LINEX = 2;
  }
  message Marker {
    MarkerType type = 1;
    double value = 2;
    string color = 3;
    string label = 4;
    string value_x = 5;
  }
  message LineConfig {
    enum Style {
      Solid = 0;
      Dotted = 1;
    }
    Style style = 1;
  }
  message ScatterConfig {
    string symbol_size = 1;
  }
  YAxisConfig y_axis = 1;
  BarGaugeConfig bar_gauge = 2;
  ValueConfig value_config = 3;
  TimeRangeOverride time_range_override = 4;
  TableConfig table_config = 5;
  QueryValueConfig query_value_config = 6;
  PieConfig pie_config = 7;
  repeated Marker markers = 8;
  LineConfig line_config = 9;
  XAxisConfig x_axis = 10;
  LabelConfig label_config = 11;
  ScatterConfig scatter_config = 12;
}



message GetDashboardRequest {
  // filter the dashboard by id
  string dashboard_id = 1;
  // filter the dashboard by project id
  string project_id = 2;
  // username or organization name
  string owner_name = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  // project slug
  string slug = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
}

message GetDashboardResponse {
  repeated Dashboard dashboards = 1;
  repeated common.Permission permissions = 2;
}

message GetExternalDashboardRequest {
  string dashboard_id = 1;
  string project_id = 2;
  string owner_name = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  string slug = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
  string url = 5;
}

message GetExternalDashboardResponse {
  DashboardResult dashboard = 1;
}

message ListExternalDashboardsRequest {
  string project_id = 1;
  string owner_name = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  string slug = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];

  oneof filter {
    string owner_filter = 7; // show only dashboards owned by this user
    NameTagsFilter tags_filter = 8;
    bool star_filter = 9; // show only caller's starred dashboards
  }
  message NameTagsFilter {
    repeated string tags = 1;
    string name = 2;
  }

  enum OrderBy {
    STARRED = 0;
    UPDATED_AT = 1;
  }
  OrderBy order_by = 10;
  int64 order_by_time_range = 11;
  int32 limit = 12;
  int32 offset = 13;
}

message DashboardResult {
  Dashboard dashboard = 1;
  bool starred = 3;
  repeated common.Permission permissions = 4;
  int32 star_count = 5;
}

message ListExternalDashboardsResponse {
  repeated DashboardResult dashboards = 1;
}

message GetUserRequest {
  oneof get_user_by {
    string user_id = 1;
    string subject = 2;
    string email = 3;
  }
}

message GetUserInfoRequest {
  oneof get_user_info_by {
    string user_id = 1;
    string user_name = 2;
    string email = 3;
  }
}

message SearchUsersInfoRequest {
  // User name
  string query = 1 [(google.api.field_behavior) = REQUIRED];
  // The max number of users to return
  // (-- TODO document default value and max allowed value? --)
  int32 limit = 2;
  // The starting point for pagination
  int32 offset = 3;
}

message SearchUsersInfoResponse {
  repeated common.UserInfo users = 1;
}

message GetProjectByIdRequest {
  string project_id = 1;
}

message GetProjectResponse {
  common.Project project = 1;
  repeated common.Permission permissions = 2;
}

message GetApiKeyRequest {
  string organization_id = 2;
}

message DeleteApiKeyRequest {
  string api_key_id = 2;
}

message RevealApiKeyRequest {
  string organization_id = 1;
  string api_key_id = 2;
}

message RevealApiKeyResponse {
  string key = 1;
}

message GetApiKeyResponse {
  repeated common.ApiKey api_keys = 2;
}

message GenerateApiKeyRequest {
  //  string project_id = 1;
  string name = 2;
  repeated string scopes = 3;
  string source = 4;
  string organization_id = 5;
  google.protobuf.Timestamp expires_at = 6;
}

message GenerateApiKeyResponse {
  common.ApiKey api_key = 1;
  string key = 2;
  string username = 3;
}

message ProjectOwnerAndSlug {
  // username or organization name
  string owner_name = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  // project slug
  string slug = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
}

message CheckOwnerRequest {
  string owner_name = 1;
}

message CheckResponse {
  bool is_taken = 1;
  bool is_valid = 2;
}

message GetProjectListRequest {
  string user_id = 1;
  string organization_id = 2;
}

message GetProjectListResponse {
  repeated common.Project projects = 1;
  repeated common.Project shared_projects = 2;
  repeated common.Project org_projects = 3;
}

message ProjectMemberRequest {
  string project_id = 1;
  string username_or_email = 2;
}

message ProjectMemberResponse {
  repeated common.Project.ProjectMember members = 1;
}

message ExportDashboardRequest {
  string dashboard_id = 1;
}

message ExportDashboardResponse {
  google.protobuf.Struct dashboard_json = 1;
}

message ImportDashboardRequest {
  // The id of the target dashboard to import into.
  string dashboard_id = 1 [(google.api.field_behavior) = REQUIRED];
  // The json data of a previously exported dashboard.
  google.protobuf.Struct dashboard_json = 2 [(google.api.field_behavior) = REQUIRED];
  // Override the layout of target dashboard.
  bool overrideLayouts = 3;
}

message ImportDashboardResponse {
  Dashboard dashboard = 1;
}

message Snapshot{
  string id = 1;
  string name = 2;
  string project_id = 3;
  string project_slug = 4;
  string owner_id = 5;
  string owner_name = 6;
  Chart chart = 7;
  google.protobuf.Struct data = 8;
  google.protobuf.Timestamp start_time = 9;
  google.protobuf.Timestamp end_time = 10;
  google.protobuf.Timestamp created_at = 11;
}

message SnapshotRequest {
  string snapshot_id = 1;
  Snapshot snapshot = 2;
}

message SnapshotResponse {
  Snapshot snapshot = 1;
}

message GetOrganizationRequest {
  string org_id_or_name = 1;
}

message GetOrganizationResponse {
  repeated common.Organization organizations = 1;
}

message OrganizationMemberRequest {
  string org_id_or_name = 1;
  string username_or_email = 2;
  common.OrganizationRole role = 3;
}

message DashboardSharing {
  string id = 1;
  string dashboard_id = 2;
  bool is_public = 3;
  repeated string viewers = 4;
}

message InvitationRequest {
  string id = 1;
  string to_email = 2;
  string to_org_name = 3;
  common.OrganizationRole to_org_role = 4;
  string to_project = 5;
}

message Invitation {
  string id = 1;
  string to_email = 2;
  common.UserInfo from = 3;
  common.UserInfo to = 4;
  common.Organization organization = 5;
  common.OrganizationRole org_role = 6;
  google.protobuf.Timestamp created_at = 7;
  common.Project project = 8;
}

message SignUpRequest {
  common.User user = 1;
  string invitation = 2;
}

message GetDashboardSharingRequest {
  string sharing_id = 1;
}

message GetDashboardSharingResponse {
  Dashboard dashboard = 1;
  common.Project project = 2;
}

message ProjectViewResponse {
  string project_id = 1;
  repeated common.ProjectView views = 2;
}

message ImportProjectRequest {
  string owner_name = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  string slug = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
  string name = 3;
  ProjectOwnerAndSlug import_project = 4;
  repeated ProjectOwnerAndSlug import_projects = 5;
}

message UnImportProjectRequest {
  string owner_name = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  string slug = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
  string unimport_owner = 3;
  string unimport_slug = 4;
}

message ImportProjectResponse {
  repeated common.ImportedProject imports = 1;
}

message SearchProjectRequest {
  string q = 1;
}

message SearchProjectResponse {
  repeated common.ProjectInfo projects = 1;
}

message ProjectsResponse {
  repeated common.Project projects = 1;
}

message ProjectsInfoResponse {
  repeated common.ProjectInfo projects = 1;
}

message TagsResponse {
  repeated string tags = 1;
  repeated int32 counts = 2;
}

message ProjectVariableKey {
  string project_id = 1;
  string key = 2;
}

service WebService {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_tag) = {
    name: "Web"
  };

  rpc CheckOwner(CheckOwnerRequest) returns (CheckResponse){
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (google.api.http) = {
      get: "/api/v1/users/{owner_name}/availability"
      additional_bindings {
        get: "/api/v1/organizations/{owner_name}/availability"
      }
    };
  }
  rpc CreateUser(SignUpRequest) returns (common.User) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/users"
      body: "*"
    };
  }

  rpc UpdateUser(common.User) returns (common.User) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      put: "/api/v1/users"
      body: "*"
    };
  }

  // Get user details by id
  rpc GetUser(GetUserRequest) returns (common.User) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/users"
      additional_bindings {
        get: "/api/v1/users/{user_id}"
      }
    };
  }
  // Get user info by id
  rpc GetUserInfo(GetUserInfoRequest) returns (common.UserInfo) {
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/users/info"
      additional_bindings {
        get: "/api/v1/users/info/{user_id}"
      }
    };
  }
  rpc SaveOrganization(common.Organization) returns (common.Organization) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/organizations"
      body: "*"
    };
  }

  rpc GetOrganization(GetOrganizationRequest) returns (GetOrganizationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/organizations"
      additional_bindings {
        get: "/api/v1/organizations/{org_id_or_name}"
      }
    };
  }

  rpc DeleteOrganization(GetOrganizationRequest) returns (common.Organization) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/organizations/{org_id_or_name}"
    };
  }

  rpc AddOrganizationMember(OrganizationMemberRequest) returns (common.Organization) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/organizations/{org_id_or_name}/members"
      body: "*"
    };
  }

  rpc RemoveOrganizationMember(OrganizationMemberRequest) returns (common.Organization) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/organizations/{org_id_or_name}/members/{username_or_email}"
    };
  }

  rpc UpdateOrganizationMember(OrganizationMemberRequest) returns (common.Organization) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/organizations/{org_id_or_name}/members/{username_or_email}"
      body: "*"
    };
  }

  rpc SaveDashboard(Dashboard) returns (Dashboard) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/dashboards"
      body: "*"
    };
  };

  rpc ListTags(google.protobuf.Empty) returns (TagsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/dashboards_tags"
    };
  };

  rpc StarDashboard(GetDashboardRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/dashboards/{dashboard_id}/star"
    };
  };
  rpc UnStarDashboard(GetDashboardRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/dashboards/{dashboard_id}/star"
    };
  };

  // List all dashboards in a project
  rpc ListDashboards(GetDashboardRequest) returns (GetDashboardResponse){
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/dashboards"
      additional_bindings{
        get: "/api/v1/projects/{owner_name}/{slug}/dashboards"
      }
    };
  };

  // List external dashboards in a project
  rpc ListExternalDashboards(ListExternalDashboardsRequest) returns (ListExternalDashboardsResponse){
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/external_dashboards"
      body: "*"
      additional_bindings{
        get: "/api/v1/external_dashboards"
      }
    };
  };

  // Get an external dashboard by id
  rpc GetExternalDashboard(GetExternalDashboardRequest) returns (GetExternalDashboardResponse){
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/external_dashboard"
    };
  };

  // Get a dashboard by id
  rpc GetDashboard(GetDashboardRequest) returns (GetDashboardResponse){
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/dashboards/{dashboard_id}"
      additional_bindings {
        get: "/api/v1/projects/{owner_name}/{slug}/dashboards/{dashboard_id}"
      }
    };
  };
  // Delete a dashboard by id
  rpc DeleteDashboard(GetDashboardRequest) returns (Dashboard){
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      delete: "/api/v1/dashboards/{dashboard_id}"
    };
  };
  // Export a dashboard to json
  rpc ExportDashboard(ExportDashboardRequest) returns (ExportDashboardResponse){
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/dashboards/{dashboard_id}/json"
    };
  };
  // Import a dashboard to another dashboard
  rpc ImportDashboard(ImportDashboardRequest) returns (ImportDashboardResponse){
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      post: "/api/v1/dashboards/json"
      body: "*"
    };
  };
  // Get project details
  rpc GetProject(ProjectOwnerAndSlug) returns (GetProjectResponse) {
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/project/{owner_name}/{slug}"
    };
  };

  // Get project details
  rpc GetProjectById(GetProjectByIdRequest) returns (common.ProjectInfo) {
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/project/{project_id}"
    };
  };

  // Get project list
  rpc GetProjectList(GetProjectListRequest) returns (GetProjectListResponse) {
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    option (google.api.http) = {
      get: "/api/v1/projects"
    };
  };

  rpc GetPopulateProjectList(GetProjectListRequest) returns (GetProjectListResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/populate-projects"
    };
  }

  rpc SaveProject(common.Project) returns (common.Project) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/projects"
      body: "*"
    };
  };

  rpc DeleteProject(GetProjectByIdRequest) returns (common.Project) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/projects/{project_id}"
    };
  };

  rpc CloneProject(CloneProjectRequest) returns (common.Project) {
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      post: "/api/v1/projects/clone"
      body: "*"
    };
  }

  rpc TransferProjectOwner(TransferProjectRequest) returns (common.Project) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/projects/{project_id}/transfer"
      body: "*"
    };
  }

  rpc AddProjectMember(ProjectMemberRequest) returns (ProjectMemberResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/projects/{project_id}/members"
      body: "*"
    };
  }

  rpc RemoveProjectMember(ProjectMemberRequest) returns (ProjectMemberResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/projects/{project_id}/members/{username_or_email}"
    };
  }

  rpc CheckProjectId(ProjectOwnerAndSlug) returns (CheckResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/projects/{owner_name}/{slug}/availability"
    };
  };

  rpc GetApiKey(GetApiKeyRequest) returns (GetApiKeyResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/keys"
    };
  }

  rpc RevealApiKey(RevealApiKeyRequest) returns (RevealApiKeyResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/keys/{api_key_id}/reveal"
    };
  }

  rpc GenerateApiKey(GenerateApiKeyRequest) returns (GenerateApiKeyResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/keys"
      body: "*"
    };
  }

  rpc DeleteApiKey(DeleteApiKeyRequest) returns (common.ApiKey) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/keys/{api_key_id}"
    };
  }

  rpc SaveSnapshot(SnapshotRequest) returns (SnapshotResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/snapshots/{snapshot_id}"
      body: "*"
    };
  }

  rpc DeleteSnapshot(SnapshotRequest) returns (SnapshotResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/snapshots/{snapshot_id}"
    };
  }

  rpc GetSnapshot(SnapshotRequest) returns (SnapshotResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/snapshots/{snapshot_id}"
    };
  }

  rpc SaveDashboardSharing(DashboardSharing) returns (DashboardSharing) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/dashboards/{dashboard_id}/sharing"
      body: "*"
    };
  }
  rpc RemoveDashboardSharing(DashboardSharing) returns (DashboardSharing) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/dashboards/{dashboard_id}/sharing"
    };
  }

  rpc SaveInvitation(InvitationRequest) returns (Invitation) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/invitations"
      body: "*"
    };
  }

  rpc GetDashboardSharing(GetDashboardSharingRequest) returns (GetDashboardSharingResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/sharing_dashboard/{sharing_id}"
    };
  }

  // Project view
  rpc AddProjectView(common.ProjectView) returns (ProjectViewResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/project_view/{project_id}"
      body: "*"
    };
  };

  rpc RemoveProjectView(common.ProjectView) returns (ProjectViewResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/project_view/{project_id}/{id}"
    };
  };

  rpc UpdateProjectView(common.ProjectView) returns (ProjectViewResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/project_view/{project_id}/{id}"
      body: "*"
    };
  };

  rpc SearchProject(SearchProjectRequest) returns (SearchProjectResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/projects/search"
    };
  };

  rpc ImportProject(ImportProjectRequest) returns (ImportProjectResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/project/{owner_name}/{slug}/importprojects"
      body: "*"
    };
  };
  rpc UnImportProject(UnImportProjectRequest) returns (ImportProjectResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/project/{owner_name}/{slug}/unimportprojects/{unimport_owner}/{unimport_slug}"
    };
  };
  rpc GetImportedProject(ProjectOwnerAndSlug) returns (ImportProjectResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/project/{owner_name}/{slug}/importprojects"
    };
  };

  rpc GetStarredProjects(google.protobuf.Empty) returns (ProjectsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/starred_projects"
    };
  };
  rpc StarProject(GetProjectByIdRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/starred_projects/{project_id}"
    };
  };

  rpc UnstarProject(GetProjectByIdRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/starred_projects/{project_id}"
    };
  };

  rpc GetViewedProjects(google.protobuf.Empty) returns (ProjectsInfoResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/viewed_projects"
    };
  };

  rpc SearchUsersInfo(SearchUsersInfoRequest) returns (SearchUsersInfoResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";


    option (google.api.http) = {
      get: "/api/v1/users/search"
    };
  }

  rpc GetProjectVariables(GetProjectByIdRequest) returns (common.ProjectVariables) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/projects/{project_id}/variables"
    };
  }

  rpc SaveProjectVariables(common.ProjectVariables) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/projects/{project_id}/variables"
      body: "*"
    };
  }

  rpc DeleteProjectVariables(ProjectVariableKey) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/projects/{project_id}/variables/{key}"
    };
  }

  // List all notifications, both read and unread.
  rpc GetNotifications(NotificationRequest) returns (NotificationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/notifications"
    };
  }

  // Mark notifications as read.
  rpc ReadNotification(NotificationReadRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      put: "/api/v1/notifications/read"
      body: "*"
    };
  }

  // List all unread notifications.
  rpc UnreadNotification(google.protobuf.Empty) returns (UnreadNotificationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/notifications/unread"
    };
  }

  rpc CreateCoderWorkspace(CreateCoderWorkspaceRequest) returns (CoderWorkspace) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/coder_workspace"
      body: "*"
    };
  }

  rpc DeleteCoderWorkspace(DeleteCoderWorkspaceRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/coder_workspace/{workspace_id}"
    };
  }

  rpc GetCoderWorkspace(CreateCoderWorkspaceRequest) returns (GetCoderWorkspaceResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/coder_workspace"
    };
  }

  rpc CreateCoderWorkspaceBuild(CreateCoderWorkspaceBuildRequest) returns (CoderWorkspace) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/coder_workspace/build"
      body: "*"
    };
  }

  rpc SaveCommunityProject(SaveCommunityProjectRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/community_project"
      body: "*"
    };
  }

  rpc DeleteCommunityProject(SaveCommunityProjectRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/community_project/{project_id}"
    };
  }

  rpc CheckCommunityProjectAlias(SaveCommunityProjectRequest) returns (CheckResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/community_project/{dash_alias}/availability"
    };
  }
}

message SaveCommunityProjectRequest {
  string project_id = 1;
  string dash_alias = 2;
  optional bool curated = 3;
}

message NotificationResponse {
  repeated common.Notification notifications = 1;
}

message NotificationReadRequest {
  // A list of notification ids
  repeated string ids = 1;
}
message NotificationRequest {
  // The max number of notifications to return
  int32 limit = 2;
  // The starting point for pagination
  int32 offset = 3;
}
message UnreadNotificationResponse {
  // The max number of notifications to return
  int32 count = 1;
}

message CloneProjectRequest {
  oneof target_project {
    string project_id = 1;
    common.Project project = 2;
  }
  string from_project_id = 3;
}

message CoderWorkspace {
  string id = 1;
  string user_id = 2;
  string project_id = 3;
  string workspace_name = 4;
  string workspace_id = 5;
  string workspace_status = 6;
  string processor_id = 7;
  string build_created_at = 8;
  string build_deadline = 9;
}

message CoderWorkspaceUser {
  string id = 1;
  string user_id = 2;
  string coder_user_name = 3;
  string coder_user_id = 4;
  string api_key_id = 5;
  string api_key_secret = 6;
}

message CreateCoderWorkspaceRequest {
  string project_id = 1;
  string processor_id = 2;
  string api_host = 3;
}

message DeleteCoderWorkspaceRequest {
  string workspace_id = 1;
}

message GetCoderWorkspaceResponse {
  CoderWorkspace workspace = 1;
  string coder_user_name = 2;
}

message CreateCoderWorkspaceBuildRequest {
  string workspace_id = 1;
  string transition = 2;
}

message TransferProjectRequest {
  string project_id = 1;
  string to_owner_name = 2;
}

