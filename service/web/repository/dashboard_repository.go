package repository

import (
	"errors"
	"fmt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"sentioxyz/sentio/service/web/models"
)

func (r *Repository) SaveDashboard(dashboard *models.Dashboard) error {
	return r.DB.Transaction(func(tx *gorm.DB) error {
		// Delete the panels that are no longer in dashboard
		if dashboard.ID != "" {
			var panelIds []string
			for _, panel := range dashboard.Panels {
				panelIds = append(panelIds, panel.ID)
			}
			stmt := tx.Where(&models.Panel{DashboardID: dashboard.ID}).Not(panelIds)
			err := stmt.Delete(&models.Panel{}).Error
			if err != nil {
				return err
			}
			
			// Load existing panels to preserve CreatorID and UpdaterID
			var existingPanels []*models.Panel
			err = tx.Where("dashboard_id = ?", dashboard.ID).Find(&existingPanels).Error
			if err != nil {
				return err
			}
			
			// Create a map for quick lookup
			existingPanelsMap := make(map[string]*models.Panel)
			for _, panel := range existingPanels {
				existingPanelsMap[panel.ID] = panel
			}
			
			// Preserve CreatorID and UpdaterID for existing panels
			for _, panel := range dashboard.Panels {
				if existingPanel, exists := existingPanelsMap[panel.ID]; exists {
					// Only set CreatorID/UpdaterID if they are nil in the new panel
					if panel.CreatorID == nil {
						panel.CreatorID = existingPanel.CreatorID
					}
					if panel.UpdaterID == nil {
						panel.UpdaterID = existingPanel.UpdaterID
					}
				}
			}
		} else if len(dashboard.URL) > 0 {
			// check dashboard url is unique
			var count int64
			err := tx.Model(&models.Dashboard{}).Where("url = ?", dashboard.URL).Count(&count).Error
			if err != nil {
				return err
			}
			if count > 0 {
				return status.Errorf(codes.AlreadyExists, "dashboard url %s already exists", dashboard.URL)
			}
		}
		
		err := tx.Session(&gorm.Session{FullSaveAssociations: true}).Clauses(clause.OnConflict{
			UpdateAll: true,
		}).Create(&dashboard).Error
		if err != nil {
			return err
		}

		// unset other default dashboard of this project if this dashboard is default
		if dashboard.IsDefault {
			err = tx.Model(&models.Dashboard{}).Where("project_id =? and id != ?", dashboard.ProjectID, dashboard.ID).
				Update("is_default", false).Error
			if err != nil {
				return err
			}
		}

		return err
	})
}

func (r *Repository) GetDashboardByID(id string) (*models.Dashboard, error) {
	d := &models.Dashboard{ID: id}
	result := r.DB.Preload("Panels.Creator").Preload("Panels.Updater").Preload("Panels").Preload("Project").Preload("Share").Preload("Tags").First(&d)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return d, result.Error
}

func (r *Repository) GetDashboardByURL(URL string) (*models.Dashboard, error) {
	d := &models.Dashboard{}
	result := r.DB.Preload("Panels.Creator").Preload("Panels.Updater").Preload("Panels").Preload("Project").Preload("Share").Preload("Tags").
		Where("url = ?", URL).First(&d)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return d, result.Error
}

func (r *Repository) GetInternalDashboardsByProjectID(projectID string) ([]*models.Dashboard, error) {
	var dashboards []*models.Dashboard
	err := r.DB.Preload("Project").Where(
		"(project_id = ?) and (visibility = 0)", projectID).Find(&dashboards).Error
	return dashboards, err
}

func (r *Repository) ListExternalDashboardsByProjectID(projectID string, userID string, orderByStarredTimeRange int64, offset int, limit int, isAdmin bool) ([]*models.Dashboard, error) {
	var dashboards []*models.Dashboard
	var err error
	if orderByStarredTimeRange >= 0 {
		subQuery := r.DB.Model(&models.StarredDashboard{}).
			Select("count(*) as star_count, dashboard_id").
			Group("dashboard_id")

		if orderByStarredTimeRange > 0 {
			subQuery = subQuery.Where(fmt.Sprintf("created_at >= (now() - interval '%d seconds')", orderByStarredTimeRange))
		}

		q := r.DB.Preload("Tags").Preload("Project")

		if isAdmin {
			q = q.Where(
				"(project_id = ?) and (visibility != ?)",
				projectID, models.VisibilityInternal)
		} else {
			q = q.Where(
				"(project_id = ?) and ((visibility = ?) or (owner_id =? and visibility = ?))",
				projectID, models.VisibilityPublic, userID, models.VisibilityPrivate)
		}
		err = q.Where(
			"(project_id = ?) and ((visibility = ?) or (owner_id =? and visibility = ?) or ?)",
			projectID, models.VisibilityPublic, userID, models.VisibilityPrivate, isAdmin).
			Joins("left join (?) as s on s.dashboard_id = dashboards.id", subQuery).
			Order("s.star_count desc nulls last").
			Offset(offset).Limit(limit).
			Find(&dashboards).Error
	} else {
		q := r.DB.Preload("Tags").Preload("Project")
		if isAdmin {
			q = q.Where(
				"(project_id = ?) and (visibility != ?)",
				projectID, models.VisibilityInternal)
		} else {
			q = q.Where(
				"(project_id = ?) and ((visibility = ?) or (owner_id =? and visibility = ?))",
				projectID, models.VisibilityPublic, userID, models.VisibilityPrivate)
		}

		err = q.Order("updated_at desc").
			Offset(offset).Limit(limit).
			Find(&dashboards).Error
	}

	return dashboards, err
}

func (r *Repository) GetUserStarredForDashboards(userID string, dashboardIDs []string) (map[string]bool, error) {
	var starredDashboards []*models.StarredDashboard
	err := r.DB.Where("user_id = ? and dashboard_id in ?", userID, dashboardIDs).Find(&starredDashboards).Error
	if err != nil {
		return nil, err
	}
	var ret = make(map[string]bool)
	for _, starredDashboard := range starredDashboards {
		ret[starredDashboard.DashboardID] = true
	}
	return ret, nil
}

func (r *Repository) GetStarCountForDashboards(dashboardIDs []string, starredTimeRange int64) (map[string]int32, error) {
	query := r.DB.Model(&models.StarredDashboard{}).
		Select("count(*), dashboard_id ").
		Group("dashboard_id")

	var err error
	if starredTimeRange > 0 {
		query = query.Where(fmt.Sprintf("created_at >= (now() - interval '%d seconds') and dashboard_id in ?", starredTimeRange), dashboardIDs)
	} else {
		query = query.Where("dashboard_id in ?", dashboardIDs)
	}
	rows, err := query.Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var ret = make(map[string]int32)
	for rows.Next() {
		var count int32
		var dashboardID string
		err = rows.Scan(&count, &dashboardID)
		if err != nil {
			return nil, err
		}
		ret[dashboardID] = count
	}
	return ret, nil
}

func (r *Repository) StarDashboard(dashboardID string, userID string) error {
	star := &models.StarredDashboard{
		UserID:      userID,
		DashboardID: dashboardID,
	}
	// check if already starred
	err := r.DB.Where("user_id = ? and dashboard_id = ?", userID, dashboardID).First(star).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = r.DB.Create(star).Error
		}
	}
	return err
}

func (r *Repository) UnStarDashboard(dashboardID string, userID string) error {
	return r.DB.Delete(&models.StarredDashboard{}, "user_id = ? and dashboard_id = ?",
		userID, dashboardID).Error
}

func (r *Repository) ListStarredDashboards(projectID string, userID string, offset int, limit int) ([]*models.Dashboard, error) {
	var dashboards []*models.Dashboard

	subQuery := r.DB.Model(&models.StarredDashboard{}).
		Select("dashboard_id ").
		Where("user_id = ?", userID)

	err := r.DB.Preload("Tags").Preload("Project").Where(
		"project_id = ? and visibility != ?",
		projectID, models.VisibilityInternal).
		Joins("inner join (?) as s on dashboards.id = s.dashboard_id", subQuery).
		Order("updated_at desc").
		Offset(offset).Limit(limit).
		Find(&dashboards).Error
	return dashboards, err
}
func (r *Repository) ListDashboardsByOwner(projectID string, owner string, offset int, limit int) ([]*models.Dashboard, error) {
	var dashboards []*models.Dashboard
	err := r.DB.Preload("Tags").Preload("Project").Where(
		"project_id = ? and owner_id =? and visibility != ?",
		projectID, owner, models.VisibilityInternal).
		Order("updated_at desc").
		Offset(offset).Limit(limit).
		Find(&dashboards).Error
	return dashboards, err
}

func (r *Repository) ListDashboardsByTagsAndName(projectID string, userID string,
	tags []string,
	name string,
	orderByStarredTimeRange int64,
	offset int,
	limit int, isAdmin bool) ([]*models.Dashboard, error) {
	var dashboards []*models.Dashboard
	var dashboardTags []*models.Tag
	byTags := len(tags) > 0
	byName := name != ""

	if byTags {
		err := r.DB.Preload("Dashboards").Where("tag in ?", tags).Find(&dashboardTags).Error
		if err != nil {
			return nil, err
		}
		// no tags found
		if len(dashboardTags) == 0 {
			return nil, nil
		}
	}

	var query *gorm.DB
	if byTags {
		query = r.DB.Model(&dashboardTags).Preload("Tags").Preload("Project")
	} else {
		query = r.DB.Preload("Tags").Preload("Project")
	}
	if isAdmin {
		query = query.Where(
			"(project_id = ?) and (visibility != ?)",
			projectID, models.VisibilityInternal)
	} else {
		query = query.Where(
			"(project_id = ?) and ((visibility = ?) or (owner_id =? and visibility = ?))",
			projectID, models.VisibilityPublic, userID, models.VisibilityPrivate)
	}
	if byName {
		query = query.Where("name ilike ? OR url ilike ?", "%"+name+"%", "%"+name+"%")
	}

	if orderByStarredTimeRange >= 0 {
		subQuery := r.DB.Model(&models.StarredDashboard{}).
			Select("count(*) as star_count, dashboard_id ").
			Group("dashboard_id")

		if orderByStarredTimeRange > 0 {
			subQuery = subQuery.Where(fmt.Sprintf("created_at >= (now() - interval '%d seconds')", orderByStarredTimeRange))
		}

		query = query.
			Joins("left join (?) as s on s.dashboard_id = dashboards.id", subQuery).
			Order("s.star_count desc nulls last").
			Offset(offset).Limit(limit)
	} else {
		query = query.Order("updated_at desc").
			Offset(offset).Limit(limit)
	}
	var err error
	if byTags {
		err = query.Association("Dashboards").Find(&dashboards)
	} else {
		err = query.Find(&dashboards).Error
	}

	return dashboards, err
}

func (r *Repository) SaveDashboardTags(dashboardID string, tags []string) error {
	var dashboardTags []*models.Tag
	for _, tag := range tags {
		var t models.Tag
		r.DB.FirstOrInit(&t, models.Tag{Tag: tag})
		dashboardTags = append(dashboardTags, &t)
	}
	err := r.DB.Model(&models.Dashboard{ID: dashboardID}).Association("Tags").Replace(dashboardTags)
	return err
}

func (r *Repository) CreateDefaultDashboard(projectID string) (*models.Dashboard, error) {
	return r.CreateEmptyDashboard(projectID, "Default", true)
}

func (r *Repository) CreateEmptyDashboard(projectID string, name string, isDefault bool) (*models.Dashboard, error) {
	dashboard := models.Dashboard{
		ProjectID: projectID,
		Name:      name,
		IsDefault: isDefault,
	}
	result := r.DB.Create(&dashboard)
	return &dashboard, result.Error
}

func (r *Repository) DeleteDashboard(tx *gorm.DB, d *models.Dashboard) error {
	err := tx.Unscoped().Delete(&models.Panel{}, "dashboard_id = ?", d.ID).Error
	if err != nil {
		return err
	}
	err = tx.Unscoped().Delete(&models.ShareDashboard{}, "dashboard_id = ?", d.ID).Error
	if err != nil {
		return err
	}
	err = tx.Unscoped().Delete(&models.StarredDashboard{}, "dashboard_id = ?", d.ID).Error
	if err != nil {
		return err
	}
	err = tx.Model(d).Association("Tags").Clear()
	if err != nil {
		return err
	}
	err = tx.Unscoped().Delete(d).Error
	if err != nil {
		return err
	}
	return nil
}
