package repository

import (
	"encoding/json"
	"gorm.io/gorm"
	modelscommon "sentioxyz/sentio/service/common/models"

	gonanoid "github.com/matoous/go-nanoid/v2"
	"gorm.io/datatypes"

	"sentioxyz/sentio/service/web/models"
	"sentioxyz/sentio/service/web/protos"

	"github.com/stretchr/testify/require"
)

func (s *RepoTestSuite) Test_Save_Dashboard_With_Panels() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	project := s.CreateProject(user.Username, "testproject")

	newDash := s.newDashboard(project, user.ID, models.VisibilityInternal)
	dashboardID := newDash.ID
	name := "Test Dashboard"
	description := "Test Dashboard Description"
	layouts := `{"layouts": [{"x":0,"y":0,"w":12,"h":12,"i":"1"}]}`

	dashboard, err := s.repository.GetDashboardByID(dashboardID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), dashboardID, dashboard.ID)
	require.Equal(s.T(), name, dashboard.Name)
	require.Equal(s.T(), description, dashboard.Description)
	require.Equal(s.T(), jsonFormat(layouts), jsonFormat(string(dashboard.Layouts)))
}

func (s *RepoTestSuite) Test_Save_Dashboard_With_Panels_Creator_Updater() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	project := s.CreateProject(user.Username, "testproject")

	newDash := s.newDashboardWithCreatorUpdater(project, user.ID, models.VisibilityInternal)
	dashboardID := newDash.ID

	dashboard, err := s.repository.GetDashboardByID(dashboardID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), dashboardID, dashboard.ID)
	require.Len(s.T(), dashboard.Panels, 1)
	
	// Check that creator and updater are set
	panel := dashboard.Panels[0]
	require.NotNil(s.T(), panel.CreatorID)
	require.Equal(s.T(), user.ID, *panel.CreatorID)
	require.NotNil(s.T(), panel.UpdaterID)
	require.Equal(s.T(), user.ID, *panel.UpdaterID)
	
	// Check that creator and updater are preloaded
	require.NotNil(s.T(), panel.Creator)
	require.Equal(s.T(), user.ID, panel.Creator.ID)
	require.NotNil(s.T(), panel.Updater)
	require.Equal(s.T(), user.ID, panel.Updater.ID)
	
	// Test ToPB() method returns common.UserInfo
	pbPanel := panel.ToPB()
	require.NotNil(s.T(), pbPanel.Creator)
	require.Equal(s.T(), user.ID, pbPanel.Creator.Id)
	require.Equal(s.T(), user.Username, pbPanel.Creator.Username)
	require.Equal(s.T(), user.FirstName, pbPanel.Creator.FirstName)
	require.Equal(s.T(), user.LastName, pbPanel.Creator.LastName)
	
	require.NotNil(s.T(), pbPanel.Updater)
	require.Equal(s.T(), user.ID, pbPanel.Updater.Id)
	require.Equal(s.T(), user.Username, pbPanel.Updater.Username)
	require.Equal(s.T(), user.FirstName, pbPanel.Updater.FirstName)
	require.Equal(s.T(), user.LastName, pbPanel.Updater.LastName)
}

func (s *RepoTestSuite) newDashboard(project *modelscommon.Project, userID string, vis models.DashboardVisibility) *models.Dashboard {
	var (
		panelID     = gonanoid.Must(8)
		dashboardID = gonanoid.Must(12)
		name        = "Test Dashboard"
		description = "Test Dashboard Description"
	)

	layouts := `{"layouts": [{"x":0,"y":0,"w":12,"h":12,"i":"1"}]}`

	dashboard := &models.Dashboard{
		ID:          dashboardID,
		Name:        name,
		ProjectID:   project.ID,
		Description: description,
		Panels: []*models.Panel{
			{
				ID:          panelID,
				DashboardID: dashboardID,
			},
		},
		Layouts:    datatypes.JSON(layouts),
		OwnerID:    &userID,
		Visibility: vis,
	}

	err := s.repository.SaveDashboard(dashboard)
	require.NoError(s.T(), err)
	return dashboard
}

func (s *RepoTestSuite) newDashboardWithCreatorUpdater(project *modelscommon.Project, userID string, vis models.DashboardVisibility) *models.Dashboard {
	var (
		panelID     = gonanoid.Must(8)
		dashboardID = gonanoid.Must(12)
		name        = "Test Dashboard"
		description = "Test Dashboard Description"
	)

	layouts := `{"layouts": [{"x":0,"y":0,"w":12,"h":12,"i":"1"}]}`

	dashboard := &models.Dashboard{
		ID:          dashboardID,
		Name:        name,
		ProjectID:   project.ID,
		Description: description,
		Panels: []*models.Panel{
			{
				ID:          panelID,
				DashboardID: dashboardID,
				CreatorID:   &userID,
				UpdaterID:   &userID,
			},
		},
		Layouts:    datatypes.JSON(layouts),
		OwnerID:    &userID,
		Visibility: vis,
	}

	err := s.repository.SaveDashboard(dashboard)
	require.NoError(s.T(), err)
	return dashboard
}

func (s *RepoTestSuite) Test_DashboardList() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	project := s.CreateProject(user.Username, "testproject")

	dashboard1 := s.newDashboard(project, user.ID, models.VisibilityPublic)
	dashboard2 := s.newDashboard(project, user.ID, models.VisibilityPublic)
	err := s.repository.StarDashboard(dashboard2.ID, user.ID)
	require.NoError(s.T(), err)

	list, err := s.repository.ListExternalDashboardsByProjectID(project.ID, user.ID, 1000, 0, 10, false)
	require.NoError(s.T(), err)
	tags3ListIDs := []string{list[0].ID, list[1].ID}
	require.Contains(s.T(), tags3ListIDs, dashboard1.ID)
	require.Contains(s.T(), tags3ListIDs, dashboard2.ID)

	// order by starred
	require.Equal(s.T(), list[0].ID, dashboard2.ID)
}

func (s *RepoTestSuite) Test_DashboardListByAdmin() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	user2 := s.CreateUser("<EMAIL>", "testuid2", "testsub2")
	project := s.CreateProject(user.Username, "testproject")

	_ = s.newDashboard(project, user.ID, models.VisibilityPrivate)
	dashboard2 := s.newDashboard(project, user2.ID, models.VisibilityPrivate)
	err := s.repository.StarDashboard(dashboard2.ID, user.ID)
	require.NoError(s.T(), err)

	list, err := s.repository.ListExternalDashboardsByProjectID(project.ID, user2.ID, 1000, 0, 10, false)
	require.NoError(s.T(), err)
	// user2 can see dashboard2
	require.Equal(s.T(), 1, len(list))
	require.Equal(s.T(), list[0].ID, dashboard2.ID)

	// user1 is project admin, can see all dashboards
	list, err = s.repository.ListExternalDashboardsByProjectID(project.ID, user.ID, 1000, 0, 10, true)
	require.NoError(s.T(), err)
	require.Equal(s.T(), 2, len(list))
}

func (s *RepoTestSuite) Test_DashboardListByTag() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	project := s.CreateProject(user.Username, "testproject")

	dashboard1 := s.newDashboard(project, user.ID, models.VisibilityPublic)
	dashboard2 := s.newDashboard(project, user.ID, models.VisibilityPublic)
	err := s.repository.SaveDashboardTags(dashboard1.ID, []string{"tag1", "tag3"})
	require.NoError(s.T(), err)
	err = s.repository.SaveDashboardTags(dashboard2.ID, []string{"tag2", "tag3"})
	require.NoError(s.T(), err)
	tag1List, err := s.repository.ListDashboardsByTagsAndName(project.ID, user.ID, []string{"tag1"}, "", 0, 0, 10, false)
	require.NoError(s.T(), err)
	require.Equal(s.T(), tag1List[0].ID, dashboard1.ID)
	tag2List, err := s.repository.ListDashboardsByTagsAndName(project.ID, user.ID, []string{"tag2"}, "", 0, 0, 10, false)
	require.NoError(s.T(), err)
	require.Equal(s.T(), tag2List[0].ID, dashboard2.ID)
	tag3List, err := s.repository.ListDashboardsByTagsAndName(project.ID, user.ID, []string{"tag3"}, "", 100, 0, 10, false)
	require.NoError(s.T(), err)
	tags3ListIDs := []string{tag3List[0].ID, tag3List[1].ID}
	require.Contains(s.T(), tags3ListIDs, dashboard1.ID)
	require.Contains(s.T(), tags3ListIDs, dashboard2.ID)
}

func (s *RepoTestSuite) Test_DashboardListByStar() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	project := s.CreateProject(user.Username, "testproject")

	dashboard1 := s.newDashboard(project, user.ID, models.VisibilityPublic)
	err := s.repository.StarDashboard(dashboard1.ID, user.ID)
	require.NoError(s.T(), err)
	list, err := s.repository.ListStarredDashboards(project.ID, user.ID, 0, 10)
	require.NoError(s.T(), err)
	require.Equal(s.T(), list[0].ID, dashboard1.ID)

	err = s.repository.UnStarDashboard(dashboard1.ID, user.ID)
	require.NoError(s.T(), err)
	list, err = s.repository.ListStarredDashboards(project.ID, user.ID, 0, 10)
	require.NoError(s.T(), err)
	require.Equal(s.T(), len(list), 0)
}

func (s *RepoTestSuite) Test_DeleteDashboard() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	project := s.CreateProject(user.Username, "testproject")

	dashboard1 := s.newDashboard(project, user.ID, models.VisibilityPublic)
	err := s.repository.StarDashboard(dashboard1.ID, user.ID)
	require.NoError(s.T(), err)
	err = s.repository.SaveDashboardTags(dashboard1.ID, []string{"tag1", "tag3"})
	require.NoError(s.T(), err)
	err = s.repository.DB.Transaction(func(tx *gorm.DB) error {
		return s.repository.DeleteDashboard(tx, dashboard1)
	})
	require.NoError(s.T(), err)
}

func (s *RepoTestSuite) Test_Save_Dashboard_With_Specific_Panels_Creator_Updater() {
	user := s.CreateUser("<EMAIL>", "testuid", "testsub")
	project := s.CreateProject(user.Username, "testproject")

	// Create a dashboard with multiple panels
	dashboard := &models.Dashboard{
		ID:          "test-dashboard",
		Name:        "Test Dashboard",
		ProjectID:   project.ID,
		Visibility:  models.VisibilityInternal,
		Panels: []*models.Panel{
			{
				ID:          "panel1",
				Name:        "Panel 1",
				DashboardID: "test-dashboard",
			},
			{
				ID:          "panel2", 
				Name:        "Panel 2",
				DashboardID: "test-dashboard",
			},
			{
				ID:          "panel3",
				Name:        "Panel 3", 
				DashboardID: "test-dashboard",
			},
		},
	}

	// Save dashboard initially
	err := s.repository.SaveDashboard(dashboard)
	require.NoError(s.T(), err)

	// Create protobuf dashboard with specific create_panels and edit_panels
	pbDashboard := &protos.Dashboard{
		Id:          dashboard.ID,
		Name:        dashboard.Name,
		ProjectId:   project.ID,
		CreatePanels: []string{"panel1"}, // Only panel1 should get creator
		EditPanels:   []string{"panel2"}, // Only panel2 should get updater
		Panels: map[string]*protos.Panel{
			"panel1": {
				Id:          "panel1",
				Name:        "Panel 1 Updated",
				DashboardId: dashboard.ID,
			},
			"panel2": {
				Id:          "panel2",
				Name:        "Panel 2 Updated", 
				DashboardId: dashboard.ID,
			},
			"panel3": {
				Id:          "panel3",
				Name:        "Panel 3 Updated",
				DashboardId: dashboard.ID,
			},
		},
	}

	// Convert to model and save
	d := &models.Dashboard{}
	d.FromPB(pbDashboard)
	d.Project = project

	// Mock the service logic for setting creator/updater
	createPanelsSet := make(map[string]bool)
	editPanelsSet := make(map[string]bool)
	
	for _, panelID := range pbDashboard.CreatePanels {
		createPanelsSet[panelID] = true
	}
	for _, panelID := range pbDashboard.EditPanels {
		editPanelsSet[panelID] = true
	}

	// Set creator and updater only for specified panels
	for _, panel := range d.Panels {
		if createPanelsSet[panel.ID] {
			panel.CreatorID = &user.ID
		}
		if editPanelsSet[panel.ID] {
			panel.UpdaterID = &user.ID
		}
	}

	err = s.repository.SaveDashboard(d)
	require.NoError(s.T(), err)

	// Retrieve and verify
	savedDashboard, err := s.repository.GetDashboardByID(dashboard.ID)
	require.NoError(s.T(), err)
	require.Len(s.T(), savedDashboard.Panels, 3)

	// Find panels by ID since order might not be preserved
	var panel1, panel2, panel3 *models.Panel
	for _, panel := range savedDashboard.Panels {
		switch panel.ID {
		case "panel1":
			panel1 = panel
		case "panel2":
			panel2 = panel
		case "panel3":
			panel3 = panel
		}
	}

	// Check panel1 - should have creator but no updater
	require.NotNil(s.T(), panel1)
	require.Equal(s.T(), "panel1", panel1.ID)
	require.NotNil(s.T(), panel1.CreatorID)
	require.Equal(s.T(), user.ID, *panel1.CreatorID)
	require.Nil(s.T(), panel1.UpdaterID)

	// Check panel2 - should have updater but no creator  
	require.NotNil(s.T(), panel2)
	require.Equal(s.T(), "panel2", panel2.ID)
	require.Nil(s.T(), panel2.CreatorID)
	require.NotNil(s.T(), panel2.UpdaterID)
	require.Equal(s.T(), user.ID, *panel2.UpdaterID)

	// Check panel3 - should have neither creator nor updater
	require.NotNil(s.T(), panel3)
	require.Equal(s.T(), "panel3", panel3.ID)
	require.Nil(s.T(), panel3.CreatorID)
	require.Nil(s.T(), panel3.UpdaterID)
}

func jsonFormat(jsonString string) string {
	x := map[string]any{}
	err := json.Unmarshal([]byte(jsonString), &x)
	if err != nil {
		panic(err)
	}
	ret, err := json.Marshal(x)
	if err != nil {
		panic(err)
	}
	return string(ret)
}
