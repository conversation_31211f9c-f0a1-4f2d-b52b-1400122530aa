load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "repository",
    srcs = [
        "dashboard_repository.go",
        "db.go",
        "notification_repostiory.go",
        "processor.go",
        "project_respository.go",
        "repository.go",
        "user_repository.go",
    ],
    importpath = "sentioxyz/sentio/service/web/repository",
    visibility = ["//visibility:public"],
    deps = [
        "//service/common/models",
        "//service/common/protos",
        "//service/common/repository",
        "//service/contract/models",
        "//service/processor/models",
        "//service/processor/protos",
        "//service/web/models",
        "@com_github_pkg_errors//:errors",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "repository_test",
    srcs = [
        "dashboard_repository_test.go",
        "db_test.go",
        "project_repository_test.go",
        "repostiory_test.go",
        "user_repository_test.go",
    ],
    embed = [":repository"],
    deps = [
        "//common/log",
        "//service/common/dbtest",
        "//service/common/models",
        "//service/common/repository",
        "//service/contract/models",
        "//service/processor/models",
        "//service/web/models",
        "//service/web/protos",
        "@com_github_fergusstrange_embedded_postgres//:embedded-postgres",
        "@com_github_matoous_go_nanoid_v2//:go-nanoid",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
    ],
)
