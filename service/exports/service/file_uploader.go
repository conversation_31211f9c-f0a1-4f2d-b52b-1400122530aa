package service

import (
	"cloud.google.com/go/storage"
	"context"
	"io"
	"os"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/models"
	"sentioxyz/sentio/service/common/storagesystem"
	"time"
)

type FileUploader struct {
	fileStorageSystem *storagesystem.FileStorageSystem
}

func NewFileUploader(gcsServiceAccount string, gcsBucket string, s3endpoint string) *FileUploader {

	fileStorageSystem := &storagesystem.FileStorageSystem{
		Settings: &storagesystem.StorageSettings{
			S3Endpoint:        s3endpoint,
			GcsServiceAccount: gcsServiceAccount,
			Bucket:            gcsBucket,
			UploadPrefix:      "upload",
			Prefix:            "exports",
		},
	}

	return &FileUploader{
		fileStorageSystem: fileStorageSystem,
	}
}

func (s *FileUploader) UploadFile(ctx context.Context, params *models.ExportSQLParams, uploadPath string) (string, error) {
	gcsClient, err := storage.NewClient(ctx)
	defer gcsClient.Close()
	if err != nil {
		log.Fatale(err)
	}
	if _, err := gcsClient.Bucket(s.gcsBucket).Attrs(ctx); err != nil {
		log.Fatale(err)
	}

	file, err := os.Open(params.Filepath)
	if err != nil {
		return "", nil
	}
	defer file.Close()

	writer := gcsClient.
		Bucket(s.gcsBucket).
		Object(uploadPath).
		NewWriter(ctx)

	log.Infof("Uploading file to GCS: %s", uploadPath)
	_, err = io.Copy(writer, file)
	if err != nil {
		return "", nil
	}

	err = writer.Close()
	if err != nil {
		return "", err
	}

	opts := &storage.SignedURLOptions{
		GoogleAccessID: s.gcsServiceAccount,
		Scheme:         storage.SigningSchemeV4,
		Method:         "GET",
		Expires:        time.Now().Add(72 * time.Hour),
	}
	url, err := gcsClient.Bucket(s.gcsBucket).SignedURL(uploadPath, opts)
	if err != nil {
		return "", err
	}
	err = os.Remove(params.Filepath)
	if err != nil {
		return "", err
	}
	log.Infof("Upload file done, url: %s", url)
	return url, nil
}
