package service

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"net/url"
	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/notification"
	"sentioxyz/sentio/common/period"
	"sentioxyz/sentio/service/billing/coinbase"
	"sentioxyz/sentio/service/billing/models"
	"sentioxyz/sentio/service/billing/protos"
	"sentioxyz/sentio/service/billing/repository"
	rf "sentioxyz/sentio/service/billing/requestfinance"
	"sentioxyz/sentio/service/common/auth"
	modelscommon "sentioxyz/sentio/service/common/models"
	protoscommon "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/common/rpc"
	"sentioxyz/sentio/service/common/util"
	protossvc "sentioxyz/sentio/service/processor/protos"
	usage "sentioxyz/sentio/service/usage/protos"
	models2 "sentioxyz/sentio/service/usage/store/models"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stripe/stripe-go/v75/paymentintent"

	"github.com/stripe/stripe-go/v75/paymentmethod"
	"github.com/stripe/stripe-go/v75/setupintent"

	"github.com/stripe/stripe-go/v75"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"gorm.io/gorm"
)

type BillingService struct {
	protos.UnimplementedBillingServiceServer
	DB                     *gorm.DB
	auth                   auth.AuthManager
	repository             repository.BillingRepository
	usageClient            usage.UsageServiceClient
	stripeEndpointSecret   string
	coinbaseAPIKey         string
	coinBaseEndpointSecret string
	domain                 string
	sendgridAPIKey         string
	processorClient        protossvc.ProcessorServiceClient
	notification           *notification.Client
	requestFinanceAPIKey   string
	receiveAddress         string
}

func NewBillingService(
	conn *gorm.DB,
	authManager auth.AuthManager,
	domain string,
	sendgridAPIKey string,
	usageServerAddress string,
	stripeAPIKey string,
	stripeEndpointSecret string,
	coinbaseAPIKey string,
	coinBaseEndpointSecret string,
	processorServer string,
	requestFinanceAPIKey string,
	receiveAddress string,
) *BillingService {
	usageServer, err := rpc.DialAuto(usageServerAddress)
	if err != nil {
		log.Fatale(err)
	}
	usageClient := usage.NewUsageServiceClient(usageServer)
	stripe.Key = stripeAPIKey

	processorConn, err := rpc.Dial(processorServer)
	if err != nil {
		log.Fatale(err, "did not connect")
	}
	processorClient := protossvc.NewProcessorServiceClient(processorConn)
	return &BillingService{
		DB:                     conn,
		auth:                   authManager,
		repository:             repository.NewRepository(conn),
		usageClient:            usageClient,
		stripeEndpointSecret:   stripeEndpointSecret,
		coinbaseAPIKey:         coinbaseAPIKey,
		coinBaseEndpointSecret: coinBaseEndpointSecret,
		domain:                 domain,
		sendgridAPIKey:         sendgridAPIKey,
		processorClient:        processorClient,
		notification:           notification.NewClient(conn),
		requestFinanceAPIKey:   requestFinanceAPIKey,
		receiveAddress:         receiveAddress,
	}
}

func (svc *BillingService) GetSubscription(
	ctx context.Context,
	in *protos.GetSubscriptionRequest,
) (*protos.GetSubscriptionResponse, error) {
	account, err := svc.requireAccount(ctx, in.NameOrId)
	if err != nil {
		return nil, err
	}

	var subscriptions []models.Subscription
	err = svc.DB.Preload("Account").
		Preload("Plan").
		Where("account_id = ?", account.ID).
		Find(&subscriptions).Error
	if err != nil {
		return nil, err
	}

	response := &protos.GetSubscriptionResponse{
		Subscriptions: make([]*protos.Subscription, len(subscriptions)),
	}
	for i, subscription := range subscriptions {
		response.Subscriptions[i] = subscription.ToPB()
	}
	return response, nil
}

func (svc *BillingService) requireAccount(ctx context.Context, nameOrID string) (*modelscommon.Account, error) {
	identity, err := svc.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	account, err := svc.repository.FindAccount(nameOrID)
	if err != nil {
		return nil, err
	}
	if account.ID == identity.UserID {
		return account, nil
	}
	if account.OwnerAsOrg != nil {
		// check if user is admin of org
		if ok, _ := svc.auth.CheckOrganizationAccess(ctx, identity, account.OwnerAsOrg.Name, auth.ADMIN); ok {
			return account, nil
		}
	}
	// check if user is sentio admin
	_, err = svc.auth.RequiredAdminLogin(ctx)
	if err != nil {
		return nil, status.Error(codes.PermissionDenied, "no access to this account")
	}

	return account, nil
}

func (svc *BillingService) UpdateSubscription(
	ctx context.Context,
	req *protos.UpdateSubscriptionRequest,
) (*protos.UpdateSubscriptionResponse, error) {
	account, err := svc.requireAccount(ctx, req.AccountId)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	if req.Tier == protoscommon.Tier_FREE {
		// cancel subscription
		subscription, err := svc.repository.GetSubscription(account.ID, models.SubscriptionStatusActive)
		if err != nil {
			return nil, err
		}
		err = svc.DB.Transaction(func(tx *gorm.DB) error {
			subscription.Status = models.SubscriptionStatusEnded
			subscription.EndedAt = &now
			err := svc.DB.Save(&subscription).Error
			if err != nil {
				return err
			}
			err = svc.repository.UpdateTier(tx, account, protoscommon.Tier_name[int32(protoscommon.Tier_FREE)])
			if err != nil {
				return err
			}
			return nil
		})

		_, _ = svc.notification.Record(
			notification.Billing,
			notification.Info,
			"",
			protoscommon.NotificationType_BILLING_SUBSCRIPTION,
			"Your subscription has been cancelled.",
			account.ID,
			account.OwnerType,
			"tier",
			protoscommon.Tier_FREE.String(),
		)

		return &protos.UpdateSubscriptionResponse{}, err
	}

	// select new plan or create new subscription
	plan := models.Plan{
		ID: req.PlanId,
	}
	err = svc.DB.First(&plan).Error
	if err != nil {
		return nil, err
	}
	tier := protoscommon.Tier_name[int32(req.Tier)]
	if plan.TierDefaultPlan == "" || plan.TierDefaultPlan != tier {
		return nil, status.Error(codes.InvalidArgument, "plan is not for this tier")
	}

	// current active sub for this account
	sub, err := svc.repository.GetSubscription(account.ID, models.SubscriptionStatusActive)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	err = svc.DB.Transaction(func(tx *gorm.DB) error {
		// update plan or tier
		if sub != nil && (sub.Tier != tier || sub.PlanID != req.PlanId) {
			sub, err = sub.CancelOldAndCreateNew(tx)
			if err != nil {
				return err
			}
		}

		// update payment period, cancel old and start new
		if sub != nil && req.PaymentPeriod != sub.PaymentPeriodProto() {
			sub, err = sub.CancelOldAndCreateNew(tx)
			if err != nil {
				return err
			}
		}

		// create new
		if sub == nil {
			sub = &models.Subscription{
				AccountID: account.ID,
				StartedAt: &now,
			}
		}

		sub.PlanID = plan.ID
		sub.Tier = tier
		sub.Status = models.SubscriptionStatusActive
		sub.PaymentPeriod = models.PaymentPeriodType(req.PaymentPeriod.String())

		err = svc.DB.Save(&sub).Error
		if err != nil {
			return err
		}

		err = svc.resumeProcessor(ctx, account)
		if err != nil {
			return err
		}

		return svc.repository.UpdateTier(tx, account, tier)
	})

	_, _ = svc.notification.Record(
		notification.Billing,
		notification.Info,
		"",
		protoscommon.NotificationType_BILLING_SUBSCRIPTION,
		fmt.Sprintf("Your subscription has been updated to %s", plan.Name),
		account.ID,
		account.OwnerType,
		"tier",
		tier,
	)

	return &protos.UpdateSubscriptionResponse{
		Subscription: sub.ToPB(),
	}, err
}

func (svc *BillingService) GetInvoice(ctx context.Context, req *protos.GetInvoiceRequest) (*protos.Invoice, error) {
	invoice := models.Invoice{
		ID: req.InvoiceId,
	}
	err := svc.DB.Preload("Account").
		Preload("Subscription").
		Preload("Items").
		Preload("Subscription.Plan").
		Preload("PaymentIntents").
		Preload("Plan").
		First(&invoice).Error
	if err != nil {
		return nil, err
	}
	if req.Hash != "" && invoice.Hash() == req.Hash {
		return invoice.ToPB(), nil
	}
	_, err = svc.requireAccount(ctx, invoice.Account.ID)
	if err != nil {
		return nil, err
	}

	return invoice.ToPB(), nil
}

func (svc *BillingService) GetPaymentIntent(
	ctx context.Context,
	req *protos.PaymentIntentRequest,
) (*protos.PaymentIntentResponse, error) {
	intent, err := svc.repository.FindPaymentIntent(req.Id)

	if err != nil {
		return nil, err
	}
	if req.Hash != "" && intent.Invoice.Hash() == req.Hash {
		return &protos.PaymentIntentResponse{
			PaymentIntent: intent.ToPB(true),
		}, nil
	}
	_, err = svc.requireAccount(ctx, intent.Invoice.Account.ID)
	if err != nil {
		return nil, err
	}

	return &protos.PaymentIntentResponse{
		PaymentIntent: intent.ToPB(true),
	}, nil
}

func (svc *BillingService) ListPlans(
	ctx context.Context,
	req *protos.ListPlansRequest,
) (*protos.ListPlansResponse, error) {
	var plans []models.Plan
	var err error
	if req.AccountId != "" {
		account, err := svc.requireAccount(ctx, req.AccountId)
		if err != nil {
			return nil, err
		}

		subs, err := svc.repository.ListSubscriptions(req.AccountId)
		if err != nil {
			return nil, err
		}
		hadTrial := false
		for _, sub := range subs {
			// any paid sub
			if sub.Plan.TierDefaultPlan != protoscommon.Tier_FREE.String() {
				hadTrial = true
				break
			}
		}
		var name string
		account.GetOwner(svc.DB)
		if account.OwnerAsOrg != nil {
			name = account.OwnerAsOrg.Name
		} else {
			name = account.OwnerAsUser.Username
		}
		q := svc.DB.Where("(is_public OR (owner = ?))", name)
		if hadTrial {
			q.Where("(is_trial is NULL or is_trial = False)")
		}
		err = q.Find(&plans).Error
		if err != nil {
			return nil, err
		}

		// additional plans
		var accountPlans []models.AccountPlan
		err = svc.DB.Preload("Plan").Where("account_id = ?", account.ID).Find(&accountPlans).Error
		if err != nil {
			return nil, err
		}
		for _, ap := range accountPlans {
			plans = append(plans, *ap.Plan)
		}
		plans = lo.Uniq(plans)

	} else {
		if _, err = svc.auth.RequiredAdminLogin(ctx); err != nil {
			return nil, status.Error(403, "Forbidden")
		}
		err = svc.DB.Find(&plans).Error
		if err != nil {
			return nil, err
		}
	}

	response := &protos.ListPlansResponse{
		Plans: make([]*protos.Plan, len(plans)),
	}
	for i, plan := range plans {
		response.Plans[i] = plan.ToPB()
	}
	return response, nil
}

func (svc *BillingService) GetUserUsage(
	ctx context.Context,
	req *protos.GetUsageRequest,
) (*protoscommon.UserUsage, error) {
	accountResp, err := svc.GetAccount(ctx, &protos.GetAccountRequest{NameOrId: req.AccountId})
	if err != nil {
		return nil, err
	}
	account, err := svc.repository.FindAccount(accountResp.Id)
	if err != nil {
		return nil, err
	}
	account.GetOwner(svc.DB)
	response := &protoscommon.UserUsage{
		UsageByProjects: make(map[string]*protoscommon.UserUsage_ProjectUsage),
	}
	if account.OwnerAsOrg != nil {
		response.Tier = protoscommon.Tier(account.OwnerAsOrg.Tier)
	}
	if account.OwnerAsUser != nil {
		response.Tier = protoscommon.Tier(account.OwnerAsUser.Tier)
	}

	//projects and alerts
	db := svc.DB.WithContext(ctx)
	p, a, err := svc.repository.GetProjectsAndAlerts(db, account.ID, req.End)
	if err != nil {
		return nil, err
	}
	response.Projects = int32(len(p))
	response.Alerts = int32(len(a))

	if req.GetStart() != nil && req.GetEnd() != nil {
		req := &usage.StatisticRequest{
			OwnerId:   account.ID,
			OwnerType: account.OwnerType,
			Tags: &usage.QueryTags{
				ProjectId: "-",
			},
			Period: &usage.Period{
				NatualMonth: true,
			},
			StartTime: req.Start,
			EndTime:   req.End,
		}
		log.Debugw("get statistic", "req", req)
		statistic, err := svc.usageClient.Statistic(ctx, req)
		if err != nil {
			return nil, err
		}
		log.Debugw("statistic response", "statistic", statistic)
		for _, s := range statistic.GetStats() {
			projectID := s.GetTags().GetProjectId()
			u := &protoscommon.UserUsage_ProjectUsage{
				Cost:  s.Cost,
				Owner: "?",
				Slug:  projectID,
			}
			if prj, err := svc.repository.GetProjectByIDUnscoped(db, projectID); err == nil && prj != nil {
				u.Owner = prj.GetOwnerName()
				u.Slug = prj.Slug
			}
			response.UsageByProjects[projectID] = u
		}
	}

	return response, nil
}

func (svc *BillingService) GetAccount(
	ctx context.Context,
	req *protos.GetAccountRequest,
) (*protoscommon.Account, error) {
	identity, err := svc.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	account, err := svc.requireAccount(ctx, req.NameOrId)
	// account is not found, try to create one
	if errors.Is(err, gorm.ErrRecordNotFound) {
		user, _ := svc.repository.GetUser(svc.DB, req.NameOrId)
		if user != nil && user.ID == identity.UserID {
			account, err := svc.repository.GetOrCreateAccountForUser(user)
			if err != nil {
				return nil, err
			}
			return account.ToPB(), nil
		}
		org, err := svc.repository.GetOrganization(svc.DB, req.NameOrId)
		if err == nil {
			access, err := svc.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.ADMIN)
			if err != nil {
				return nil, err
			}
			if access {
				account, err := svc.repository.GetOrCreateAccountForOrg(org)
				if err != nil {
					return nil, err
				}
				return account.ToPB(), nil
			}
			return nil, status.Error(403, "Forbidden")
		}
	}
	if err != nil {
		return nil, err
	}
	return account.ToPB(), nil
}

func (svc *BillingService) UpdateAccount(
	ctx context.Context,
	req *protos.UpdateAccountRequest,
) (*protoscommon.Account, error) {
	account, err := svc.requireAccount(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	account.Name = req.Name
	account.Contact = req.Contact
	account.Address = req.Address
	account.PayMethod = req.PaymentMethod.String()
	err = svc.DB.Save(&account).Error
	if err != nil {
		return nil, err
	}
	return account.ToPB(), nil
}

func (svc *BillingService) SetupPayment(
	ctx context.Context,
	req *protos.SetupPaymentRequest,
) (*protos.SetupPaymentResponse, error) {
	account, err := svc.requireAccount(ctx, req.AccountId)
	if err != nil {
		return nil, err
	}
	c, err := svc.repository.GetOrCreateStripeCustomerForAccount(account)
	if err != nil {
		return nil, err
	}
	params := &stripe.SetupIntentParams{
		Customer: stripe.String(c.ID),
		AutomaticPaymentMethods: &stripe.SetupIntentAutomaticPaymentMethodsParams{
			Enabled: stripe.Bool(true),
		},
	}

	intent, err := setupintent.New(params)
	if err != nil {
		return nil, err
	}

	return &protos.SetupPaymentResponse{
		ClientSecret: intent.ClientSecret,
	}, nil
}

func (svc *BillingService) GetPaymentMethods(
	ctx context.Context,
	req *protos.GetPaymentMethodsRequest,
) (*protos.GetPaymentMethodsResponse, error) {
	account, err := svc.requireAccount(ctx, req.AccountId)
	if err != nil {
		return nil, err
	}

	paymentInfo := account.GetPaymentInfo()
	var pm *stripe.PaymentMethod
	if paymentInfo != nil && paymentInfo.Stripe.PaymentMethodID != "" {
		pm, err = paymentmethod.Get(paymentInfo.Stripe.PaymentMethodID, nil)
		if err != nil {
			return nil, err
		}
	} else {
		params := &stripe.PaymentMethodListParams{
			Type: stripe.String("card"),
		}
		if paymentInfo != nil && paymentInfo.Stripe.CustomerID != "" {
			params.Customer = stripe.String(paymentInfo.Stripe.CustomerID)
		} else {
			c, err := svc.repository.GetOrCreateStripeCustomerForAccount(account)
			if err != nil {
				return nil, err
			}
			params.Customer = stripe.String(c.ID)
		}
		i := paymentmethod.List(params)
		for i.Next() {
			pm = i.PaymentMethod()
		}
	}

	if pm != nil && pm.Card != nil {
		name := ""
		if pm.BillingDetails != nil {
			name = pm.BillingDetails.Name
		} else {
			name = pm.Customer.Name
		}

		return &protos.GetPaymentMethodsResponse{
			AccountId:      account.ID,
			CardBrand:      string(pm.Card.Brand),
			CardLast4:      pm.Card.Last4,
			CardExpMonth:   strconv.FormatInt(pm.Card.ExpMonth, 10),
			CardExpYear:    strconv.FormatInt(pm.Card.ExpYear, 10),
			CardHolderName: name,
		}, nil
	}

	return nil, status.Error(codes.NotFound, "Not found")
}

func (svc *BillingService) ListInvoices(
	ctx context.Context,
	req *protos.ListInvoicesRequest,
) (*protos.ListInvoicesResponse, error) {
	var invoices []models.Invoice
	var err error
	if req.AccountId == "" {
		_, err = svc.auth.RequiredAdminLogin(ctx)
		if err != nil {
			return nil, err
		}
		stmt := svc.DB.Preload("PaymentIntents").
			Preload("Account").
			Preload("Items").
			Preload("Charges")

		if req.IncludeAll {
			err = stmt.Find(&invoices).Where("start_date > ? and end_date < ?", req.Start.AsTime(), req.End.AsTime()).Error
		} else {
			err = stmt.Where("status in ? and start_date > ? and end_date < ?",
				[]string{
					protos.InvoiceStatus_SENT.String(),
					protos.InvoiceStatus_PAID.String(),
					protos.InvoiceStatus_FAILED.String(),
					protos.InvoiceStatus_DRAFT.String(),
				}, req.Start.AsTime(), req.End.AsTime()).Find(&invoices).Error
		}
	} else {
		var account *modelscommon.Account
		account, err = svc.requireAccount(ctx, req.AccountId)
		if err != nil {
			return nil, err
		}
		stmt := svc.DB.Preload("PaymentIntents").
			Preload("Items").
			Preload("Charges")
		if req.IncludeAll {
			err = stmt.Where("account_id = ?", account.ID).Find(&invoices).Error
		} else {
			err = stmt.Where("account_id = ? AND status in ?",
				account.ID,
				[]string{
					protos.InvoiceStatus_SENT.String(),
					protos.InvoiceStatus_PAID.String(),
					protos.InvoiceStatus_FAILED.String(),
					protos.InvoiceStatus_DRAFT.String(),
				},
			).Find(&invoices).Error
		}
		for _, invoice := range invoices {
			invoice.Account = account
		}
	}

	for _, invoice := range invoices {
		if invoice.Account != nil {
			invoice.Account.GetOwner(svc.DB)
		}
	}

	if err != nil {
		return nil, err
	}
	response := &protos.ListInvoicesResponse{
		Invoices: make([]*protos.Invoice, len(invoices)),
	}
	for i, invoice := range invoices {
		response.Invoices[i] = invoice.ToPB()
	}
	return response, nil
}

func (svc *BillingService) resumeProcessor(ctx context.Context, account *modelscommon.Account) error {
	_, opErr := svc.processorClient.ResumeWaitingProcessorsInternal(
		ctx,
		&protossvc.ResumeWaitingProcessorsRequest{
			OwnerId:   account.ID,
			OwnerType: account.OwnerType,
		},
	)
	if opErr != nil {
		log.Errore(opErr)
	}
	return nil
}

func (svc *BillingService) GenPaymentIntent(
	ctx context.Context,
	in *protos.GenPaymentIntentRequest,
) (*protos.PaymentIntentResponse, error) {
	invoice := models.Invoice{
		ID: in.InvoiceId,
	}
	err := svc.DB.Preload("Account").Preload("Items").Preload("PaymentIntents").Preload("Charges").First(&invoice).Error
	if err != nil {
		return nil, err
	}

	if invoice.Status == protos.InvoiceStatus_VOID.String() {
		return nil, status.Error(400, "Cannot pay voided invoice")
	}

	switch in.PaymentMethod {
	case protos.PaymentGateway_STRIPE:
		// Only support USD for now
		amount := invoice.DueAmount.Mul(decimal.NewFromInt(repository.USDSmallestUnit)).IntPart()

		params := &stripe.PaymentIntentParams{
			Description: stripe.String(fmt.Sprintf("Invoice %s", invoice.ID)),
			Amount:      stripe.Int64(amount),
			AutomaticPaymentMethods: &stripe.PaymentIntentAutomaticPaymentMethodsParams{
				Enabled: stripe.Bool(true),
			},
			//PaymentMethodTypes: []*string{stripe.String("card")},
			Currency: stripe.String(string(stripe.CurrencyUSD)),
		}
		if invoice.Account.Contact != "" {
			params.ReceiptEmail = stripe.String(invoice.Account.Contact)
		}
		if in.DirectCharge {
			paymentInfo := invoice.Account.GetPaymentInfo()
			if paymentInfo != nil {
				params.PaymentMethod = stripe.String(paymentInfo.Stripe.PaymentMethodID)
				params.Customer = stripe.String(paymentInfo.Stripe.CustomerID)
				params.Confirm = stripe.Bool(true)
				params.OffSession = stripe.Bool(true)
				params.ReturnURL = stripe.String(
					fmt.Sprintf("%s/billing/checkout?id=%s&hash=%s", svc.domain, url.QueryEscape(invoice.ID), invoice.Hash()),
				)
			}
		}

		stripePi, err := paymentintent.New(params)
		if err != nil {
			return nil, err
		}
		// convert stripePi to json
		data, err := json.Marshal(stripePi)
		if err != nil {
			return nil, err
		}
		pi := models.PaymentIntent{
			ID:             stripePi.ID,
			InvoiceID:      invoice.ID,
			PaymentGateway: models.Stripe,
			Data:           data,
		}
		err = svc.DB.Create(&pi).Error
		if err != nil {
			return nil, err
		}
		return &protos.PaymentIntentResponse{
			PaymentIntent: pi.ToPB(true),
		}, nil

	case protos.PaymentGateway_COIN_BASE:
		// Only support USD for now
		amount := invoice.DueAmount.String()
		req := &coinbase.ChargeRequest{
			Name:        fmt.Sprintf("Invoice %s", invoice.ID),
			Description: fmt.Sprintf("Invoice %s", invoice.ID),
			LocalPrice: struct {
				Amount   string `json:"amount"`
				Currency string `json:"currency"`
			}{
				Amount:   amount,
				Currency: "USD",
			},
			PricingType: "fixed_price",
		}
		client := coinbase.NewClient(svc.coinbaseAPIKey)
		resp, err := client.CreateCharge(ctx, req)
		if err != nil {
			return nil, err
		}
		data, err := json.Marshal(resp.Data)
		if err != nil {
			return nil, err
		}
		pi := models.PaymentIntent{
			ID:             resp.Data.ID,
			InvoiceID:      invoice.ID,
			PaymentGateway: models.Coinbase,
			Data:           data,
		}
		err = svc.DB.Create(&pi).Error
		if err != nil {
			return nil, err
		}
		return &protos.PaymentIntentResponse{
			PaymentIntent: pi.ToPB(true),
		}, nil

	case protos.PaymentGateway_REQUEST_FINANCE:
		requestInvoice := &rf.Invoice{
			Meta: rf.Meta{
				Format:  "rnf_invoice",
				Version: "0.0.3",
			},
			CreationDate:  invoice.CreatedAt,
			InvoiceItems:  nil,
			InvoiceNumber: invoice.ID,
			BuyerInfo: rf.BuyerInfo{
				BusinessName: invoice.Account.Name,
				Email:        invoice.Account.Contact,
			},
			PaymentTerms: rf.PaymentTerms{
				DueDate: invoice.DueDate,
			},
			Tags: []string{invoice.Account.Name},
		}
		for _, s := range strings.Split(svc.receiveAddress, ",") {
			if currency, address, ok := strings.Cut(s, ":"); ok {
				if _, chain, ok := strings.Cut(currency, "-"); ok {
					requestInvoice.PaymentOptions = append(requestInvoice.PaymentOptions, &rf.PaymentOptions{
						Type: "wallet",
						Value: rf.PaymentValue{
							Currencies: []string{currency},
							PaymentInformation: rf.PaymentInformation{
								PaymentAddress: address,
								Chain:          chain,
							}},
					})
				}
			}
		}

		requestInvoice.InvoiceItems = append(requestInvoice.InvoiceItems, rf.InvoiceItem{
			Currency:  "USD",
			Name:      "Due Amount",
			Quantity:  1,
			UnitPrice: fmt.Sprintf("%d", invoice.DueAmount.Mul(decimal.NewFromInt(repository.USDSmallestUnit)).IntPart()),
			Tax: rf.Tax{
				Amount: "0",
				Type:   "fixed",
			},
		})

		rfClient := rf.NewRequestFinanceClient(svc.requestFinanceAPIKey)
		resp, err := rfClient.CreateInvoice(ctx, requestInvoice)
		if err != nil {
			return nil, err
		}
		err = resp.ToResult(requestInvoice)
		if err != nil {
			return nil, err
		}
		resp, err = rfClient.ConvertInvoice(ctx, requestInvoice.ID)
		if err != nil {
			return nil, err
		}

		pi := models.PaymentIntent{
			ID:             requestInvoice.ID,
			InvoiceID:      invoice.ID,
			PaymentGateway: models.RequestFinance,
			Data:           resp.Body,
		}
		err = svc.DB.Create(&pi).Error
		if err != nil {
			return nil, err
		}

		return &protos.PaymentIntentResponse{
			PaymentIntent: pi.ToPB(true),
		}, nil
	}
	return nil, status.Error(400, "Invalid payment method")
}

func (svc *BillingService) SetAccountOverCapLimit(ctx context.Context, req *protos.OverCapLimitRequest) (*protoscommon.Account, error) {
	account, err := svc.requireAccount(ctx, req.AccountId)
	if err != nil {
		return nil, err
	}
	sub, err := svc.repository.GetSubscription(account.ID, models.SubscriptionStatusActive)
	if err != nil {
		return nil, err
	}

	err = svc.DB.Transaction(func(tx *gorm.DB) error {
		err = tx.Where("owner_id = ?", account.OwnerID).Delete(&models2.OwnerLimiter{}).Error
		if err != nil {
			return err
		}
		if req.NoLimit {
			account.UsageOverCapLimit = nil
		} else {
			limit := util.MoneyToDecimal(req.Limit)
			account.UsageOverCapLimit = &limit
			cost := decimal.Zero
			if sub.Plan.UnitPrice.IsPositive() {
				cost = limit.Div(sub.Plan.UnitPrice).Add(decimal.NewFromInt(sub.Plan.UnitCap))
			} else {
				cost = decimal.NewFromInt(sub.Plan.UnitCap)
			}
			limiter := models2.OwnerLimiter{
				ID:            gonanoid.Must(8),
				EffectiveTime: sub.Started(),
				ExpireTime:    sub.Started().AddDate(100, 0, 0),
				OwnerID:       account.OwnerID,
				OwnerType:     account.OwnerType,
				Period:        period.Month.String(),
				Cost:          cost.BigInt().Uint64(),
			}
			if err = tx.Create(&limiter).Error; err != nil {
				return err
			}
		}
		return tx.Save(&account).Error
	})

	return account.ToPB(), err
}
