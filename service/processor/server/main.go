package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"sigs.k8s.io/controller-runtime/pkg/controller"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/concurrency"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/flags"
	"sentioxyz/sentio/common/jsonrpc"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/monitoring"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/driver/entity/cleaner"
	"sentioxyz/sentio/k8s/client"
	"sentioxyz/sentio/k8s/controllers"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/rpc"
	filestorage "sentioxyz/sentio/service/common/storagesystem"
	processorservice "sentioxyz/sentio/service/processor"
	"sentioxyz/sentio/service/processor/protos"
	"sentioxyz/sentio/service/processor/repository"
)

func main() {
	var (
		dbURL = flag.String(
			"database",
			"postgres://postgres:postgres@localhost:5432/postgres",
			"The postgres database address",
		)
		port          = flag.Int("port", 10020, "The grpc port")
		graphNodePort = flag.Int("graph-node-port", 10090, "The graph node service port")
		authIssuerURL = flag.String("auth-issuer-url", "https://sentio-dev.us.auth0.com/", "The auth0 issue url")
		authAudience  = flag.String("auth-audience", "http://localhost:8080/v1", "The auth0 audience")
		// dataPath      = flag.String("data-path", "./data", "The path to the data directory")
		local             = flag.Bool("run-local", true, "If true, skip k8s part")
		noManager         = flag.Bool("no-k8s-manager", false, "If true, skip k8s manager")
		k8sClustersConfig = flag.String("k8s-clusters-file", "", "The config file of multi k8s clusters")
		webhookService    = flag.String("webhook-service", "localhost:10050", "The address of webhook service")
		gcsServiceAccount = flag.String(
			"gcs-service-account",
			"<EMAIL>",
			"Service account for GCS",
		)
		gcsBucket = flag.String(
			"gcs-bucket",
			"sentio-processors",
			"The GCS bucket for processor code",
		)
		gcsEnvPrefix = flag.String(
			"gcs-object-prefix",
			"local",
			"The env prefix of GCS processor objects",
		)
		gcsUploadPrefix = flag.String(
			"gcs-upload-prefix",
			"upload",
			"GCS object prefix for uploaded objects",
		)
		timescaleConfigPath = flag.String(
			"timescale-db-config",
			"common/timescale/timescale_db_local_config.yml",
			"The timescale multi db config, will use default config if not set")
		pprofPort = flag.Int(
			"pprof-port", 6060,
			"If non-zero, start pprof server on the port",
		)
		clickhouseConfigPath = flag.String(
			"clickhouse-config-path", "", "clickhouse config path")
		processorClusterShardingConfig = flag.String(
			"processor-cluster-sharding-config", "",
			"cluster id of new processors")
		logClickHouseDSN = flag.String("log-clickhouse-dsn", "clickhouse://default:password@localhost:9100/default", "address to clickhouse stores log")

		s3endpoint  = flag.String("s3-endpoint", "", "S3 endpoint URL (http://host:port or https://host:port), if not set, will use GCS")
		s3accessKey = flag.String("s3-access-key", "", "S3 access key ID")
		s3secretKey = flag.String("s3-secret-key", "", "S3 secret key")
	)
	flags.ParseAndInitLogFlag()

	monitoring.StartMonitoring()
	defer monitoring.StopMonitoring()

	if pprofPort != nil && *pprofPort > 0 {
		go func() {
			log.Fatale(http.ListenAndServe(fmt.Sprintf(":%d", *pprofPort), nil))
		}()
	}

	// create new gRPC server
	grpcSever := rpc.NewServer(true)
	// register the GreeterServerImpl on the gRPC server
	conn, err := repository.SetupDB(*dbURL)
	if err != nil {
		log.Fatale(err)
	}

	authConfig := auth.AuthConfig{
		IssuerURL: *authIssuerURL,
		Audience:  *authAudience,
	}

	var k8sClient client.MultiClient
	var k8sMgr client.MultiManager
	var driverJobWatcher = make(map[int]*controllers.Reconciler)

	k8sMgr, err = client.NewMultiManagerFromFile(*k8sClustersConfig)
	if err != nil {
		log.Fatale(err)
	}
	for clusterID, mgr := range k8sMgr {
		driverJobWatcher[clusterID] = &controllers.Reconciler{}
		opts := controller.Options{
			SkipNameValidation: utils.WrapPointer(true),
			NeedLeaderElection: utils.WrapPointer(false),
		}
		if err = driverJobWatcher[clusterID].SetupWithManager(mgr, opts, false); err != nil {
			log.Fatale(fmt.Errorf("setup driver job watcher for cluster %d failed: %w", clusterID, err))
		}
	}
	k8sClient = k8sMgr.GetMultiClient()

	if *local || *noManager {
		// skip k8s manager
	} else {
		go func() {
			// start to watching DriverJob resource
			if err = k8sMgr.Start(context.Background()); err != nil {
				log.Errorfe(err, "k8s-manager run failed")
			}
		}()
	}

	ctx := concurrency.NewSignalContext(context.Background())

	storageSetting := filestorage.StorageSettings{
		GcsServiceAccount: *gcsServiceAccount,
		Bucket:            *gcsBucket,
		Prefix:            *gcsEnvPrefix,
		UploadPrefix:      *gcsUploadPrefix,
		S3Endpoint:        *s3endpoint,
		S3AccessKeyID:     *s3accessKey,
		S3SecretKey:       *s3secretKey,
	}

	var clickhouseMultiSharding event.MultiSharding
	if *clickhouseConfigPath != "" {
		clickhouseMultiSharding, err = clickhouse.NewMultiSharding(*clickhouseConfigPath)
		if err != nil {
			log.Fatale(err)
		}
	}
	ch, err := clickhouse.NewConn(*logClickHouseDSN)
	if err != nil {
		log.Fatale(err)
	}

	entityCleaner := cleaner.NewStdCleaner(clickhouseMultiSharding)

	promClients, err := processorservice.NewMultiPromClient(*k8sClustersConfig)
	if err != nil {
		log.Fatalf("Error creating promClient: %v", err)
	}

	processorService := processorservice.NewService(
		conn,
		&authConfig,
		*local,
		*timescaleConfigPath,
		entityCleaner,
		k8sClient,
		driverJobWatcher,
		*webhookService,
		clickhouseMultiSharding,
		*processorClusterShardingConfig,
		promClients,
		ch.GetClickhouseConn(),
		&storageSetting,
	)
	protos.RegisterProcessorServiceServer(grpcSever, processorService)

	if *graphNodePort > 0 {
		graphNodeService := processorservice.NewGraphNodeService(processorService)
		handler := jsonrpc.NewHandler("graph-node", false, nil, nil)
		handler.Register("subgraph", "1.0", graphNodeService)
		h := monitoring.NewWrappedHandler(handler, "subgraph")
		addr := fmt.Sprintf(":%d", *graphNodePort)
		sgCtx, _ := log.FromContext(ctx, "svrName", "subgraph")
		done := make(chan struct{})
		go func() {
			defer close(done)
			sgErr := jsonrpc.ListenAndServe(sgCtx, addr, h)
			log.Warne(sgErr, "subgraph jsonrpc server ended")
		}()
		defer func() {
			<-done
		}()
	}

	mux := rpc.NewServeMux()

	err = mux.HandlePath("GET", "/healthz", rpc.HealthCheck(conn))
	if err != nil {
		log.Fatale(err)
	}

	err = protos.RegisterProcessorServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}

	rpc.BindAndServeWithHTTP(mux, grpcSever, *port, nil)
}
