load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "redislock",
    srcs = [
        "errors.go",
        "mutex.go",
        "rwlock.go",
    ],
    importpath = "sentioxyz/sentio/common/redislock",
    visibility = ["//visibility:public"],
    deps = [
        "//common/errgroup",
        "//common/log",
        "@com_github_redis_go_redis_v9//:go-redis",
    ],
)

go_test(
    name = "redislock_test",
    srcs = [
        "mutex_test.go",
        "redis_test.go",
        "rwlock_test.go",
    ],
    embed = [":redislock"],
    deps = [
        "//common/errgroup",
        "//common/log",
        "@com_github_alicebob_miniredis_v2//:miniredis",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_stretchr_testify//assert",
        "@org_golang_x_exp//slices",
    ],
)
