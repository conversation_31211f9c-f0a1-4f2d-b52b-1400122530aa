[{"fileFormatVersion": 6, "address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "name": "SupraSValueFeed", "friends": [], "structs": {"CoherentCluster": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "data_hash", "type": {"Vector": "U8"}}, {"name": "pair", "type": {"Vector": "U32"}}, {"name": "prices", "type": {"Vector": "U128"}}, {"name": "timestamp", "type": {"Vector": "U128"}}, {"name": "decimals", "type": {"Vector": "U16"}}]}, "DkgState": {"abilities": {"abilities": ["Store", "Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "public_key", "type": {"Vector": "U8"}}]}, "Entry": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "value", "type": "U128"}, {"name": "decimal", "type": "U16"}, {"name": "timestamp", "type": "U128"}, {"name": "round", "type": "U64"}]}, "MinBatch": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "protocol", "type": {"Vector": "U8"}}, {"name": "txn_hashes", "type": {"Vector": {"Vector": "U8"}}}]}, "MinBlock": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "round", "type": {"Vector": "U8"}}, {"name": "timestamp", "type": {"Vector": "U8"}}, {"name": "author", "type": {"Vector": "U8"}}, {"name": "qc_hash", "type": {"Vector": "U8"}}, {"name": "batch_hashes", "type": {"Vector": {"Vector": "U8"}}}]}, "MinTxn": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "cluster_hashes", "type": {"Vector": {"Vector": "U8"}}}, {"name": "sender", "type": {"Vector": "U8"}}, {"name": "protocol", "type": {"Vector": "U8"}}, {"name": "tx_sub_type", "type": "U8"}]}, "OracleHolder": {"abilities": {"abilities": ["Store", "Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "version", "type": "U64"}, {"name": "owner", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "feeds", "type": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": ["U32", {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "Entry", "typeArguments": []}}]}}}]}, "Origin": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Vector": "U8"}}, {"name": "member_index", "type": "U64"}, {"name": "committee_index", "type": "U64"}]}, "OwnerCap": {"abilities": {"abilities": ["Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}]}, "Price": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "pair", "type": "U32"}, {"name": "value", "type": "U128"}, {"name": "decimal", "type": "U16"}, {"name": "timestamp", "type": "U128"}, {"name": "round", "type": "U64"}]}, "SCCProcessedEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "hash", "type": {"Vector": "U8"}}]}, "SignedCoherentCluster": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "cc", "type": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "CoherentCluster", "typeArguments": []}}}, {"name": "qc", "type": {"Vector": "U8"}}, {"name": "round", "type": "U64"}, {"name": "origin", "type": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "Origin", "typeArguments": []}}}]}, "Vote": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "smr_block", "type": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "<PERSON><PERSON><PERSON>", "typeArguments": []}}}, {"name": "round", "type": "U64"}]}}, "exposedFunctions": {"add_public_key": {"visibility": "Private", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "OwnerCap", "typeArguments": []}}}, {"Vector": "U8"}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "extract_price": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "Price", "typeArguments": []}}}], "return": ["U32", "U128", "U16", "U128", "U64"]}, "get_price": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "OracleHolder", "typeArguments": []}}}, "U32"], "return": ["U128", "U16", "U128", "U64"]}, "get_prices": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "OracleHolder", "typeArguments": []}}}, {"Vector": "U32"}], "return": [{"Vector": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "Price", "typeArguments": []}}}]}, "migrate": {"visibility": "Private", "isEntry": true, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "OwnerCap", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "OracleHolder", "typeArguments": []}}}], "return": []}, "process_cluster": {"visibility": "Private", "isEntry": true, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "DkgState", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x5d8fbbf6f908a4af8c6d072669a462d53e03eb3c1d863bd0359dc818c69ea706", "module": "SupraSValueFeed", "name": "OracleHolder", "typeArguments": []}}}, {"Vector": {"Vector": "U8"}}, {"Vector": {"Vector": "U8"}}, {"Vector": {"Vector": "U8"}}, {"Vector": {"Vector": "U8"}}, {"Vector": {"Vector": {"Vector": "U8"}}}, {"Vector": "U64"}, {"Vector": {"Vector": "U8"}}, {"Vector": {"Vector": {"Vector": "U8"}}}, {"Vector": {"Vector": {"Vector": "U8"}}}, {"Vector": {"Vector": "U8"}}, {"Vector": {"Vector": "U8"}}, {"Vector": "U8"}, {"Vector": {"Vector": "U8"}}, {"Vector": {"Vector": "U32"}}, {"Vector": {"Vector": "U128"}}, {"Vector": {"Vector": "U128"}}, {"Vector": {"Vector": "U16"}}, {"Vector": {"Vector": "U8"}}, {"Vector": "U64"}, {"Vector": {"Vector": "U8"}}, {"Vector": "U64"}, {"Vector": "U64"}, {"Vector": "U64"}, {"Vector": "U64"}, {"Vector": "U64"}, {"Vector": {"Vector": "U8"}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}}}]