{"lockFileVersion": 18, "registryFileHashes": {"https://bcr.bazel.build/bazel_registry.json": "8a28e4aff06ee60aed2a8c281907fb8bcbf3b753c91fb5a5c57da3215d5b3497", "https://bcr.bazel.build/modules/abseil-cpp/20210324.2/MODULE.bazel": "7cd0312e064fde87c8d1cd79ba06c876bd23630c83466e9500321be55c96ace2", "https://bcr.bazel.build/modules/abseil-cpp/20211102.0/MODULE.bazel": "70390338f7a5106231d20620712f7cccb659cd0e9d073d1991c038eb9fc57589", "https://bcr.bazel.build/modules/abseil-cpp/20220623.1/MODULE.bazel": "73ae41b6818d423a11fd79d95aedef1258f304448193d4db4ff90e5e7a0f076c", "https://bcr.bazel.build/modules/abseil-cpp/20230125.1/MODULE.bazel": "89047429cb0207707b2dface14ba7f8df85273d484c2572755be4bab7ce9c3a0", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0.bcr.1/MODULE.bazel": "1c8cec495288dccd14fdae6e3f95f772c1c91857047a098fad772034264cc8cb", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0/MODULE.bazel": "d253ae36a8bd9ee3c5955384096ccb6baf16a1b1e93e858370da0a3b94f77c16", "https://bcr.bazel.build/modules/abseil-cpp/20230802.1/MODULE.bazel": "fa92e2eb41a04df73cdabeec37107316f7e5272650f81d6cc096418fe647b915", "https://bcr.bazel.build/modules/abseil-cpp/20240116.0/MODULE.bazel": "98dc378d64c12a4e4741ad3362f87fb737ee6a0886b2d90c3cdbb4d93ea3e0bf", "https://bcr.bazel.build/modules/abseil-cpp/20240116.1/MODULE.bazel": "37bcdb4440fbb61df6a1c296ae01b327f19e9bb521f9b8e26ec854b6f97309ed", "https://bcr.bazel.build/modules/abseil-cpp/20240116.1/source.json": "9be551b8d4e3ef76875c0d744b5d6a504a27e3ae67bc6b28f46415fd2d2957da", "https://bcr.bazel.build/modules/apple_support/1.15.1/MODULE.bazel": "a0556fefca0b1bb2de8567b8827518f94db6a6e7e7d632b4c48dc5f865bc7c85", "https://bcr.bazel.build/modules/apple_support/1.15.1/source.json": "517f2b77430084c541bc9be2db63fdcbb7102938c5f64c17ee60ffda2e5cf07b", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.10.0/MODULE.bazel": "ae12288421b12faeb09bc4cfb3c05945ce66df5c55fca8a2f2f66ae9f21c7acb", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.14.0/MODULE.bazel": "2b31ffcc9bdc8295b2167e07a757dbbc9ac8906e7028e5170a3708cecaac119f", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.14.0/source.json": "0cf1826853b0bef8b5cd19c0610d717500f5521aa2b38b72b2ec302ac5e7526c", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.7.2/MODULE.bazel": "780d1a6522b28f5edb7ea09630748720721dfe27690d65a2d33aa7509de77e07", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.7.7/MODULE.bazel": "491f8681205e31bb57892d67442ce448cda4f472a8e6b3dc062865e29a64f89c", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.9.3/MODULE.bazel": "66baf724dbae7aff4787bf2245cc188d50cb08e07789769730151c0943587c14", "https://bcr.bazel.build/modules/aspect_rules_js/2.0.0/MODULE.bazel": "b45b507574aa60a92796e3e13c195cd5744b3b8aff516a9c0cb5ae6a048161c5", "https://bcr.bazel.build/modules/aspect_rules_js/2.3.3/MODULE.bazel": "7a09f106e5c52fc3594e3d55356e331dec05095bf50de529e0c33c4dbf3914ef", "https://bcr.bazel.build/modules/aspect_rules_js/2.3.3/source.json": "8f254bddb19525f73a1a5fea22c762d18c829e89c5a5095d5ef44ed96a1ec4e9", "https://bcr.bazel.build/modules/aspect_rules_py/1.3.2/MODULE.bazel": "0bd9f9f787a70a725b9829dd17c19e89ea86b1e06f25df505f780d77a96f55a5", "https://bcr.bazel.build/modules/aspect_rules_py/1.3.2/source.json": "cafc6d1570e4503ff93cfd91309cdf1c96f468f3555993bd1654e75528a9bdd3", "https://bcr.bazel.build/modules/aspect_rules_ts/3.5.1/MODULE.bazel": "1bfe5fabcfbd3caa8b47973c418d14dc3f6da14c623f038ed7c71a757774ea63", "https://bcr.bazel.build/modules/aspect_rules_ts/3.5.1/source.json": "9471f6d6320d36cdbb2e879fbe8c58e1d3aac8eea077ba081ff144d9ba8b326f", "https://bcr.bazel.build/modules/bazel_features/1.1.0/MODULE.bazel": "cfd42ff3b815a5f39554d97182657f8c4b9719568eb7fded2b9135f084bf760b", "https://bcr.bazel.build/modules/bazel_features/1.1.1/MODULE.bazel": "27b8c79ef57efe08efccbd9dd6ef70d61b4798320b8d3c134fd571f78963dbcd", "https://bcr.bazel.build/modules/bazel_features/1.10.0/MODULE.bazel": "f75e8807570484a99be90abcd52b5e1f390362c258bcb73106f4544957a48101", "https://bcr.bazel.build/modules/bazel_features/1.11.0/MODULE.bazel": "f9382337dd5a474c3b7d334c2f83e50b6eaedc284253334cf823044a26de03e8", "https://bcr.bazel.build/modules/bazel_features/1.15.0/MODULE.bazel": "d38ff6e517149dc509406aca0db3ad1efdd890a85e049585b7234d04238e2a4d", "https://bcr.bazel.build/modules/bazel_features/1.17.0/MODULE.bazel": "039de32d21b816b47bd42c778e0454217e9c9caac4a3cf8e15c7231ee3ddee4d", "https://bcr.bazel.build/modules/bazel_features/1.18.0/MODULE.bazel": "1be0ae2557ab3a72a57aeb31b29be347bcdc5d2b1eb1e70f39e3851a7e97041a", "https://bcr.bazel.build/modules/bazel_features/1.19.0/MODULE.bazel": "59adcdf28230d220f0067b1f435b8537dd033bfff8db21335ef9217919c7fb58", "https://bcr.bazel.build/modules/bazel_features/1.20.0/MODULE.bazel": "8b85300b9c8594752e0721a37210e34879d23adc219ed9dc8f4104a4a1750920", "https://bcr.bazel.build/modules/bazel_features/1.21.0/MODULE.bazel": "675642261665d8eea09989aa3b8afb5c37627f1be178382c320d1b46afba5e3b", "https://bcr.bazel.build/modules/bazel_features/1.21.0/source.json": "3e8379efaaef53ce35b7b8ba419df829315a880cb0a030e5bb45c96d6d5ecb5f", "https://bcr.bazel.build/modules/bazel_features/1.3.0/MODULE.bazel": "cdcafe83ec318cda34e02948e81d790aab8df7a929cec6f6969f13a489ccecd9", "https://bcr.bazel.build/modules/bazel_features/1.4.1/MODULE.bazel": "e45b6bb2350aff3e442ae1111c555e27eac1d915e77775f6fdc4b351b758b5d7", "https://bcr.bazel.build/modules/bazel_features/1.9.0/MODULE.bazel": "885151d58d90d8d9c811eb75e3288c11f850e1d6b481a8c9f766adee4712358b", "https://bcr.bazel.build/modules/bazel_features/1.9.1/MODULE.bazel": "8f679097876a9b609ad1f60249c49d68bfab783dd9be012faf9d82547b14815a", "https://bcr.bazel.build/modules/bazel_skylib/1.0.3/MODULE.bazel": "bcb0fd896384802d1ad283b4e4eb4d718eebd8cb820b0a2c3a347fb971afd9d8", "https://bcr.bazel.build/modules/bazel_skylib/1.1.1/MODULE.bazel": "1add3e7d93ff2e6998f9e118022c84d163917d912f5afafb3058e3d2f1545b5e", "https://bcr.bazel.build/modules/bazel_skylib/1.2.0/MODULE.bazel": "44fe84260e454ed94ad326352a698422dbe372b21a1ac9f3eab76eb531223686", "https://bcr.bazel.build/modules/bazel_skylib/1.2.1/MODULE.bazel": "f35baf9da0efe45fa3da1696ae906eea3d615ad41e2e3def4aeb4e8bc0ef9a7a", "https://bcr.bazel.build/modules/bazel_skylib/1.3.0/MODULE.bazel": "20228b92868bf5cfc41bda7afc8a8ba2a543201851de39d990ec957b513579c5", "https://bcr.bazel.build/modules/bazel_skylib/1.4.1/MODULE.bazel": "a0dcb779424be33100dcae821e9e27e4f2901d9dfd5333efe5ac6a8d7ab75e1d", "https://bcr.bazel.build/modules/bazel_skylib/1.4.2/MODULE.bazel": "3bd40978e7a1fac911d5989e6b09d8f64921865a45822d8b09e815eaa726a651", "https://bcr.bazel.build/modules/bazel_skylib/1.5.0/MODULE.bazel": "32880f5e2945ce6a03d1fbd588e9198c0a959bb42297b2cfaf1685b7bc32e138", "https://bcr.bazel.build/modules/bazel_skylib/1.6.1/MODULE.bazel": "8fdee2dbaace6c252131c00e1de4b165dc65af02ea278476187765e1a617b917", "https://bcr.bazel.build/modules/bazel_skylib/1.7.0/MODULE.bazel": "0db596f4563de7938de764cc8deeabec291f55e8ec15299718b93c4423e9796d", "https://bcr.bazel.build/modules/bazel_skylib/1.7.1/MODULE.bazel": "3120d80c5861aa616222ec015332e5f8d3171e062e3e804a2a0253e1be26e59b", "https://bcr.bazel.build/modules/bazel_skylib/1.7.1/source.json": "f121b43eeefc7c29efbd51b83d08631e2347297c95aac9764a701f2a6a2bb953", "https://bcr.bazel.build/modules/boringssl/0.0.0-20211025-d4f1ab9/MODULE.bazel": "6ee6353f8b1a701fe2178e1d925034294971350b6d3ac37e67e5a7d463267834", "https://bcr.bazel.build/modules/boringssl/0.0.0-20230215-5c22014/MODULE.bazel": "4b03dc0d04375fa0271174badcd202ed249870c8e895b26664fd7298abea7282", "https://bcr.bazel.build/modules/boringssl/0.0.0-20230215-5c22014/source.json": "f90873cd3d891bb63ece55a527d97366da650f84c79c2109bea29c17629bee20", "https://bcr.bazel.build/modules/buildifier_prebuilt/6.4.0/MODULE.bazel": "37389c6b5a40c59410b4226d3bb54b08637f393d66e2fa57925c6fcf68e64bf4", "https://bcr.bazel.build/modules/buildifier_prebuilt/7.3.1/MODULE.bazel": "537faf0ad9f5892910074b8e43b4c91c96f1d5d86b6ed04bdbe40cf68aa48b68", "https://bcr.bazel.build/modules/buildifier_prebuilt/7.3.1/source.json": "55153a5e6ca9c8a7e266c4b46b951e8a010d25ec6062bc35d5d4f89925796bad", "https://bcr.bazel.build/modules/buildozer/7.1.2/MODULE.bazel": "2e8dd40ede9c454042645fd8d8d0cd1527966aa5c919de86661e62953cd73d84", "https://bcr.bazel.build/modules/buildozer/7.1.2/source.json": "c9028a501d2db85793a6996205c8de120944f50a0d570438fcae0457a5f9d1f8", "https://bcr.bazel.build/modules/c-ares/1.15.0/MODULE.bazel": "ba0a78360fdc83f02f437a9e7df0532ad1fbaa59b722f6e715c11effebaa0166", "https://bcr.bazel.build/modules/c-ares/1.15.0/source.json": "5e3ed991616c5ec4cc09b0893b29a19232de4a1830eb78c567121bfea87453f7", "https://bcr.bazel.build/modules/curl/8.4.0/MODULE.bazel": "0bc250aa1cb69590049383df7a9537c809591fcf876c620f5f097c58fdc9bc10", "https://bcr.bazel.build/modules/curl/8.4.0/source.json": "8b9532397af6a24be4ec118d8637b1f4e3e5a0d4be672c94b2275d675c7f7d6b", "https://bcr.bazel.build/modules/gazelle/0.27.0/MODULE.bazel": "3446abd608295de6d90b4a8a118ed64a9ce11dcb3dda2dc3290a22056bd20996", "https://bcr.bazel.build/modules/gazelle/0.30.0/MODULE.bazel": "f888a1effe338491f35f0e0e85003b47bb9d8295ccba73c37e07702d8d31c65b", "https://bcr.bazel.build/modules/gazelle/0.32.0/MODULE.bazel": "b499f58a5d0d3537f3cf5b76d8ada18242f64ec474d8391247438bf04f58c7b8", "https://bcr.bazel.build/modules/gazelle/0.33.0/MODULE.bazel": "a13a0f279b462b784fb8dd52a4074526c4a2afe70e114c7d09066097a46b3350", "https://bcr.bazel.build/modules/gazelle/0.34.0/MODULE.bazel": "abdd8ce4d70978933209db92e436deb3a8b737859e9354fb5fd11fb5c2004c8a", "https://bcr.bazel.build/modules/gazelle/0.36.0/MODULE.bazel": "e375d5d6e9a6ca59b0cb38b0540bc9a05b6aa926d322f2de268ad267a2ee74c0", "https://bcr.bazel.build/modules/gazelle/0.37.0/MODULE.bazel": "d1327ba0907d0275ed5103bfbbb13518f6c04955b402213319d0d6c0ce9839d4", "https://bcr.bazel.build/modules/gazelle/0.42.0/MODULE.bazel": "fa140a7c019f3a22779ba7c6132ffff9d2d10a51dba2f3304dee61523d11fef4", "https://bcr.bazel.build/modules/gazelle/0.42.0/source.json": "eb6f7b0cb76c52d2679164910a01fa6ddcee409e6a7fee06e602ef259f65165c", "https://bcr.bazel.build/modules/google_benchmark/1.8.2/MODULE.bazel": "a70cf1bba851000ba93b58ae2f6d76490a9feb74192e57ab8e8ff13c34ec50cb", "https://bcr.bazel.build/modules/google_benchmark/1.8.4/MODULE.bazel": "c6d54a11dcf64ee63545f42561eda3fd94c1b5f5ebe1357011de63ae33739d5e", "https://bcr.bazel.build/modules/google_benchmark/1.8.4/source.json": "84590f7bc5a1fd99e1ef274ee16bb41c214f705e62847b42e705010dfa81fe53", "https://bcr.bazel.build/modules/googleapis-rules-registry/1.0.0/MODULE.bazel": "97c6a4d413b373d4cc97065da3de1b2166e22cbbb5f4cc9f05760bfa83619e24", "https://bcr.bazel.build/modules/googleapis-rules-registry/1.0.0/source.json": "cf611c836a60e98e2e2ab2de8004f119e9f06878dcf4ea2d95a437b1b7a89fe9", "https://bcr.bazel.build/modules/googleapis/0.0.0-20240326-1c8d509c5/MODULE.bazel": "a4b7e46393c1cdcc5a00e6f85524467c48c565256b22b5fae20f84ab4a999a68", "https://bcr.bazel.build/modules/googleapis/0.0.0-20241220-5e258e33/MODULE.bazel": "571b018644920302f5a69520b91dae189c17d566e730a1b87c9dbeefe39bd6a5", "https://bcr.bazel.build/modules/googleapis/0.0.0-20241220-5e258e33/source.json": "2172f9bad88838c509e92489545b6ec41d99fb0de8cf7b2a2da61dae7587a0e4", "https://bcr.bazel.build/modules/googletest/1.11.0/MODULE.bazel": "3a83f095183f66345ca86aa13c58b59f9f94a2f81999c093d4eeaa2d262d12f4", "https://bcr.bazel.build/modules/googletest/1.14.0.bcr.1/MODULE.bazel": "22c31a561553727960057361aa33bf20fb2e98584bc4fec007906e27053f80c6", "https://bcr.bazel.build/modules/googletest/1.14.0.bcr.1/source.json": "41e9e129f80d8c8bf103a7acc337b76e54fad1214ac0a7084bf24f4cd924b8b4", "https://bcr.bazel.build/modules/googletest/1.14.0/MODULE.bazel": "cfbcbf3e6eac06ef9d85900f64424708cc08687d1b527f0ef65aa7517af8118f", "https://bcr.bazel.build/modules/grpc-java/1.62.2/MODULE.bazel": "99b8771e8c7cacb130170fed2a10c9e8fed26334a93e73b42d2953250885a158", "https://bcr.bazel.build/modules/grpc/1.41.0/MODULE.bazel": "5bcbfc2b274dabea628f0649dc50c90cf36543b1cfc31624832538644ad1aae8", "https://bcr.bazel.build/modules/grpc/1.56.3.bcr.1/MODULE.bazel": "cd5b1eb276b806ec5ab85032921f24acc51735a69ace781be586880af20ab33f", "https://bcr.bazel.build/modules/grpc/1.65.0/MODULE.bazel": "a8601bc0ee3fea20dd4a47e5a2d6c20d29cf5208fbf986e1d6612988c53e649d", "https://bcr.bazel.build/modules/grpc/1.65.0/source.json": "8890a442b3f53ac1bd908f857c35e3a6c8889af6afd8c98a0fe052d15259115e", "https://bcr.bazel.build/modules/hermetic_cc_toolchain/3.2.0/MODULE.bazel": "8e7faec81c1f0fb65fe277ecfc75ea3636ce7bf848f88037fedd58e6eeacc28f", "https://bcr.bazel.build/modules/hermetic_cc_toolchain/3.2.0/source.json": "67c2b76edff27c3ec449a935fc9468996d1a730b52a9a6f97c40c8a06d381630", "https://bcr.bazel.build/modules/jsoncpp/1.9.5/MODULE.bazel": "31271aedc59e815656f5736f282bb7509a97c7ecb43e927ac1a37966e0578075", "https://bcr.bazel.build/modules/jsoncpp/1.9.5/source.json": "4108ee5085dd2885a341c7fab149429db457b3169b86eb081fa245eadf69169d", "https://bcr.bazel.build/modules/libpfm/4.11.0/MODULE.bazel": "45061ff025b301940f1e30d2c16bea596c25b176c8b6b3087e92615adbd52902", "https://bcr.bazel.build/modules/libpfm/4.11.0/source.json": "caaffb3ac2b59b8aac456917a4ecf3167d40478ee79f15ab7a877ec9273937c9", "https://bcr.bazel.build/modules/nlohmann_json/3.11.3/MODULE.bazel": "87023db2f55fc3a9949c7b08dc711fae4d4be339a80a99d04453c4bb3998eefc", "https://bcr.bazel.build/modules/nlohmann_json/3.11.3/source.json": "296c63a90c6813e53b3812d24245711981fc7e563d98fe15625f55181494488a", "https://bcr.bazel.build/modules/nlohmann_json/3.6.1/MODULE.bazel": "6f7b417dcc794d9add9e556673ad25cb3ba835224290f4f848f8e2db1e1fca74", "https://bcr.bazel.build/modules/opentelemetry-cpp/1.14.2/MODULE.bazel": "089a5613c2a159c7dfde098dabfc61e966889c7d6a81a98422a84c51535ed17d", "https://bcr.bazel.build/modules/opentelemetry-cpp/1.14.2/source.json": "0c5f85ab9e5894c6f1382cf58ba03a6cd024f0592bee2229f99db216ef0c6764", "https://bcr.bazel.build/modules/opentelemetry-proto/1.1.0/MODULE.bazel": "a49f406e99bf05ab43ed4f5b3322fbd33adfd484b6546948929d1316299b68bf", "https://bcr.bazel.build/modules/opentelemetry-proto/1.1.0/source.json": "39ffadc4b7d9ccc0c0f45422510cbaeb8eca7b26e68d4142fc3ff18b4c2711b6", "https://bcr.bazel.build/modules/opentracing-cpp/1.6.0/MODULE.bazel": "b3925269f63561b8b880ae7cf62ccf81f6ece55b62cd791eda9925147ae116ec", "https://bcr.bazel.build/modules/opentracing-cpp/1.6.0/source.json": "da1cb1add160f5e5074b7272e9db6fd8f1b3336c15032cd0a653af9d2f484aed", "https://bcr.bazel.build/modules/platforms/0.0.10/MODULE.bazel": "8cb8efaf200bdeb2150d93e162c40f388529a25852b332cec879373771e48ed5", "https://bcr.bazel.build/modules/platforms/0.0.11/MODULE.bazel": "0daefc49732e227caa8bfa834d65dc52e8cc18a2faf80df25e8caea151a9413f", "https://bcr.bazel.build/modules/platforms/0.0.11/source.json": "f7e188b79ebedebfe75e9e1d098b8845226c7992b307e28e1496f23112e8fc29", "https://bcr.bazel.build/modules/platforms/0.0.4/MODULE.bazel": "9b328e31ee156f53f3c416a64f8491f7eb731742655a47c9eec4703a71644aee", "https://bcr.bazel.build/modules/platforms/0.0.5/MODULE.bazel": "5733b54ea419d5eaf7997054bb55f6a1d0b5ff8aedf0176fef9eea44f3acda37", "https://bcr.bazel.build/modules/platforms/0.0.6/MODULE.bazel": "ad6eeef431dc52aefd2d77ed20a4b353f8ebf0f4ecdd26a807d2da5aa8cd0615", "https://bcr.bazel.build/modules/platforms/0.0.7/MODULE.bazel": "72fd4a0ede9ee5c021f6a8dd92b503e089f46c227ba2813ff183b71616034814", "https://bcr.bazel.build/modules/platforms/0.0.8/MODULE.bazel": "9f142c03e348f6d263719f5074b21ef3adf0b139ee4c5133e2aa35664da9eb2d", "https://bcr.bazel.build/modules/platforms/0.0.9/MODULE.bazel": "4a87a60c927b56ddd67db50c89acaa62f4ce2a1d2149ccb63ffd871d5ce29ebc", "https://bcr.bazel.build/modules/prometheus-cpp/1.2.4/MODULE.bazel": "0fbe5dcff66311947a3f6b86ebc6a6d9328e31a28413ca864debc4a043f371e5", "https://bcr.bazel.build/modules/prometheus-cpp/1.2.4/source.json": "aa58bb10d0bb0dcaf4ad2c509ddcec23d2e94c3935e21517a5adbc2363248a55", "https://bcr.bazel.build/modules/protobuf/21.7/MODULE.bazel": "a5a29bb89544f9b97edce05642fac225a808b5b7be74038ea3640fae2f8e66a7", "https://bcr.bazel.build/modules/protobuf/23.1/MODULE.bazel": "88b393b3eb4101d18129e5db51847cd40a5517a53e81216144a8c32dfeeca52a", "https://bcr.bazel.build/modules/protobuf/24.4/MODULE.bazel": "7bc7ce5f2abf36b3b7b7c8218d3acdebb9426aeb35c2257c96445756f970eb12", "https://bcr.bazel.build/modules/protobuf/26.0.bcr.2/MODULE.bazel": "62e0b84ca727bdeb55a6fe1ef180e6b191bbe548a58305ea1426c158067be534", "https://bcr.bazel.build/modules/protobuf/27.0/MODULE.bazel": "7873b60be88844a0a1d8f80b9d5d20cfbd8495a689b8763e76c6372998d3f64c", "https://bcr.bazel.build/modules/protobuf/27.1/MODULE.bazel": "703a7b614728bb06647f965264967a8ef1c39e09e8f167b3ca0bb1fd80449c0d", "https://bcr.bazel.build/modules/protobuf/29.0-rc2/MODULE.bazel": "6241d35983510143049943fc0d57937937122baf1b287862f9dc8590fc4c37df", "https://bcr.bazel.build/modules/protobuf/29.0-rc3/MODULE.bazel": "33c2dfa286578573afc55a7acaea3cada4122b9631007c594bf0729f41c8de92", "https://bcr.bazel.build/modules/protobuf/29.0/MODULE.bazel": "319dc8bf4c679ff87e71b1ccfb5a6e90a6dbc4693501d471f48662ac46d04e4e", "https://bcr.bazel.build/modules/protobuf/29.1/MODULE.bazel": "557c3457560ff49e122ed76c0bc3397a64af9574691cb8201b4e46d4ab2ecb95", "https://bcr.bazel.build/modules/protobuf/29.2/MODULE.bazel": "5435497c190d86f79b0568698c45044df7c8d97692886cda9fe9cf9053aea712", "https://bcr.bazel.build/modules/protobuf/29.2/source.json": "fe7090cc34072609b26d9beafb122916dabc1d47ba61b242c26c4b06c51384ab", "https://bcr.bazel.build/modules/protobuf/3.19.0/MODULE.bazel": "6b5fbb433f760a99a22b18b6850ed5784ef0e9928a72668b66e4d7ccd47db9b0", "https://bcr.bazel.build/modules/protobuf/3.19.2/MODULE.bazel": "532ffe5f2186b69fdde039efe6df13ba726ff338c6bc82275ad433013fa10573", "https://bcr.bazel.build/modules/protobuf/3.19.6/MODULE.bazel": "9233edc5e1f2ee276a60de3eaa47ac4132302ef9643238f23128fea53ea12858", "https://bcr.bazel.build/modules/pybind11_bazel/2.11.1/MODULE.bazel": "88af1c246226d87e65be78ed49ecd1e6f5e98648558c14ce99176da041dc378e", "https://bcr.bazel.build/modules/pybind11_bazel/2.11.1/source.json": "be4789e951dd5301282729fe3d4938995dc4c1a81c2ff150afc9f1b0504c6022", "https://bcr.bazel.build/modules/re2/2021-09-01/MODULE.bazel": "bcb6b96f3b071e6fe2d8bed9cc8ada137a105f9d2c5912e91d27528b3d123833", "https://bcr.bazel.build/modules/re2/2023-09-01/MODULE.bazel": "cb3d511531b16cfc78a225a9e2136007a48cf8a677e4264baeab57fe78a80206", "https://bcr.bazel.build/modules/re2/2023-09-01/source.json": "e044ce89c2883cd957a2969a43e79f7752f9656f6b20050b62f90ede21ec6eb4", "https://bcr.bazel.build/modules/rules_android/0.1.1/MODULE.bazel": "48809ab0091b07ad0182defb787c4c5328bd3a278938415c00a7b69b50c4d3a8", "https://bcr.bazel.build/modules/rules_android/0.1.1/source.json": "e6986b41626ee10bdc864937ffb6d6bf275bb5b9c65120e6137d56e6331f089e", "https://bcr.bazel.build/modules/rules_apple/3.5.1/MODULE.bazel": "3d1bbf65ad3692003d36d8a29eff54d4e5c1c5f4bfb60f79e28646a924d9101c", "https://bcr.bazel.build/modules/rules_apple/3.5.1/source.json": "e7593cdf26437d35dbda64faeaf5b82cbdd9df72674b0f041fdde75c1d20dda7", "https://bcr.bazel.build/modules/rules_cc/0.0.1/MODULE.bazel": "cb2aa0747f84c6c3a78dad4e2049c154f08ab9d166b1273835a8174940365647", "https://bcr.bazel.build/modules/rules_cc/0.0.10/MODULE.bazel": "ec1705118f7eaedd6e118508d3d26deba2a4e76476ada7e0e3965211be012002", "https://bcr.bazel.build/modules/rules_cc/0.0.13/MODULE.bazel": "0e8529ed7b323dad0775ff924d2ae5af7640b23553dfcd4d34344c7e7a867191", "https://bcr.bazel.build/modules/rules_cc/0.0.14/MODULE.bazel": "5e343a3aac88b8d7af3b1b6d2093b55c347b8eefc2e7d1442f7a02dc8fea48ac", "https://bcr.bazel.build/modules/rules_cc/0.0.15/MODULE.bazel": "6704c35f7b4a72502ee81f61bf88706b54f06b3cbe5558ac17e2e14666cd5dcc", "https://bcr.bazel.build/modules/rules_cc/0.0.16/MODULE.bazel": "7661303b8fc1b4d7f532e54e9d6565771fea666fbdf839e0a86affcd02defe87", "https://bcr.bazel.build/modules/rules_cc/0.0.17/MODULE.bazel": "2ae1d8f4238ec67d7185d8861cb0a2cdf4bc608697c331b95bf990e69b62e64a", "https://bcr.bazel.build/modules/rules_cc/0.0.17/source.json": "4db99b3f55c90ab28d14552aa0632533e3e8e5e9aea0f5c24ac0014282c2a7c5", "https://bcr.bazel.build/modules/rules_cc/0.0.2/MODULE.bazel": "6915987c90970493ab97393024c156ea8fb9f3bea953b2f3ec05c34f19b5695c", "https://bcr.bazel.build/modules/rules_cc/0.0.5/MODULE.bazel": "be41f87587998fe8890cd82ea4e848ed8eb799e053c224f78f3ff7fe1a1d9b74", "https://bcr.bazel.build/modules/rules_cc/0.0.6/MODULE.bazel": "abf360251023dfe3efcef65ab9d56beefa8394d4176dd29529750e1c57eaa33f", "https://bcr.bazel.build/modules/rules_cc/0.0.8/MODULE.bazel": "964c85c82cfeb6f3855e6a07054fdb159aced38e99a5eecf7bce9d53990afa3e", "https://bcr.bazel.build/modules/rules_cc/0.0.9/MODULE.bazel": "836e76439f354b89afe6a911a7adf59a6b2518fafb174483ad78a2a2fde7b1c5", "https://bcr.bazel.build/modules/rules_foreign_cc/0.10.1/MODULE.bazel": "b9527010e5fef060af92b6724edb3691970a5b1f76f74b21d39f7d433641be60", "https://bcr.bazel.build/modules/rules_foreign_cc/0.10.1/source.json": "9300e71df0cdde0952f10afff1401fa664e9fc5d9ae6204660ba1b158d90d6a6", "https://bcr.bazel.build/modules/rules_foreign_cc/0.9.0/MODULE.bazel": "c9e8c682bf75b0e7c704166d79b599f93b72cfca5ad7477df596947891feeef6", "https://bcr.bazel.build/modules/rules_fuzzing/0.5.2/MODULE.bazel": "40c97d1144356f52905566c55811f13b299453a14ac7769dfba2ac38192337a8", "https://bcr.bazel.build/modules/rules_fuzzing/0.5.2/source.json": "c8b1e2c717646f1702290959a3302a178fb639d987ab61d548105019f11e527e", "https://bcr.bazel.build/modules/rules_go/0.33.0/MODULE.bazel": "a2b11b64cd24bf94f57454f53288a5dacfe6cb86453eee7761b7637728c1910c", "https://bcr.bazel.build/modules/rules_go/0.38.1/MODULE.bazel": "fb8e73dd3b6fc4ff9d260ceacd830114891d49904f5bda1c16bc147bcc254f71", "https://bcr.bazel.build/modules/rules_go/0.39.1/MODULE.bazel": "d34fb2a249403a5f4339c754f1e63dc9e5ad70b47c5e97faee1441fc6636cd61", "https://bcr.bazel.build/modules/rules_go/0.41.0/MODULE.bazel": "55861d8e8bb0e62cbd2896f60ff303f62ffcb0eddb74ecb0e5c0cbe36fc292c8", "https://bcr.bazel.build/modules/rules_go/0.42.0/MODULE.bazel": "8cfa875b9aa8c6fce2b2e5925e73c1388173ea3c32a0db4d2b4804b453c14270", "https://bcr.bazel.build/modules/rules_go/0.46.0/MODULE.bazel": "3477df8bdcc49e698b9d25f734c4f3a9f5931ff34ee48a2c662be168f5f2d3fd", "https://bcr.bazel.build/modules/rules_go/0.48.0/MODULE.bazel": "d00ebcae0908ee3f5e6d53f68677a303d6d59a77beef879598700049c3980a03", "https://bcr.bazel.build/modules/rules_go/0.49.0/MODULE.bazel": "61cfc1ba17123356d1b12b6c50f6e0162b2cc7fd6f51753c12471e973a0f72a5", "https://bcr.bazel.build/modules/rules_go/0.50.1/MODULE.bazel": "b91a308dc5782bb0a8021ad4330c81fea5bda77f96b9e4c117b9b9c8f6665ee0", "https://bcr.bazel.build/modules/rules_go/0.52.0/MODULE.bazel": "0cf080a2706aa8fc9abf64286cee60fdf0238db37b7f1793b0f7d550d59ea3ae", "https://bcr.bazel.build/modules/rules_go/0.52.0/source.json": "441bc7591044993dce9fb0377fcadf3086d6afac621b909d17d53858a4a1b8d4", "https://bcr.bazel.build/modules/rules_java/4.0.0/MODULE.bazel": "5a78a7ae82cd1a33cef56dc578c7d2a46ed0dca12643ee45edbb8417899e6f74", "https://bcr.bazel.build/modules/rules_java/5.1.0/MODULE.bazel": "324b6478b0343a3ce7a9add8586ad75d24076d6d43d2f622990b9c1cfd8a1b15", "https://bcr.bazel.build/modules/rules_java/5.3.5/MODULE.bazel": "a4ec4f2db570171e3e5eb753276ee4b389bae16b96207e9d3230895c99644b86", "https://bcr.bazel.build/modules/rules_java/6.0.0/MODULE.bazel": "8a43b7df601a7ec1af61d79345c17b31ea1fedc6711fd4abfd013ea612978e39", "https://bcr.bazel.build/modules/rules_java/6.3.0/MODULE.bazel": "a97c7678c19f236a956ad260d59c86e10a463badb7eb2eda787490f4c969b963", "https://bcr.bazel.build/modules/rules_java/6.4.0/MODULE.bazel": "e986a9fe25aeaa84ac17ca093ef13a4637f6107375f64667a15999f77db6c8f6", "https://bcr.bazel.build/modules/rules_java/6.5.2/MODULE.bazel": "1d440d262d0e08453fa0c4d8f699ba81609ed0e9a9a0f02cd10b3e7942e61e31", "https://bcr.bazel.build/modules/rules_java/7.1.0/MODULE.bazel": "30d9135a2b6561c761bd67bd4990da591e6bdc128790ce3e7afd6a3558b2fb64", "https://bcr.bazel.build/modules/rules_java/7.10.0/MODULE.bazel": "530c3beb3067e870561739f1144329a21c851ff771cd752a49e06e3dc9c2e71a", "https://bcr.bazel.build/modules/rules_java/7.12.2/MODULE.bazel": "579c505165ee757a4280ef83cda0150eea193eed3bef50b1004ba88b99da6de6", "https://bcr.bazel.build/modules/rules_java/7.2.0/MODULE.bazel": "06c0334c9be61e6cef2c8c84a7800cef502063269a5af25ceb100b192453d4ab", "https://bcr.bazel.build/modules/rules_java/7.3.2/MODULE.bazel": "50dece891cfdf1741ea230d001aa9c14398062f2b7c066470accace78e412bc2", "https://bcr.bazel.build/modules/rules_java/7.4.0/MODULE.bazel": "a592852f8a3dd539e82ee6542013bf2cadfc4c6946be8941e189d224500a8934", "https://bcr.bazel.build/modules/rules_java/7.6.1/MODULE.bazel": "2f14b7e8a1aa2f67ae92bc69d1ec0fa8d9f827c4e17ff5e5f02e91caa3b2d0fe", "https://bcr.bazel.build/modules/rules_java/8.3.2/MODULE.bazel": "7336d5511ad5af0b8615fdc7477535a2e4e723a357b6713af439fe8cf0195017", "https://bcr.bazel.build/modules/rules_java/8.5.1/MODULE.bazel": "d8a9e38cc5228881f7055a6079f6f7821a073df3744d441978e7a43e20226939", "https://bcr.bazel.build/modules/rules_java/8.6.1/MODULE.bazel": "f4808e2ab5b0197f094cabce9f4b006a27766beb6a9975931da07099560ca9c2", "https://bcr.bazel.build/modules/rules_java/8.6.1/source.json": "f18d9ad3c4c54945bf422ad584fa6c5ca5b3116ff55a5b1bc77e5c1210be5960", "https://bcr.bazel.build/modules/rules_jvm_external/4.4.2/MODULE.bazel": "a56b85e418c83eb1839819f0b515c431010160383306d13ec21959ac412d2fe7", "https://bcr.bazel.build/modules/rules_jvm_external/5.1/MODULE.bazel": "33f6f999e03183f7d088c9be518a63467dfd0be94a11d0055fe2d210f89aa909", "https://bcr.bazel.build/modules/rules_jvm_external/5.2/MODULE.bazel": "d9351ba35217ad0de03816ef3ed63f89d411349353077348a45348b096615036", "https://bcr.bazel.build/modules/rules_jvm_external/5.3/MODULE.bazel": "bf93870767689637164657731849fb887ad086739bd5d360d90007a581d5527d", "https://bcr.bazel.build/modules/rules_jvm_external/6.0/MODULE.bazel": "37c93a5a78d32e895d52f86a8d0416176e915daabd029ccb5594db422e87c495", "https://bcr.bazel.build/modules/rules_jvm_external/6.1/MODULE.bazel": "75b5fec090dbd46cf9b7d8ea08cf84a0472d92ba3585b476f44c326eda8059c4", "https://bcr.bazel.build/modules/rules_jvm_external/6.3/MODULE.bazel": "c998e060b85f71e00de5ec552019347c8bca255062c990ac02d051bb80a38df0", "https://bcr.bazel.build/modules/rules_jvm_external/6.3/source.json": "6f5f5a5a4419ae4e37c35a5bb0a6ae657ed40b7abc5a5189111b47fcebe43197", "https://bcr.bazel.build/modules/rules_kotlin/1.9.0/MODULE.bazel": "ef85697305025e5a61f395d4eaede272a5393cee479ace6686dba707de804d59", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/MODULE.bazel": "d269a01a18ee74d0335450b10f62c9ed81f2321d7958a2934e44272fe82dcef3", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/source.json": "2faa4794364282db7c06600b7e5e34867a564ae91bda7cae7c29c64e9466b7d5", "https://bcr.bazel.build/modules/rules_license/0.0.3/MODULE.bazel": "627e9ab0247f7d1e05736b59dbb1b6871373de5ad31c3011880b4133cafd4bd0", "https://bcr.bazel.build/modules/rules_license/0.0.7/MODULE.bazel": "088fbeb0b6a419005b89cf93fe62d9517c0a2b8bb56af3244af65ecfe37e7d5d", "https://bcr.bazel.build/modules/rules_license/1.0.0/MODULE.bazel": "a7fda60eefdf3d8c827262ba499957e4df06f659330bbe6cdbdb975b768bb65c", "https://bcr.bazel.build/modules/rules_license/1.0.0/source.json": "a52c89e54cc311196e478f8382df91c15f7a2bfdf4c6cd0e2675cc2ff0b56efb", "https://bcr.bazel.build/modules/rules_multirun/0.11.0/MODULE.bazel": "19b6307a5ef8a890255cecd704027af0a1afd0f04b992666e0c3ea55ba3398e0", "https://bcr.bazel.build/modules/rules_multirun/0.11.0/source.json": "04504712281584d8d70989ef5072d741701ccd51529d3fe77c0ebef12efdbb58", "https://bcr.bazel.build/modules/rules_multitool/0.11.0/MODULE.bazel": "8d9dda78d2398e136300d3ef4fbcc89ede7c32c158d8c016fa7d032df41c4aaf", "https://bcr.bazel.build/modules/rules_multitool/0.11.0/source.json": "0b86574a1eaff37c33aafaff095ea16d6ac846beb94ffc74c4fcf626f8f80681", "https://bcr.bazel.build/modules/rules_nodejs/6.2.0/MODULE.bazel": "ec27907f55eb34705adb4e8257952162a2d4c3ed0f0b3b4c3c1aad1fac7be35e", "https://bcr.bazel.build/modules/rules_nodejs/6.3.0/MODULE.bazel": "45345e4aba35dd6e4701c1eebf5a4e67af4ed708def9ebcdc6027585b34ee52d", "https://bcr.bazel.build/modules/rules_nodejs/6.3.4/MODULE.bazel": "8a87c1d45548e7224e261f721a559a984995d3728cb95fb60cf249491ebf72a7", "https://bcr.bazel.build/modules/rules_nodejs/6.3.4/source.json": "26645e2934d5783805a70d50ae0bb7d65a52e993dc1de16a70878f0a11bf9e47", "https://bcr.bazel.build/modules/rules_oci/2.2.3/MODULE.bazel": "1b6ecbe5aed7c1d0dc5659f3dff046ab88851f935df3af351b9e90fcf965135d", "https://bcr.bazel.build/modules/rules_oci/2.2.3/source.json": "0b4fd9acc23a123b2712abf1bd339b87ce0ffe8875c0859458d41212d2a771aa", "https://bcr.bazel.build/modules/rules_pkg/0.7.0/MODULE.bazel": "df99f03fc7934a4737122518bb87e667e62d780b610910f0447665a7e2be62dc", "https://bcr.bazel.build/modules/rules_pkg/1.0.1/MODULE.bazel": "5b1df97dbc29623bccdf2b0dcd0f5cb08e2f2c9050aab1092fd39a41e82686ff", "https://bcr.bazel.build/modules/rules_pkg/1.1.0/MODULE.bazel": "9db8031e71b6ef32d1846106e10dd0ee2deac042bd9a2de22b4761b0c3036453", "https://bcr.bazel.build/modules/rules_pkg/1.1.0/source.json": "fef768df13a92ce6067e1cd0cdc47560dace01354f1d921cfb1d632511f7d608", "https://bcr.bazel.build/modules/rules_proto/4.0.0/MODULE.bazel": "a7a7b6ce9bee418c1a760b3d84f83a299ad6952f9903c67f19e4edd964894e06", "https://bcr.bazel.build/modules/rules_proto/5.3.0-21.7/MODULE.bazel": "e8dff86b0971688790ae75528fe1813f71809b5afd57facb44dad9e8eca631b7", "https://bcr.bazel.build/modules/rules_proto/6.0.0-rc1/MODULE.bazel": "1e5b502e2e1a9e825eef74476a5a1ee524a92297085015a052510b09a1a09483", "https://bcr.bazel.build/modules/rules_proto/6.0.0/MODULE.bazel": "b531d7f09f58dce456cd61b4579ce8c86b38544da75184eadaf0a7cb7966453f", "https://bcr.bazel.build/modules/rules_proto/6.0.2/MODULE.bazel": "ce916b775a62b90b61888052a416ccdda405212b6aaeb39522f7dc53431a5e73", "https://bcr.bazel.build/modules/rules_proto/7.0.2/MODULE.bazel": "bf81793bd6d2ad89a37a40693e56c61b0ee30f7a7fdbaf3eabbf5f39de47dea2", "https://bcr.bazel.build/modules/rules_proto/7.1.0/MODULE.bazel": "002d62d9108f75bb807cd56245d45648f38275cb3a99dcd45dfb864c5d74cb96", "https://bcr.bazel.build/modules/rules_proto/7.1.0/source.json": "39f89066c12c24097854e8f57ab8558929f9c8d474d34b2c00ac04630ad8940e", "https://bcr.bazel.build/modules/rules_proto_grpc/5.0.1/MODULE.bazel": "af7a76546e6fb5cfb37d30ece061bad276ceb785eb4ea43d6f74fc35cff71dfc", "https://bcr.bazel.build/modules/rules_proto_grpc/5.0.1/source.json": "eb2a5cd4344970803514e64bce3bb16840fe9476a4e9695d95c6e0475d821606", "https://bcr.bazel.build/modules/rules_proto_grpc_cpp/5.0.1/MODULE.bazel": "2bd7210df98e3690c7146b7679b1361a802eb01ec9431167ef5cafc641d9c94d", "https://bcr.bazel.build/modules/rules_proto_grpc_cpp/5.0.1/source.json": "aa40ef927cf13362c51c2c8796d6a67278e0de8c7b2f4874003dc503b6b4ff35", "https://bcr.bazel.build/modules/rules_proto_grpc_go/5.0.1/MODULE.bazel": "1d6a9096ae9d4714b5d82d693fb714fb0b1554d56e6fb1c18595c1e35a2121fb", "https://bcr.bazel.build/modules/rules_proto_grpc_go/5.0.1/source.json": "ec321eca680b081db23700e5e4b18f5c4e03feb438d26715aee99830889b3385", "https://bcr.bazel.build/modules/rules_proto_grpc_grpc_gateway/5.0.1/MODULE.bazel": "cb7f583257592d9e12f5997a3c84e359ae3313c739450dea194e097406726266", "https://bcr.bazel.build/modules/rules_proto_grpc_grpc_gateway/5.0.1/source.json": "260b735e74f52d4e1437539ccbbe81d218acc5554ca67a8ab1f5586b8623d0e2", "https://bcr.bazel.build/modules/rules_python/0.10.2/MODULE.bazel": "cc82bc96f2997baa545ab3ce73f196d040ffb8756fd2d66125a530031cd90e5f", "https://bcr.bazel.build/modules/rules_python/0.20.0/MODULE.bazel": "bfe14d17f20e3fe900b9588f526f52c967a6f281e47a1d6b988679bd15082286", "https://bcr.bazel.build/modules/rules_python/0.23.1/MODULE.bazel": "49ffccf0511cb8414de28321f5fcf2a31312b47c40cc21577144b7447f2bf300", "https://bcr.bazel.build/modules/rules_python/0.25.0/MODULE.bazel": "72f1506841c920a1afec76975b35312410eea3aa7b63267436bfb1dd91d2d382", "https://bcr.bazel.build/modules/rules_python/0.28.0/MODULE.bazel": "cba2573d870babc976664a912539b320cbaa7114cd3e8f053c720171cde331ed", "https://bcr.bazel.build/modules/rules_python/0.29.0/MODULE.bazel": "2ac8cd70524b4b9ec49a0b8284c79e4cd86199296f82f6e0d5da3f783d660c82", "https://bcr.bazel.build/modules/rules_python/0.31.0/MODULE.bazel": "93a43dc47ee570e6ec9f5779b2e64c1476a6ce921c48cc9a1678a91dd5f8fd58", "https://bcr.bazel.build/modules/rules_python/0.34.0/MODULE.bazel": "1d623d026e075b78c9fde483a889cda7996f5da4f36dffb24c246ab30f06513a", "https://bcr.bazel.build/modules/rules_python/0.36.0/MODULE.bazel": "a4ce1ccea92b9106c7d16ab9ee51c6183107e78ba4a37aa65055227b80cd480c", "https://bcr.bazel.build/modules/rules_python/0.4.0/MODULE.bazel": "9208ee05fd48bf09ac60ed269791cf17fb343db56c8226a720fbb1cdf467166c", "https://bcr.bazel.build/modules/rules_python/0.40.0/MODULE.bazel": "9d1a3cd88ed7d8e39583d9ffe56ae8a244f67783ae89b60caafc9f5cf318ada7", "https://bcr.bazel.build/modules/rules_python/1.0.0/MODULE.bazel": "898a3d999c22caa585eb062b600f88654bf92efb204fa346fb55f6f8edffca43", "https://bcr.bazel.build/modules/rules_python/1.0.0/source.json": "b0162a65c6312e45e7912e39abd1a7f8856c2c7e41ecc9b6dc688a6f6400a917", "https://bcr.bazel.build/modules/rules_shell/0.2.0/MODULE.bazel": "fda8a652ab3c7d8fee214de05e7a9916d8b28082234e8d2c0094505c5268ed3c", "https://bcr.bazel.build/modules/rules_shell/0.3.0/MODULE.bazel": "de4402cd12f4cc8fda2354fce179fdb068c0b9ca1ec2d2b17b3e21b24c1a937b", "https://bcr.bazel.build/modules/rules_shell/0.3.0/source.json": "c55ed591aa5009401ddf80ded9762ac32c358d2517ee7820be981e2de9756cf3", "https://bcr.bazel.build/modules/rules_swift/1.18.0/MODULE.bazel": "a6aba73625d0dc64c7b4a1e831549b6e375fbddb9d2dde9d80c9de6ec45b24c9", "https://bcr.bazel.build/modules/rules_swift/1.18.0/source.json": "9e636cabd446f43444ea2662341a9cbb74ecd87ab0557225ae73f1127cb7ff52", "https://bcr.bazel.build/modules/rules_uv/0.62.0/MODULE.bazel": "89611c098d759da8bdb7a983c1336231ef533b85efca13dfdd96742902a725e5", "https://bcr.bazel.build/modules/rules_uv/0.62.0/source.json": "fd5c5d2f9b8e3fd90a61b68797fbd470f8b610f36e78670785b8d2a7308573cf", "https://bcr.bazel.build/modules/stardoc/0.5.1/MODULE.bazel": "1a05d92974d0c122f5ccf09291442580317cdd859f07a8655f1db9a60374f9f8", "https://bcr.bazel.build/modules/stardoc/0.5.3/MODULE.bazel": "c7f6948dae6999bf0db32c1858ae345f112cacf98f174c7a8bb707e41b974f1c", "https://bcr.bazel.build/modules/stardoc/0.5.4/MODULE.bazel": "6569966df04610b8520957cb8e97cf2e9faac2c0309657c537ab51c16c18a2a4", "https://bcr.bazel.build/modules/stardoc/0.5.6/MODULE.bazel": "c43dabc564990eeab55e25ed61c07a1aadafe9ece96a4efabb3f8bf9063b71ef", "https://bcr.bazel.build/modules/stardoc/0.6.2/MODULE.bazel": "7060193196395f5dd668eda046ccbeacebfd98efc77fed418dbe2b82ffaa39fd", "https://bcr.bazel.build/modules/stardoc/0.7.0/MODULE.bazel": "05e3d6d30c099b6770e97da986c53bd31844d7f13d41412480ea265ac9e8079c", "https://bcr.bazel.build/modules/stardoc/0.7.1/MODULE.bazel": "3548faea4ee5dda5580f9af150e79d0f6aea934fc60c1cc50f4efdd9420759e7", "https://bcr.bazel.build/modules/stardoc/0.7.2/MODULE.bazel": "fc152419aa2ea0f51c29583fab1e8c99ddefd5b3778421845606ee628629e0e5", "https://bcr.bazel.build/modules/stardoc/0.7.2/source.json": "58b029e5e901d6802967754adf0a9056747e8176f017cfe3607c0851f4d42216", "https://bcr.bazel.build/modules/toolchains_protoc/0.3.1/MODULE.bazel": "b6574a2a314cbd40cafb5ed87b03d1996e015315f80a7e33116c8b2e209cb5cf", "https://bcr.bazel.build/modules/toolchains_protoc/0.3.7/MODULE.bazel": "d2758e5af5838970175274c4fcb67edc8d2f4f8f348885da90c42fafc221236d", "https://bcr.bazel.build/modules/toolchains_protoc/0.3.7/source.json": "07e0eab9be4aded2a8074504a80c55c5a48e811a9824061642af639baaabf985", "https://bcr.bazel.build/modules/upb/0.0.0-20211020-160625a/MODULE.bazel": "6cced416be2dc5b9c05efd5b997049ba795e5e4e6fafbe1624f4587767638928", "https://bcr.bazel.build/modules/upb/0.0.0-20220923-a547704/MODULE.bazel": "7298990c00040a0e2f121f6c32544bab27d4452f80d9ce51349b1a28f3005c43", "https://bcr.bazel.build/modules/upb/0.0.0-20230516-61a97ef/MODULE.bazel": "c0df5e35ad55e264160417fd0875932ee3c9dda63d9fccace35ac62f45e1b6f9", "https://bcr.bazel.build/modules/upb/0.0.0-20230907-e7430e6/MODULE.bazel": "3a7dedadf70346e678dc059dbe44d05cbf3ab17f1ce43a1c7a42edc7cbf93fd9", "https://bcr.bazel.build/modules/upb/0.0.0-20230907-e7430e6/source.json": "6e513de1d26d1ded97a1c98a8ee166ff9be371a71556d4bc91220332dd3aa48e", "https://bcr.bazel.build/modules/zlib/1.2.11/MODULE.bazel": "07b389abc85fdbca459b69e2ec656ae5622873af3f845e1c9d80fe179f3effa0", "https://bcr.bazel.build/modules/zlib/1.2.12/MODULE.bazel": "3b1a8834ada2a883674be8cbd36ede1b6ec481477ada359cd2d3ddc562340b27", "https://bcr.bazel.build/modules/zlib/1.2.13/MODULE.bazel": "aa6deb1b83c18ffecd940c4119aff9567cd0a671d7bba756741cb2ef043a29d5", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.1/MODULE.bazel": "6a9fe6e3fc865715a7be9823ce694ceb01e364c35f7a846bf0d2b34762bc066b", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.3/MODULE.bazel": "af322bc08976524477c79d1e45e241b6efbeb918c497e8840b8ab116802dda79", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.3/source.json": "2be409ac3c7601245958cd4fcdff4288be79ed23bd690b4b951f500d54ee6e7d", "https://bcr.bazel.build/modules/zlib/1.3.1/MODULE.bazel": "751c9940dcfe869f5f7274e1295422a34623555916eb98c174c1e945594bf198", "https://bcr.bazel.build/modules/zlib/1.3/MODULE.bazel": "6a9c02f19a24dcedb05572b2381446e27c272cd383aed11d41d99da9e3167a72"}, "selectedYankedVersions": {}, "moduleExtensions": {"@@apple_support+//crosstool:setup.bzl%apple_cc_configure_extension": {"general": {"bzlTransitiveDigest": "okb7JAyJ9zeL+SDmtbWT0XBLq8WRoLJ0zWAG783RLVI=", "usagesDigest": "yAC1H7cg3wkisnNswc7hxM2fAxrH04yqn7CXVasZPgc=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"local_config_apple_cc_toolchains": {"repoRuleId": "@@apple_support+//crosstool:setup.bzl%_apple_cc_autoconf_toolchains", "attributes": {}}, "local_config_apple_cc": {"repoRuleId": "@@apple_support+//crosstool:setup.bzl%_apple_cc_autoconf", "attributes": {}}}, "recordedRepoMappingEntries": [["apple_support+", "bazel_tools", "bazel_tools"], ["bazel_tools", "rules_cc", "rules_cc+"]]}}, "@@aspect_rules_js+//npm:extensions.bzl%pnpm": {"general": {"bzlTransitiveDigest": "XI1G/dfMsS4ruMJlpJD1UgH1NQvwks0194qwMkZz9bI=", "usagesDigest": "x0vHfkUGWojsaLB1/xG2+g9Wgn/8A74VHlxG11eygZQ=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"pnpm": {"repoRuleId": "@@aspect_rules_js+//npm/private:npm_import.bzl%npm_import_rule", "attributes": {"package": "pnpm", "version": "10.6.5", "root_package": "", "link_workspace": "", "link_packages": {}, "integrity": "sha512-zfko/KIIMs1Z7FOCZJK33CXcUk1DcLa0rb9lgD0y76psHIgUfArk6NV5psnuxxV1e1DU+jXuoXnYaOraTtBDrw==", "url": "", "commit": "", "patch_args": ["-p0"], "patches": [], "custom_postinstall": "", "npm_auth": "", "npm_auth_basic": "", "npm_auth_username": "", "npm_auth_password": "", "lifecycle_hooks": [], "extra_build_content": "load(\"@aspect_rules_js//js:defs.bzl\", \"js_binary\")\njs_binary(name = \"pnpm\", data = glob([\"package/**\"]), entry_point = \"package/dist/pnpm.cjs\", visibility = [\"//visibility:public\"])", "generate_bzl_library_targets": false, "extract_full_archive": true, "exclude_package_contents": [], "system_tar": "auto"}}, "pnpm__links": {"repoRuleId": "@@aspect_rules_js+//npm/private:npm_import.bzl%npm_import_links", "attributes": {"package": "pnpm", "version": "10.6.5", "dev": false, "root_package": "", "link_packages": {}, "deps": {}, "transitive_closure": {}, "lifecycle_build_target": false, "lifecycle_hooks_env": [], "lifecycle_hooks_execution_requirements": ["no-sandbox"], "lifecycle_hooks_use_default_shell_env": false, "bins": {}, "package_visibility": ["//visibility:public"], "replace_package": "", "exclude_package_contents": []}}}, "recordedRepoMappingEntries": [["aspect_bazel_lib+", "bazel_skylib", "bazel_skylib+"], ["aspect_bazel_lib+", "bazel_tools", "bazel_tools"], ["aspect_rules_js+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["aspect_rules_js+", "bazel_features", "bazel_features+"], ["aspect_rules_js+", "bazel_skylib", "bazel_skylib+"], ["aspect_rules_js+", "bazel_tools", "bazel_tools"], ["bazel_features+", "bazel_features_globals", "bazel_features++version_extension+bazel_features_globals"], ["bazel_features+", "bazel_features_version", "bazel_features++version_extension+bazel_features_version"]]}}, "@@aspect_rules_py+//py:extensions.bzl%py_tools": {"general": {"bzlTransitiveDigest": "6C3+TlQhp5AgA/FO67Y+TZkQgILQoXMCBQvH2E9Eewk=", "usagesDigest": "/X+U31L/0W+mPo15khS6kjLevIqdkqh+JgfyWHDdHH8=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"bsd_tar_darwin_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "bsd_tar_darwin_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "bsd_tar_linux_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "linux_amd64"}}, "bsd_tar_linux_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "linux_arm64"}}, "bsd_tar_windows_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "windows_amd64"}}, "bsd_tar_toolchains": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%tar_toolchains_repo", "attributes": {"user_repository_name": "bsd_tar"}}, "rules_py_tools.darwin_amd64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:tools.bzl%prebuilt_tool_repo", "attributes": {"platform": "darwin_amd64"}}, "rules_py_tools.darwin_arm64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:tools.bzl%prebuilt_tool_repo", "attributes": {"platform": "darwin_arm64"}}, "rules_py_tools.linux_amd64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:tools.bzl%prebuilt_tool_repo", "attributes": {"platform": "linux_amd64"}}, "rules_py_tools.linux_arm64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:tools.bzl%prebuilt_tool_repo", "attributes": {"platform": "linux_arm64"}}, "rules_py_tools": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:repo.bzl%toolchains_repo", "attributes": {"user_repository_name": "rules_py_tools"}}, "rules_py_pex_2_3_1": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://files.pythonhosted.org/packages/e7/d0/fbda2a4d41d62d86ce53f5ae4fbaaee8c34070f75bb7ca009090510ae874/pex-2.3.1-py2.py3-none-any.whl"], "sha256": "64692a5bf6f298403aab930d22f0d836ae4736c5bc820e262e9092fe8c56f830", "downloaded_file_path": "pex-2.3.1-py2.py3-none-any.whl"}}}, "recordedRepoMappingEntries": [["aspect_bazel_lib+", "bazel_tools", "bazel_tools"], ["aspect_rules_py+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["aspect_rules_py+", "bazel_tools", "bazel_tools"]]}}, "@@aspect_rules_ts+//ts:extensions.bzl%ext": {"general": {"bzlTransitiveDigest": "FJ5umetm8cKp3WM9RnI4dWzq5a6rpU5nhK4C+Rgq9hw=", "usagesDigest": "DUgr9xnkjfNbXY80AKYzOmFR3Cqmx4BSSedvlwZmvhw=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"npm_typescript": {"repoRuleId": "@@aspect_rules_ts+//ts/private:npm_repositories.bzl%http_archive_version", "attributes": {"bzlmod": true, "version": "", "version_from": "@@//:package.json", "integrity": "", "build_file": "@@aspect_rules_ts+//ts:BUILD.typescript", "build_file_substitutions": {"bazel_worker_version": "5.4.2", "google_protobuf_version": "3.20.1"}, "urls": ["https://registry.npmjs.org/typescript/-/typescript-{}.tgz"]}}}, "recordedRepoMappingEntries": [["aspect_rules_ts+", "bazel_tools", "bazel_tools"]]}}, "@@buildifier_prebuilt+//:defs.bzl%buildifier_prebuilt_deps_extension": {"general": {"bzlTransitiveDigest": "qC/0s/MZ8q8Sf6/o/iJNMssZNgXa3CjJ7vEVbpHFQRs=", "usagesDigest": "eWMDBEn8E8CrwAPXrlrjIap2pseSMhxDyDdrntHBOOE=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"buildifier_darwin_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildifier-darwin-amd64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "375f823103d01620aaec20a0c29c6cbca99f4fd0725ae30b93655c6704f44d71"}}, "buildifier_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildifier-darwin-arm64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "5a6afc6ac7a09f5455ba0b89bd99d5ae23b4174dc5dc9d6c0ed5ce8caac3f813"}}, "buildifier_linux_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildifier-linux-amd64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "5474cc5128a74e806783d54081f581662c4be8ae65022f557e9281ed5dc88009"}}, "buildifier_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildifier-linux-arm64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "0bf86c4bfffaf4f08eed77bde5b2082e4ae5039a11e2e8b03984c173c34a561c"}}, "buildifier_windows_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildifier-windows-amd64.exe"], "downloaded_file_path": "buildifier.exe", "executable": true, "sha256": "370cd576075ad29930a82f5de132f1a1de4084c784a82514bd4da80c85acf4a8"}}, "buildozer_darwin_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildozer-darwin-amd64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "854c9583efc166602276802658cef3f224d60898cfaa60630b33d328db3b0de2"}}, "buildozer_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildozer-darwin-arm64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "31b1bfe20d7d5444be217af78f94c5c43799cdf847c6ce69794b7bf3319c5364"}}, "buildozer_linux_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildozer-linux-amd64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "3305e287b3fcc68b9a35fd8515ee617452cd4e018f9e6886b6c7cdbcba8710d4"}}, "buildozer_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildozer-linux-arm64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "0b5a2a717ac4fc911e1fec8d92af71dbb4fe95b10e5213da0cc3d56cea64a328"}}, "buildozer_windows_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v7.3.1/buildozer-windows-amd64.exe"], "downloaded_file_path": "buildozer.exe", "executable": true, "sha256": "58d41ce53257c5594c9bc86d769f580909269f68de114297f46284fbb9023dcf"}}, "buildifier_prebuilt_toolchains": {"repoRuleId": "@@buildifier_prebuilt+//:defs.bzl%_buildifier_toolchain_setup", "attributes": {"assets_json": "[{\"arch\":\"amd64\",\"name\":\"buildifier\",\"platform\":\"darwin\",\"sha256\":\"375f823103d01620aaec20a0c29c6cbca99f4fd0725ae30b93655c6704f44d71\",\"version\":\"v7.3.1\"},{\"arch\":\"arm64\",\"name\":\"buildifier\",\"platform\":\"darwin\",\"sha256\":\"5a6afc6ac7a09f5455ba0b89bd99d5ae23b4174dc5dc9d6c0ed5ce8caac3f813\",\"version\":\"v7.3.1\"},{\"arch\":\"amd64\",\"name\":\"buildifier\",\"platform\":\"linux\",\"sha256\":\"5474cc5128a74e806783d54081f581662c4be8ae65022f557e9281ed5dc88009\",\"version\":\"v7.3.1\"},{\"arch\":\"arm64\",\"name\":\"buildifier\",\"platform\":\"linux\",\"sha256\":\"0bf86c4bfffaf4f08eed77bde5b2082e4ae5039a11e2e8b03984c173c34a561c\",\"version\":\"v7.3.1\"},{\"arch\":\"amd64\",\"name\":\"buildifier\",\"platform\":\"windows\",\"sha256\":\"370cd576075ad29930a82f5de132f1a1de4084c784a82514bd4da80c85acf4a8\",\"version\":\"v7.3.1\"},{\"arch\":\"amd64\",\"name\":\"buildozer\",\"platform\":\"darwin\",\"sha256\":\"854c9583efc166602276802658cef3f224d60898cfaa60630b33d328db3b0de2\",\"version\":\"v7.3.1\"},{\"arch\":\"arm64\",\"name\":\"buildozer\",\"platform\":\"darwin\",\"sha256\":\"31b1bfe20d7d5444be217af78f94c5c43799cdf847c6ce69794b7bf3319c5364\",\"version\":\"v7.3.1\"},{\"arch\":\"amd64\",\"name\":\"buildozer\",\"platform\":\"linux\",\"sha256\":\"3305e287b3fcc68b9a35fd8515ee617452cd4e018f9e6886b6c7cdbcba8710d4\",\"version\":\"v7.3.1\"},{\"arch\":\"arm64\",\"name\":\"buildozer\",\"platform\":\"linux\",\"sha256\":\"0b5a2a717ac4fc911e1fec8d92af71dbb4fe95b10e5213da0cc3d56cea64a328\",\"version\":\"v7.3.1\"},{\"arch\":\"amd64\",\"name\":\"buildozer\",\"platform\":\"windows\",\"sha256\":\"58d41ce53257c5594c9bc86d769f580909269f68de114297f46284fbb9023dcf\",\"version\":\"v7.3.1\"}]"}}}, "recordedRepoMappingEntries": [["buildifier_prebuilt+", "bazel_skylib", "bazel_skylib+"], ["buildifier_prebuilt+", "bazel_tools", "bazel_tools"]]}}, "@@openapi_tools_generator_bazel+//:extension.bzl%openapi_gen": {"general": {"bzlTransitiveDigest": "OpLFSbBZQIsnXk7RdEflIQTNoFCU+0FDFCogz5A2rAc=", "usagesDigest": "SaFhS5QWvxieHYmjnoneMFGwHZgu8fP1ohVQ2z1dIZY=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"openapi_tools_generator_bazel_cli": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:jvm.bzl%jvm_import_external", "attributes": {"generated_rule_name": "openapi_tools_generator_bazel_cli", "artifact_urls": ["https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.12.0/openapi-generator-cli-7.12.0.jar"], "srcjar_urls": [], "canonical_id": "org.openapitools:openapi-generator-cli:7.12.0", "rule_name": "java_import", "tags": ["maven_coordinates=org.openapitools:openapi-generator-cli:7.12.0"], "artifact_sha256": "33e7dfa7a1f04d58405ee12ae19e2c6fc2a91497cf2e56fa68f1875a95cbf220"}}}, "recordedRepoMappingEntries": [["openapi_tools_generator_bazel+", "bazel_tools", "bazel_tools"]]}}, "@@rules_foreign_cc+//foreign_cc:extensions.bzl%tools": {"general": {"bzlTransitiveDigest": "FApcIcVN43WOEs7g8eg7Cy1hrfRbVNEoUu8IiF+8WOc=", "usagesDigest": "9LXdVp01HkdYQT8gYPjYLO6VLVJHo9uFfxWaU1ymiRE=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"rules_foreign_cc_framework_toolchain_linux": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:linux_commands.bzl", "exec_compatible_with": ["@platforms//os:linux"]}}, "rules_foreign_cc_framework_toolchain_freebsd": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:freebsd_commands.bzl", "exec_compatible_with": ["@platforms//os:freebsd"]}}, "rules_foreign_cc_framework_toolchain_windows": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:windows_commands.bzl", "exec_compatible_with": ["@platforms//os:windows"]}}, "rules_foreign_cc_framework_toolchain_macos": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:macos_commands.bzl", "exec_compatible_with": ["@platforms//os:macos"]}}, "rules_foreign_cc_framework_toolchains": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository_hub", "attributes": {}}, "cmake_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "f316b40053466f9a416adf981efda41b160ca859e97f6a484b447ea299ff26aa", "strip_prefix": "cmake-3.23.2", "urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2.tar.gz"]}}, "gnumake_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "581f4d4e872da74b3941c874215898a7d35802f03732bdccee1d4a7979105d18", "strip_prefix": "make-4.4", "urls": ["https://mirror.bazel.build/ftpmirror.gnu.org/gnu/make/make-4.4.tar.gz", "http://ftpmirror.gnu.org/gnu/make/make-4.4.tar.gz"]}}, "ninja_build_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "31747ae633213f1eda3842686f83c2aa1412e0f5691d1c14dbbcc67fe7400cea", "strip_prefix": "ninja-1.11.1", "urls": ["https://github.com/ninja-build/ninja/archive/v1.11.1.tar.gz"]}}, "meson_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "exports_files([\"meson.py\"])\n\nfilegroup(\n    name = \"runtime\",\n    srcs = glob([\"mesonbuild/**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "strip_prefix": "meson-1.1.1", "url": "https://github.com/mesonbuild/meson/releases/download/1.1.1/meson-1.1.1.tar.gz"}}, "glib_dev": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\nload(\"@rules_cc//cc:defs.bzl\", \"cc_library\")\n\ncc_import(\n    name = \"glib_dev\",\n    hdrs = glob([\"include/**\"]),\n    shared_library = \"@glib_runtime//:bin/libglib-2.0-0.dll\",\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "bdf18506df304d38be98a4b3f18055b8b8cca81beabecad0eece6ce95319c369", "urls": ["https://download.gnome.org/binaries/win64/glib/2.26/glib-dev_2.26.1-1_win64.zip"]}}, "glib_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\ncc_import(\n    name = \"msvc_hdr\",\n    hdrs = [\"msvc_recommended_pragmas.h\"],\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "bc96f63112823b7d6c9f06572d2ad626ddac7eb452c04d762592197f6e07898e", "strip_prefix": "glib-2.26.1", "urls": ["https://download.gnome.org/sources/glib/2.26/glib-2.26.1.tar.gz"]}}, "glib_runtime": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\nexports_files(\n    [\n        \"bin/libgio-2.0-0.dll\",\n        \"bin/libglib-2.0-0.dll\",\n        \"bin/libgmodule-2.0-0.dll\",\n        \"bin/libgobject-2.0-0.dll\",\n        \"bin/libgthread-2.0-0.dll\",\n    ],\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "88d857087e86f16a9be651ee7021880b3f7ba050d34a1ed9f06113b8799cb973", "urls": ["https://download.gnome.org/binaries/win64/glib/2.26/glib_2.26.1-1_win64.zip"]}}, "gettext_runtime": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\ncc_import(\n    name = \"gettext_runtime\",\n    shared_library = \"bin/libintl-8.dll\",\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "1f4269c0e021076d60a54e98da6f978a3195013f6de21674ba0edbc339c5b079", "urls": ["https://download.gnome.org/binaries/win64/dependencies/gettext-runtime_0.18.1.1-2_win64.zip"]}}, "pkgconfig_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "6fc69c01688c9458a57eb9a1664c9aba372ccda420a02bf4429fe610e7e7d591", "strip_prefix": "pkg-config-0.29.2", "patches": ["@@rules_foreign_cc+//toolchains:pkgconfig-detectenv.patch", "@@rules_foreign_cc+//toolchains:pkgconfig-makefile-vc.patch"], "urls": ["https://pkgconfig.freedesktop.org/releases/pkg-config-0.29.2.tar.gz"]}}, "bazel_skylib": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://mirror.bazel.build/github.com/bazelbuild/bazel-skylib/releases/download/1.2.1/bazel-skylib-1.2.1.tar.gz", "https://github.com/bazelbuild/bazel-skylib/releases/download/1.2.1/bazel-skylib-1.2.1.tar.gz"], "sha256": "f7be3474d42aae265405a592bb7da8e171919d74c16f082a5457840f06054728"}}, "rules_python": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "84aec9e21cc56fbc7f1335035a71c850d1b9b5cc6ff497306f84cced9a769841", "strip_prefix": "rules_python-0.23.1", "url": "https://github.com/bazelbuild/rules_python/archive/refs/tags/0.23.1.tar.gz"}}, "cmake-3.23.2-linux-aarch64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-linux-aarch64.tar.gz"], "sha256": "f2654bf780b53f170bbbec44d8ac67d401d24788e590faa53036a89476efa91e", "strip_prefix": "cmake-3.23.2-linux-aarch64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-linux-x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-linux-x86_64.tar.gz"], "sha256": "aaced6f745b86ce853661a595bdac6c5314a60f8181b6912a0a4920acfa32708", "strip_prefix": "cmake-3.23.2-linux-x86_64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-macos-universal": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-macos-universal.tar.gz"], "sha256": "853a0f9af148c5ef47282ffffee06c4c9f257be2635936755f39ca13c3286c88", "strip_prefix": "cmake-3.23.2-macos-universal/CMake.app/Contents", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-windows-i386": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-windows-i386.zip"], "sha256": "6a4fcd6a2315b93cb23c93507efccacc30c449c2bf98f14d6032bb226c582e07", "strip_prefix": "cmake-3.23.2-windows-i386", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake.exe\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-windows-x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-windows-x86_64.zip"], "sha256": "2329387f3166b84c25091c86389fb891193967740c9bcf01e7f6d3306f7ffda0", "strip_prefix": "cmake-3.23.2-windows-x86_64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake.exe\",\n    target = \":cmake_data\",\n)\n"}}, "cmake_3.23.2_toolchains": {"repoRuleId": "@@rules_foreign_cc+//toolchains:prebuilt_toolchains_repository.bzl%prebuilt_toolchains_repository", "attributes": {"repos": {"cmake-3.23.2-linux-aarch64": ["@platforms//cpu:aarch64", "@platforms//os:linux"], "cmake-3.23.2-linux-x86_64": ["@platforms//cpu:x86_64", "@platforms//os:linux"], "cmake-3.23.2-macos-universal": ["@platforms//os:macos"], "cmake-3.23.2-windows-i386": ["@platforms//cpu:x86_32", "@platforms//os:windows"], "cmake-3.23.2-windows-x86_64": ["@platforms//cpu:x86_64", "@platforms//os:windows"]}, "tool": "cmake"}}, "ninja_1.11.1_linux": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-linux.zip"], "sha256": "b901ba96e486dce377f9a070ed4ef3f79deb45f4ffe2938f8e7ddc69cfb3df77", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_mac": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-mac.zip"], "sha256": "482ecb23c59ae3d4f158029112de172dd96bb0e97549c4b1ca32d8fad11f873e", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_win": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-win.zip"], "sha256": "524b344a1a9a55005eaf868d991e090ab8ce07fa109f1820d40e74642e289abc", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja.exe\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_toolchains": {"repoRuleId": "@@rules_foreign_cc+//toolchains:prebuilt_toolchains_repository.bzl%prebuilt_toolchains_repository", "attributes": {"repos": {"ninja_1.11.1_linux": ["@platforms//cpu:x86_64", "@platforms//os:linux"], "ninja_1.11.1_mac": ["@platforms//cpu:x86_64", "@platforms//os:macos"], "ninja_1.11.1_win": ["@platforms//cpu:x86_64", "@platforms//os:windows"]}, "tool": "ninja"}}}, "recordedRepoMappingEntries": [["rules_foreign_cc+", "bazel_tools", "bazel_tools"], ["rules_foreign_cc+", "rules_foreign_cc", "rules_foreign_cc+"]]}}, "@@rules_java+//java:rules_java_deps.bzl%compatibility_proxy": {"general": {"bzlTransitiveDigest": "84xJEZ1jnXXwo8BXMprvBm++rRt4jsTu9liBxz0ivps=", "usagesDigest": "jTQDdLDxsS43zuRmg1faAjIEPWdLAbDAowI1pInQSoo=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"compatibility_proxy": {"repoRuleId": "@@rules_java+//java:rules_java_deps.bzl%_compatibility_proxy_repo_rule", "attributes": {}}}, "recordedRepoMappingEntries": [["rules_java+", "bazel_tools", "bazel_tools"]]}}, "@@rules_kotlin+//src/main/starlark/core/repositories:bzlmod_setup.bzl%rules_kotlin_extensions": {"general": {"bzlTransitiveDigest": "sFhcgPbDQehmbD1EOXzX4H1q/CD5df8zwG4kp4jbvr8=", "usagesDigest": "QI2z8ZUR+mqtbwsf2fLqYdJAkPOHdOV+tF2yVAUgRzw=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_github_jetbrains_kotlin_git": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:compiler.bzl%kotlin_compiler_git_repository", "attributes": {"urls": ["https://github.com/JetBrains/kotlin/releases/download/v1.9.23/kotlin-compiler-1.9.23.zip"], "sha256": "93137d3aab9afa9b27cb06a824c2324195c6b6f6179d8a8653f440f5bd58be88"}}, "com_github_jetbrains_kotlin": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:compiler.bzl%kotlin_capabilities_repository", "attributes": {"git_repository_name": "com_github_jetbrains_kotlin_git", "compiler_version": "1.9.23"}}, "com_github_google_ksp": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:ksp.bzl%ksp_compiler_plugin_repository", "attributes": {"urls": ["https://github.com/google/ksp/releases/download/1.9.23-1.0.20/artifacts.zip"], "sha256": "ee0618755913ef7fd6511288a232e8fad24838b9af6ea73972a76e81053c8c2d", "strip_version": "1.9.23-1.0.20"}}, "com_github_pinterest_ktlint": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"sha256": "01b2e0ef893383a50dbeb13970fe7fa3be36ca3e83259e01649945b09d736985", "urls": ["https://github.com/pinterest/ktlint/releases/download/1.3.0/ktlint"], "executable": true}}, "rules_android": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "cd06d15dd8bb59926e4d65f9003bfc20f9da4b2519985c27e190cddc8b7a7806", "strip_prefix": "rules_android-0.1.1", "urls": ["https://github.com/bazelbuild/rules_android/archive/v0.1.1.zip"]}}}, "recordedRepoMappingEntries": [["rules_kotlin+", "bazel_tools", "bazel_tools"]]}}, "@@rules_multitool+//multitool:extension.bzl%multitool": {"general": {"bzlTransitiveDigest": "3oboZoLHhLmg8U9CjjVdu2NxgtEU/AqvsLxgGAIZrOw=", "usagesDigest": "fHRbucC+Ne0eWB/8JmwHEh1f6Em1PxZrkFloRM0oh1M=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"multitool.linux_arm64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "linux", "cpu": "arm64"}}, "multitool.linux_x86_64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "linux", "cpu": "x86_64"}}, "multitool.macos_arm64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "macos", "cpu": "arm64"}}, "multitool.macos_x86_64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "macos", "cpu": "x86_64"}}, "multitool.windows_arm64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "windows", "cpu": "arm64"}}, "multitool.windows_x86_64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "windows", "cpu": "x86_64"}}, "multitool": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_multitool_hub", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"]}}}, "recordedRepoMappingEntries": [["bazel_features+", "bazel_features_globals", "bazel_features++version_extension+bazel_features_globals"], ["bazel_features+", "bazel_features_version", "bazel_features++version_extension+bazel_features_version"], ["rules_multitool+", "bazel_features", "bazel_features+"]]}}, "@@rules_nodejs+//nodejs:extensions.bzl%node": {"general": {"bzlTransitiveDigest": "6mH+d9usKSnfYPXj6umbdS1DJq28sikqbQWLllnmceE=", "usagesDigest": "SXzq0nR6fVanChlwZ3J/YI9YIVeHVYRnII3pwS0b9zk=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"nodejs_linux_amd64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.11.0", "include_headers": false, "platform": "linux_amd64"}}, "nodejs_linux_arm64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.11.0", "include_headers": false, "platform": "linux_arm64"}}, "nodejs_linux_s390x": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.11.0", "include_headers": false, "platform": "linux_s390x"}}, "nodejs_linux_ppc64le": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.11.0", "include_headers": false, "platform": "linux_ppc64le"}}, "nodejs_darwin_amd64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.11.0", "include_headers": false, "platform": "darwin_amd64"}}, "nodejs_darwin_arm64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.11.0", "include_headers": false, "platform": "darwin_arm64"}}, "nodejs_windows_amd64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.11.0", "include_headers": false, "platform": "windows_amd64"}}, "nodejs": {"repoRuleId": "@@rules_nodejs+//nodejs/private:nodejs_repo_host_os_alias.bzl%nodejs_repo_host_os_alias", "attributes": {"user_node_repository_name": "nodejs"}}, "nodejs_host": {"repoRuleId": "@@rules_nodejs+//nodejs/private:nodejs_repo_host_os_alias.bzl%nodejs_repo_host_os_alias", "attributes": {"user_node_repository_name": "nodejs"}}, "nodejs_toolchains": {"repoRuleId": "@@rules_nodejs+//nodejs/private:nodejs_toolchains_repo.bzl%nodejs_toolchains_repo", "attributes": {"user_node_repository_name": "nodejs"}}}, "recordedRepoMappingEntries": []}}, "@@rules_oci+//oci:extensions.bzl%oci": {"general": {"bzlTransitiveDigest": "YDlk1fPnlHbKEYsmKy4WSWzUgoLXLjuslR435yz/oRA=", "usagesDigest": "E/A7/GULnbBw3mQP+gC7+EG4SNFuDL2ebIopGl3+Lz8=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"go_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"scheme": "https", "registry": "gcr.io", "repository": "distroless/base", "identifier": "debug", "platform": "linux/amd64", "target_name": "go_base_image_linux_amd64", "bazel_tags": []}}, "go_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "go_base_image", "scheme": "https", "registry": "gcr.io", "repository": "distroless/base", "identifier": "debug", "platforms": {"@@platforms//cpu:x86_64": "@go_base_image_linux_amd64"}, "bzlmod_repository": "go_base_image", "reproducible": true}}, "python_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"scheme": "https", "registry": "index.docker.io", "repository": "library/ubuntu", "identifier": "22.04", "platform": "linux/amd64", "target_name": "python_base_image_linux_amd64", "bazel_tags": []}}, "python_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "python_base_image", "scheme": "https", "registry": "index.docker.io", "repository": "library/ubuntu", "identifier": "22.04", "platforms": {"@@platforms//cpu:x86_64": "@python_base_image_linux_amd64"}, "bzlmod_repository": "python_base_image", "reproducible": true}}, "node_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"scheme": "https", "registry": "us-west1-docker.pkg.dev", "repository": "sentio-352722/sentio/node", "identifier": "22-1.1.1", "platform": "linux/amd64", "target_name": "node_base_image_linux_amd64", "bazel_tags": []}}, "node_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "node_base_image", "scheme": "https", "registry": "us-west1-docker.pkg.dev", "repository": "sentio-352722/sentio/node", "identifier": "22-1.1.1", "platforms": {"@@platforms//cpu:x86_64": "@node_base_image_linux_amd64"}, "bzlmod_repository": "node_base_image", "reproducible": true}}, "clickhouse_client_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"scheme": "https", "registry": "index.docker.io", "repository": "clickhouse/clickhouse-server", "identifier": "head-alpine", "platform": "linux/amd64", "target_name": "clickhouse_client_image_linux_amd64", "bazel_tags": []}}, "clickhouse_client_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "clickhouse_client_image", "scheme": "https", "registry": "index.docker.io", "repository": "clickhouse/clickhouse-server", "identifier": "head-alpine", "platforms": {"@@platforms//cpu:x86_64": "@clickhouse_client_image_linux_amd64"}, "bzlmod_repository": "clickhouse_client_image", "reproducible": true}}, "move_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"scheme": "https", "registry": "index.docker.io", "repository": "google/cloud-sdk", "identifier": "slim", "platform": "linux/amd64", "target_name": "move_base_image_linux_amd64", "bazel_tags": []}}, "move_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "move_base_image", "scheme": "https", "registry": "index.docker.io", "repository": "google/cloud-sdk", "identifier": "slim", "platforms": {"@@platforms//cpu:x86_64": "@move_base_image_linux_amd64"}, "bzlmod_repository": "move_base_image", "reproducible": true}}, "bazel_features_version": {"repoRuleId": "@@bazel_features+//private:version_repo.bzl%version_repo", "attributes": {}}, "bazel_features_globals": {"repoRuleId": "@@bazel_features+//private:globals_repo.bzl%globals_repo", "attributes": {"globals": {"CcSharedLibraryInfo": "6.0.0-pre.20220630.1", "CcSharedLibraryHintInfo": "7.0.0-pre.20230316.2", "PackageSpecificationInfo": "6.4.0", "RunEnvironmentInfo": "5.3.0", "DefaultInfo": "0.0.1", "__TestingOnly_NeverAvailable": "**********.0.0"}, "legacy_globals": {"JavaInfo": "8.0.0", "JavaPluginInfo": "8.0.0", "ProtoInfo": "8.0.0", "PyCcLinkParamsProvider": "8.0.0", "PyInfo": "8.0.0", "PyRuntimeInfo": "8.0.0"}}}, "bazel_skylib": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "9f38886a40548c6e96c106b752f242130ee11aaa068a56ba7e56f4511f33e4f2", "urls": ["https://mirror.bazel.build/github.com/bazelbuild/bazel-skylib/releases/download/1.6.1/bazel-skylib-1.6.1.tar.gz", "https://github.com/bazelbuild/bazel-skylib/releases/download/1.6.1/bazel-skylib-1.6.1.tar.gz"]}}, "jq_darwin_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:jq_toolchain.bzl%jq_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "1.7"}}, "jq_darwin_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:jq_toolchain.bzl%jq_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "1.7"}}, "jq_linux_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:jq_toolchain.bzl%jq_platform_repo", "attributes": {"platform": "linux_amd64", "version": "1.7"}}, "jq_linux_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:jq_toolchain.bzl%jq_platform_repo", "attributes": {"platform": "linux_arm64", "version": "1.7"}}, "jq_windows_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:jq_toolchain.bzl%jq_platform_repo", "attributes": {"platform": "windows_amd64", "version": "1.7"}}, "jq": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:jq_toolchain.bzl%jq_host_alias_repo", "attributes": {}}, "jq_toolchains": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:jq_toolchain.bzl%jq_toolchains_repo", "attributes": {"user_repository_name": "jq"}}, "bsd_tar_darwin_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "bsd_tar_darwin_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "bsd_tar_linux_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "linux_amd64"}}, "bsd_tar_linux_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "linux_arm64"}}, "bsd_tar_windows_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "windows_amd64"}}, "bsd_tar_toolchains": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%tar_toolchains_repo", "attributes": {"user_repository_name": "bsd_tar"}}, "coreutils_darwin_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:coreutils_toolchain.bzl%coreutils_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "0.0.27"}}, "coreutils_darwin_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:coreutils_toolchain.bzl%coreutils_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "0.0.27"}}, "coreutils_linux_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:coreutils_toolchain.bzl%coreutils_platform_repo", "attributes": {"platform": "linux_amd64", "version": "0.0.27"}}, "coreutils_linux_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:coreutils_toolchain.bzl%coreutils_platform_repo", "attributes": {"platform": "linux_arm64", "version": "0.0.27"}}, "coreutils_windows_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:coreutils_toolchain.bzl%coreutils_platform_repo", "attributes": {"platform": "windows_amd64", "version": "0.0.27"}}, "coreutils_toolchains": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:coreutils_toolchain.bzl%coreutils_toolchains_repo", "attributes": {"user_repository_name": "coreutils"}}, "copy_to_directory_darwin_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_platform_repo", "attributes": {"platform": "darwin_amd64"}}, "copy_to_directory_darwin_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_platform_repo", "attributes": {"platform": "darwin_arm64"}}, "copy_to_directory_freebsd_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_platform_repo", "attributes": {"platform": "freebsd_amd64"}}, "copy_to_directory_linux_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_platform_repo", "attributes": {"platform": "linux_amd64"}}, "copy_to_directory_linux_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_platform_repo", "attributes": {"platform": "linux_arm64"}}, "copy_to_directory_linux_s390x": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_platform_repo", "attributes": {"platform": "linux_s390x"}}, "copy_to_directory_windows_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_platform_repo", "attributes": {"platform": "windows_amd64"}}, "copy_to_directory_toolchains": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:copy_to_directory_toolchain.bzl%copy_to_directory_toolchains_repo", "attributes": {"user_repository_name": "copy_to_directory"}}, "zstd_darwin_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:zstd_toolchain.bzl%zstd_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "zstd_darwin_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:zstd_toolchain.bzl%zstd_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "zstd_linux_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:zstd_toolchain.bzl%zstd_binary_repo", "attributes": {"platform": "linux_amd64"}}, "zstd_linux_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:zstd_toolchain.bzl%zstd_binary_repo", "attributes": {"platform": "linux_arm64"}}, "zstd_toolchains": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:zstd_toolchain.bzl%zstd_toolchains_repo", "attributes": {"user_repository_name": "zstd"}}, "oci_crane_darwin_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "darwin_amd64", "crane_version": "v0.18.0"}}, "oci_crane_darwin_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "darwin_arm64", "crane_version": "v0.18.0"}}, "oci_crane_linux_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_arm64", "crane_version": "v0.18.0"}}, "oci_crane_linux_armv6": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_armv6", "crane_version": "v0.18.0"}}, "oci_crane_linux_i386": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_i386", "crane_version": "v0.18.0"}}, "oci_crane_linux_s390x": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_s390x", "crane_version": "v0.18.0"}}, "oci_crane_linux_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_amd64", "crane_version": "v0.18.0"}}, "oci_crane_windows_armv6": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "windows_armv6", "crane_version": "v0.18.0"}}, "oci_crane_windows_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "windows_amd64", "crane_version": "v0.18.0"}}, "oci_crane_toolchains": {"repoRuleId": "@@rules_oci+//oci/private:toolchains_repo.bzl%toolchains_repo", "attributes": {"toolchain_type": "@rules_oci//oci:crane_toolchain_type", "toolchain": "@oci_crane_{platform}//:crane_toolchain"}}, "oci_regctl_darwin_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "darwin_amd64"}}, "oci_regctl_darwin_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "darwin_arm64"}}, "oci_regctl_linux_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "linux_arm64"}}, "oci_regctl_linux_s390x": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "linux_s390x"}}, "oci_regctl_linux_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "linux_amd64"}}, "oci_regctl_windows_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "windows_amd64"}}, "oci_regctl_toolchains": {"repoRuleId": "@@rules_oci+//oci/private:toolchains_repo.bzl%toolchains_repo", "attributes": {"toolchain_type": "@rules_oci//oci:regctl_toolchain_type", "toolchain": "@oci_regctl_{platform}//:regctl_toolchain"}}}, "moduleExtensionMetadata": {"explicitRootModuleDirectDeps": ["go_base_image", "go_base_image_linux_amd64", "python_base_image", "python_base_image_linux_amd64", "node_base_image", "node_base_image_linux_amd64", "clickhouse_client_image", "clickhouse_client_image_linux_amd64", "move_base_image", "move_base_image_linux_amd64"], "explicitRootModuleDirectDevDeps": [], "useAllRepos": "NO", "reproducible": false}, "recordedRepoMappingEntries": [["aspect_bazel_lib+", "bazel_tools", "bazel_tools"], ["bazel_features+", "bazel_tools", "bazel_tools"], ["rules_oci+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["rules_oci+", "bazel_features", "bazel_features+"], ["rules_oci+", "bazel_skylib", "bazel_skylib+"]]}}, "@@rules_proto_grpc_grpc_gateway+//:module_extensions.bzl%download_plugins": {"general": {"bzlTransitiveDigest": "kXkHAXk639Wjh9pho0V/vHgXAwqap57TokMNsuhfulw=", "usagesDigest": "SAVBhlI5cUPQRQrVj2z1TD66zkr/YpJm2shRU2/fHJo=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"grpc_gateway_plugin_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "8fdc478fddc691771bcaeb2db2d5d8a7b02abd66eff37b5b585047f244d13a81", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-darwin-arm64"}}, "grpc_gateway_plugin_darwin_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "448b136143017da93ad6ed0ad6aad40a76bfcd47a443362516c31eaf1fd7bcdb", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-darwin-x86_64"}}, "grpc_gateway_plugin_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "05a34c88c48f25c0bfccc5548cd522188f6a09ae839dc442eaefd18a83420b4b", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-linux-arm64"}}, "grpc_gateway_plugin_linux_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "9435a60c0ad0f9d535cc28998087e43ebf54fb87f491408752ddec3e89a3fdf3", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-linux-x86_64"}}, "grpc_gateway_plugin_windows_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "e79caf845fe8cb4ad534281ba32c1b607ef64e50f94bb278d7d9514541efad2b", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-windows-arm64.exe"}}, "grpc_gateway_plugin_windows_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "25cfbcfc9c555145e373a85cc0dfc5eaef6c9df49c556e82f526fac51070f6d6", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-windows-x86_64.exe"}}, "openapiv2_plugin_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "ff82e0513c99fcef2ddfc4432092bcb8fb770086bedb166811fd4c60f1b4f950", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-darwin-arm64"}}, "openapiv2_plugin_darwin_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "4903651de013d031c33976730b2f91f82dbe116ed91af7dcc718656809ff8a9a", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-darwin-x86_64"}}, "openapiv2_plugin_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "92ca757d3be792b0164b07606d6b69ccd3f0f6d765c6c38c01503c72ae51dfbc", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-linux-arm64"}}, "openapiv2_plugin_linux_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "d17ed6eb57ba2df1fef60a60c2bbce1bd47a05152ce54666cb9333d5c35792b2", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-linux-x86_64"}}, "openapiv2_plugin_windows_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "d456387c82d37322408b2c53f0d587659e90ff7645fa62d83571d913135dc08a", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-windows-arm64.exe"}}, "openapiv2_plugin_windows_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "1d2c9687cd90a58872a664691e07f4d99fb1625de6b92a60bdb5058614248fcb", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-windows-x86_64.exe"}}}, "moduleExtensionMetadata": {"useAllRepos": "REGULAR", "reproducible": false}, "recordedRepoMappingEntries": [["rules_proto_grpc_grpc_gateway+", "bazel_tools", "bazel_tools"]]}}, "@@rules_python+//python/extensions:pip.bzl%pip": {"general": {"bzlTransitiveDigest": "ypBYcZ0XstlL/WxmhInk0VPop1m22d0tJwtEWFIMPp0=", "usagesDigest": "lcMeihWRIbHvBp7HED3+KlPqkO0qlak9Vqf0GHIa2TU=", "recordedFileInputs": {"@@//requirements.txt": "9220b7130814f0b9b7bc0c656cba04c55c6cdd30a106ba0d1f8f99761b81935d", "@@grpc+//requirements.bazel.txt": "c43a56443ad71bd3b700cc3f4e889e1fd7516c2deddb6e9b75f40194bc92bf1c", "@@protobuf+//python/requirements.txt": "983be60d3cec4b319dcab6d48aeb3f5b2f7c3350f26b3a9e97486c37967c73c5", "@@rules_fuzzing+//fuzzing/requirements.txt": "ab04664be026b632a0d2a2446c4f65982b7654f5b6851d2f9d399a19b7242a5b", "@@rules_proto_grpc_python+//requirements.txt": "70b3976511773cef8594b6d6cd64d3f468a8502d55c7dcdef9f989f4892d94a2", "@@rules_python+//tools/publish/requirements_darwin.txt": "2994136eab7e57b083c3de76faf46f70fad130bc8e7360a7fed2b288b69e79dc", "@@rules_python+//tools/publish/requirements_linux.txt": "8175b4c8df50ae2f22d1706961884beeb54e7da27bd2447018314a175981997d", "@@rules_python+//tools/publish/requirements_windows.txt": "7673adc71dc1a81d3661b90924d7a7c0fc998cd508b3cb4174337cef3f2de556"}, "recordedDirentsInputs": {}, "envVariables": {"RULES_PYTHON_REPO_DEBUG": null, "RULES_PYTHON_REPO_DEBUG_VERBOSITY": null}, "generatedRepoSpecs": {"grpc_python_dependencies_310_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_310_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_310_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_310_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_310_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_310_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_310_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_310_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_310_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_310_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_310_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_310_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_310_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_310_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "googleapis-common-protos==1.61.0"}}, "grpc_python_dependencies_310_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_310_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "idna==2.7"}}, "grpc_python_dependencies_310_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_310_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_310_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_310_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_310_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_310_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_310_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_310_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-semantic-conventions==0.42b0"}}, "grpc_python_dependencies_310_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_310_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_310_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "protobuf>=3.5.0.post1, < 4.0dev"}}, "grpc_python_dependencies_310_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_310_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_310_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_310_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_310_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_310_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_310_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_310_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_310_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_310_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_310_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_310_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_311_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_311_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_311_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_311_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_311_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_311_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_311_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_311_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_311_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_311_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_311_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_311_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_311_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_311_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "googleapis-common-protos==1.61.0"}}, "grpc_python_dependencies_311_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_311_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "idna==2.7"}}, "grpc_python_dependencies_311_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_311_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_311_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_311_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_311_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_311_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_311_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_311_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-semantic-conventions==0.42b0"}}, "grpc_python_dependencies_311_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_311_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_311_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "protobuf>=3.5.0.post1, < 4.0dev"}}, "grpc_python_dependencies_311_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_311_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_311_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_311_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_311_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_311_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_311_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_311_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_311_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_311_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_311_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_311_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_312_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_312_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_312_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_312_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_312_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_312_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_312_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_312_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_312_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_312_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_312_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_312_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_312_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_312_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "googleapis-common-protos==1.61.0"}}, "grpc_python_dependencies_312_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_312_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "idna==2.7"}}, "grpc_python_dependencies_312_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_312_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_312_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_312_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_312_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_312_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_312_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_312_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-semantic-conventions==0.42b0"}}, "grpc_python_dependencies_312_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_312_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_312_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "protobuf>=3.5.0.post1, < 4.0dev"}}, "grpc_python_dependencies_312_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_312_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_312_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_312_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_312_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_312_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_312_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_312_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_312_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_312_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_312_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_312_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_38_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_38_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_38_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_38_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_38_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_38_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_38_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_38_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_38_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_38_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_38_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_38_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_38_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_38_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "googleapis-common-protos==1.61.0"}}, "grpc_python_dependencies_38_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_38_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "idna==2.7"}}, "grpc_python_dependencies_38_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_38_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_38_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_38_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_38_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_38_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_38_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_38_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "opentelemetry-semantic-conventions==0.42b0"}}, "grpc_python_dependencies_38_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_38_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_38_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "protobuf>=3.5.0.post1, < 4.0dev"}}, "grpc_python_dependencies_38_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_38_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_38_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_38_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_38_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_38_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_38_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_38_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_38_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_38_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_38_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_38_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "grpc_python_dependencies_38", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_39_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_39_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_39_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_39_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_39_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_39_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_39_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_39_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_39_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_39_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_39_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_39_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_39_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_39_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "googleapis-common-protos==1.61.0"}}, "grpc_python_dependencies_39_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_39_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "idna==2.7"}}, "grpc_python_dependencies_39_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_39_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_39_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_39_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_39_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_39_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_39_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_39_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-semantic-conventions==0.42b0"}}, "grpc_python_dependencies_39_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_39_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_39_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "protobuf>=3.5.0.post1, < 4.0dev"}}, "grpc_python_dependencies_39_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_39_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_39_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_39_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_39_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_39_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_39_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_39_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_39_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_39_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_39_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_39_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "zope.interface==6.1"}}, "pip_311_aiohttp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "aiohttp==3.8.5"}}, "pip_311_aiosignal": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "aiosignal==1.3.1"}}, "pip_311_annotated_types": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "annotated_types==0.7.0"}}, "pip_311_anthropic": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "anthropic==0.46.0"}}, "pip_311_anyio": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "anyio>=4.5.0"}}, "pip_311_async_timeout": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "async-timeout==4.0.2"}}, "pip_311_attrs": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "attrs==23.1.0"}}, "pip_311_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "certifi==2023.7.22"}}, "pip_311_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "charset-normalizer==3.2.0"}}, "pip_311_click": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "click>=7.0"}}, "pip_311_dataclasses_json": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "dataclasses-json==0.5.14"}}, "pip_311_distro": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "distro==1.9.0"}}, "pip_311_dnspython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "dnspython==2.4.2"}}, "pip_311_elastic_transport": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "elastic_transport==8.4.0"}}, "pip_311_elasticsearch": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "elasticsearch==8.10.0"}}, "pip_311_exceptiongroup": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "exceptiongroup==1.1.3"}}, "pip_311_frozenlist": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "frozenlist==1.4.0"}}, "pip_311_gitdb": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "gitdb==4.0.10"}}, "pip_311_gitpython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "GitPython==3.1.32"}}, "pip_311_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "greenlet==2.0.2"}}, "pip_311_h11": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "h11>=0.16.0"}}, "pip_311_httpcore": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "httpcore>=1.0.0,<2.0.0"}}, "pip_311_httpx": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "httpx>=0.27.0"}}, "pip_311_httpx_sse": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "httpx_sse==0.4.0"}}, "pip_311_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "idna==3.4"}}, "pip_311_jiter": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "jiter==0.8.2"}}, "pip_311_jsonpatch": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "jsonpatch==1.33"}}, "pip_311_jsonpointer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "jsonpointer>=1.9"}}, "pip_311_langchain": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langchain==0.3.12"}}, "pip_311_langchain_anthropic": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langchain-anthropic==0.3.10"}}, "pip_311_langchain_community": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langchain_community==0.3.12"}}, "pip_311_langchain_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langchain_core==0.3.49"}}, "pip_311_langchain_mcp_adapters": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langchain-mcp-adapters>=0.1.0"}}, "pip_311_langchain_openai": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langchain_openai==0.3.11"}}, "pip_311_langchain_text_splitters": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langchain_text_splitters==0.3.3"}}, "pip_311_langgraph": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langgraph==0.3.34"}}, "pip_311_langgraph_checkpoint": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langgraph-checkpoint>=2.0.10,<3.0.0"}}, "pip_311_langgraph_prebuilt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langgraph-prebuilt>=0.1.8,<0.2"}}, "pip_311_langgraph_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langgraph-sdk>=0.1.42,<0.2.0"}}, "pip_311_langsmith": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "langsmith==0.2.3"}}, "pip_311_loguru": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "loguru==0.7.0"}}, "pip_311_marshmallow": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "marshmallow==3.20.1"}}, "pip_311_mcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "mcp>=1.7"}}, "pip_311_mmh3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "mmh3==5.0.1"}}, "pip_311_multidict": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "multidict==6.0.4"}}, "pip_311_mypy_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "mypy-extensions==1.0.0"}}, "pip_311_numexpr": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "numexpr==2.8.4"}}, "pip_311_numpy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "numpy==1.24.3"}}, "pip_311_openai": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "openai==1.68.2"}}, "pip_311_orjson": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "orjson==3.10.0"}}, "pip_311_ormsgpack": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "ormsgpack>=1.8.0,<2.0.0"}}, "pip_311_packaging": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "packaging==23.2"}}, "pip_311_pydantic": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "pydantic==2.10.6"}}, "pip_311_pydantic_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "pydantic_core==2.27.2"}}, "pip_311_pydantic_settings": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "pydantic_settings>=2.5.2"}}, "pip_311_python_dateutil": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "python_dateutil==2.8.2"}}, "pip_311_python_dotenv": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "python_dotenv==1.0.0"}}, "pip_311_python_multipart": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "python-multipart>=0.0.9"}}, "pip_311_python_telegram_bot": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "python-telegram-bot==20.4"}}, "pip_311_pyyaml": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "PyYAML==6.0.1"}}, "pip_311_redis": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "redis==5.2.1"}}, "pip_311_regex": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "regex==2023.8.8"}}, "pip_311_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "requests==2.31.0"}}, "pip_311_requests_toolbelt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "requests_toolbelt==1.0.0"}}, "pip_311_scipy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "scipy==1.10.1"}}, "pip_311_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "six==1.16.0"}}, "pip_311_smmap": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "smmap==5.0.0"}}, "pip_311_sniffio": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "sniffio==1.3.0"}}, "pip_311_sqlalchemy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "SQLAlchemy==2.0.19"}}, "pip_311_sse_starlette": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "sse-starlette>=1.6.1"}}, "pip_311_starlette": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "starlette>=0.27"}}, "pip_311_tenacity": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "tenacity==8.2.2"}}, "pip_311_tiktoken": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "tiktoken==0.9.0"}}, "pip_311_tqdm": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "tqdm==4.65.0"}}, "pip_311_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "typing_extensions==4.12.2"}}, "pip_311_typing_inspect": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "typing-inspect==0.9.0"}}, "pip_311_typing_inspection": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "typing-inspection>=0.4.0"}}, "pip_311_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "urllib3==1.26.2"}}, "pip_311_uvicorn": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "uvicorn>=0.23.1"}}, "pip_311_xxhash": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "xxhash>=3.5.0,<4.0.0"}}, "pip_311_yarl": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_311", "requirement": "yarl==1.9.2"}}, "pip_deps_310_numpy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pip_deps_310", "requirement": "numpy<=1.26.1"}}, "pip_deps_310_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pip_deps_310", "requirement": "setuptools<=70.3.0"}}, "pip_deps_311_numpy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_deps_311", "requirement": "numpy<=1.26.1"}}, "pip_deps_311_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pip_deps_311", "requirement": "setuptools<=70.3.0"}}, "pip_deps_312_numpy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pip_deps_312", "requirement": "numpy<=1.26.1"}}, "pip_deps_312_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pip_deps_312", "requirement": "setuptools<=70.3.0"}}, "pip_deps_38_numpy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "pip_deps_38", "requirement": "numpy<=1.26.1"}}, "pip_deps_38_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "pip_deps_38", "requirement": "setuptools<=70.3.0"}}, "pip_deps_39_numpy": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pip_deps_39", "requirement": "numpy<=1.26.1"}}, "pip_deps_39_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pip_deps_39", "requirement": "setuptools<=70.3.0"}}, "rules_fuzzing_py_deps_310_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_fuzzing_py_deps_310", "requirement": "absl-py==2.0.0 --hash=sha256:9a28abb62774ae4e8edbe2dd4c49ffcd45a6a848952a5eccc6a49f3f0fc1e2f3"}}, "rules_fuzzing_py_deps_310_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_fuzzing_py_deps_310", "requirement": "six==1.16.0 --hash=sha256:8abb2f1d86890a2dfb989f9a77cfcfd3e47c2a354b01111771326f8aa26e0254"}}, "rules_fuzzing_py_deps_311_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_fuzzing_py_deps_311", "requirement": "absl-py==2.0.0 --hash=sha256:9a28abb62774ae4e8edbe2dd4c49ffcd45a6a848952a5eccc6a49f3f0fc1e2f3"}}, "rules_fuzzing_py_deps_311_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_fuzzing_py_deps_311", "requirement": "six==1.16.0 --hash=sha256:8abb2f1d86890a2dfb989f9a77cfcfd3e47c2a354b01111771326f8aa26e0254"}}, "rules_fuzzing_py_deps_312_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_fuzzing_py_deps_312", "requirement": "absl-py==2.0.0 --hash=sha256:9a28abb62774ae4e8edbe2dd4c49ffcd45a6a848952a5eccc6a49f3f0fc1e2f3"}}, "rules_fuzzing_py_deps_312_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_fuzzing_py_deps_312", "requirement": "six==1.16.0 --hash=sha256:8abb2f1d86890a2dfb989f9a77cfcfd3e47c2a354b01111771326f8aa26e0254"}}, "rules_fuzzing_py_deps_38_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_fuzzing_py_deps_38", "requirement": "absl-py==2.0.0 --hash=sha256:9a28abb62774ae4e8edbe2dd4c49ffcd45a6a848952a5eccc6a49f3f0fc1e2f3"}}, "rules_fuzzing_py_deps_38_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_fuzzing_py_deps_38", "requirement": "six==1.16.0 --hash=sha256:8abb2f1d86890a2dfb989f9a77cfcfd3e47c2a354b01111771326f8aa26e0254"}}, "rules_fuzzing_py_deps_39_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_fuzzing_py_deps_39", "requirement": "absl-py==2.0.0 --hash=sha256:9a28abb62774ae4e8edbe2dd4c49ffcd45a6a848952a5eccc6a49f3f0fc1e2f3"}}, "rules_fuzzing_py_deps_39_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_fuzzing_py_deps//{name}:{target}", "extra_pip_args": ["--require-hashes"], "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_fuzzing_py_deps_39", "requirement": "six==1.16.0 --hash=sha256:8abb2f1d86890a2dfb989f9a77cfcfd3e47c2a354b01111771326f8aa26e0254"}}, "rules_proto_grpc_python_pip_deps_310_grpcio": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_proto_grpc_python_pip_deps_310", "requirement": "grpcio==1.64.1     --hash=sha256:03b43d0ccf99c557ec671c7dede64f023c7da9bb632ac65dbc57f166e4970040     --hash=sha256:0a12ddb1678ebc6a84ec6b0487feac020ee2b1659cbe69b80f06dbffdb249122     --hash=sha256:0a2813093ddb27418a4c99f9b1c223fab0b053157176a64cc9db0f4557b69bd9     --hash=sha256:0cc79c982ccb2feec8aad0e8fb0d168bcbca85bc77b080d0d3c5f2f15c24ea8f     --hash=sha256:1257b76748612aca0f89beec7fa0615727fd6f2a1ad580a9638816a4b2eb18fd     --hash=sha256:1262402af5a511c245c3ae918167eca57342c72320dffae5d9b51840c4b2f86d     --hash=sha256:19264fc964576ddb065368cae953f8d0514ecc6cb3da8903766d9fb9d4554c33     --hash=sha256:198908f9b22e2672a998870355e226a725aeab327ac4e6ff3a1399792ece4762     --hash=sha256:1de403fc1305fd96cfa75e83be3dee8538f2413a6b1685b8452301c7ba33c294     --hash=sha256:20405cb8b13fd779135df23fabadc53b86522d0f1cba8cca0e87968587f50650     --hash=sha256:2981c7365a9353f9b5c864595c510c983251b1ab403e05b1ccc70a3d9541a73b     --hash=sha256:2c3c1b90ab93fed424e454e93c0ed0b9d552bdf1b0929712b094f5ecfe7a23ad     --hash=sha256:39b9d0acaa8d835a6566c640f48b50054f422d03e77e49716d4c4e8e279665a1     --hash=sha256:3b64ae304c175671efdaa7ec9ae2cc36996b681eb63ca39c464958396697daff     --hash=sha256:4657d24c8063e6095f850b68f2d1ba3b39f2b287a38242dcabc166453e950c59     --hash=sha256:4d6dab6124225496010bd22690f2d9bd35c7cbb267b3f14e7a3eb05c911325d4     --hash=sha256:55260032b95c49bee69a423c2f5365baa9369d2f7d233e933564d8a47b893027     --hash=sha256:55697ecec192bc3f2f3cc13a295ab670f51de29884ca9ae6cd6247df55df2502     --hash=sha256:5841dd1f284bd1b3d8a6eca3a7f062b06f1eec09b184397e1d1d43447e89a7ae     --hash=sha256:58b1041e7c870bb30ee41d3090cbd6f0851f30ae4eb68228955d973d3efa2e61     --hash=sha256:5e42634a989c3aa6049f132266faf6b949ec2a6f7d302dbb5c15395b77d757eb     --hash=sha256:5e56462b05a6f860b72f0fa50dca06d5b26543a4e88d0396259a07dc30f4e5aa     --hash=sha256:5f8b75f64d5d324c565b263c67dbe4f0af595635bbdd93bb1a88189fc62ed2e5     --hash=sha256:62b4e6eb7bf901719fce0ca83e3ed474ae5022bb3827b0a501e056458c51c0a1     --hash=sha256:6503b64c8b2dfad299749cad1b595c650c91e5b2c8a1b775380fcf8d2cbba1e9     --hash=sha256:6c024ffc22d6dc59000faf8ad781696d81e8e38f4078cb0f2630b4a3cf231a90     --hash=sha256:73819689c169417a4f978e562d24f2def2be75739c4bed1992435d007819da1b     --hash=sha256:75dbbf415026d2862192fe1b28d71f209e2fd87079d98470db90bebe57b33179     --hash=sha256:8caee47e970b92b3dd948371230fcceb80d3f2277b3bf7fbd7c0564e7d39068e     --hash=sha256:8d51dd1c59d5fa0f34266b80a3805ec29a1f26425c2a54736133f6d87fc4968a     --hash=sha256:940e3ec884520155f68a3b712d045e077d61c520a195d1a5932c531f11883489     --hash=sha256:a011ac6c03cfe162ff2b727bcb530567826cec85eb8d4ad2bfb4bd023287a52d     --hash=sha256:a3a035c37ce7565b8f4f35ff683a4db34d24e53dc487e47438e434eb3f701b2a     --hash=sha256:a5e771d0252e871ce194d0fdcafd13971f1aae0ddacc5f25615030d5df55c3a2     --hash=sha256:ac15b6c2c80a4d1338b04d42a02d376a53395ddf0ec9ab157cbaf44191f3ffdd     --hash=sha256:b1a82e0b9b3022799c336e1fc0f6210adc019ae84efb7321d668129d28ee1efb     --hash=sha256:bac71b4b28bc9af61efcdc7630b166440bbfbaa80940c9a697271b5e1dabbc61     --hash=sha256:bbc5b1d78a7822b0a84c6f8917faa986c1a744e65d762ef6d8be9d75677af2ca     --hash=sha256:c1a786ac592b47573a5bb7e35665c08064a5d77ab88a076eec11f8ae86b3e3f6     --hash=sha256:c84ad903d0d94311a2b7eea608da163dace97c5fe9412ea311e72c3684925602     --hash=sha256:d4d29cc612e1332237877dfa7fe687157973aab1d63bd0f84cf06692f04c0367     --hash=sha256:e3d9f8d1221baa0ced7ec7322a981e28deb23749c76eeeb3d33e18b72935ab62     --hash=sha256:e7cd5c1325f6808b8ae31657d281aadb2a51ac11ab081ae335f4f7fc44c1721d     --hash=sha256:ed6091fa0adcc7e4ff944090cf203a52da35c37a130efa564ded02b7aff63bcd     --hash=sha256:ee73a2f5ca4ba44fa33b4d7d2c71e2c8a9e9f78d53f6507ad68e7d2ad5f64a22     --hash=sha256:f10193c69fc9d3d726e83bbf0f3d316f1847c3071c8c93d8090cf5f326b14309"}}, "rules_proto_grpc_python_pip_deps_310_grpclib": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_proto_grpc_python_pip_deps_310", "requirement": "grpclib==0.4.7     --hash=sha256:2988ef57c02b22b7a2e8e961792c41ccf97efc2ace91ae7a5b0de03c363823c3"}}, "rules_proto_grpc_python_pip_deps_310_h2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_proto_grpc_python_pip_deps_310", "requirement": "h2==4.1.0     --hash=sha256:03a46bcf682256c95b5fd9e9a99c1323584c3eec6440d379b9903d709476bc6d     --hash=sha256:a83aca08fbe7aacb79fec788c9c0bac936343560ed9ec18b82a13a12c28d2abb"}}, "rules_proto_grpc_python_pip_deps_310_hpack": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_proto_grpc_python_pip_deps_310", "requirement": "hpack==4.0.0     --hash=sha256:84a076fad3dc9a9f8063ccb8041ef100867b1878b25ef0ee63847a5d53818a6c     --hash=sha256:fc41de0c63e687ebffde81187a948221294896f6bdc0ae2312708df339430095"}}, "rules_proto_grpc_python_pip_deps_310_hyperframe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_proto_grpc_python_pip_deps_310", "requirement": "hyperframe==6.0.1     --hash=sha256:0ec6bafd80d8ad2195c4f03aacba3a8265e57bc4cff261e802bf39970ed02a15     --hash=sha256:ae510046231dc8e9ecb1a6586f63d2347bf4c8905914aa84ba585ae85f28a914"}}, "rules_proto_grpc_python_pip_deps_310_multidict": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_proto_grpc_python_pip_deps_310", "requirement": "multidict==6.0.5     --hash=sha256:01265f5e40f5a17f8241d52656ed27192be03bfa8764d88e8220141d1e4b3556     --hash=sha256:0275e35209c27a3f7951e1ce7aaf93ce0d163b28948444bec61dd7badc6d3f8c     --hash=sha256:04bde7a7b3de05732a4eb39c94574db1ec99abb56162d6c520ad26f83267de29     --hash=sha256:04da1bb8c8dbadf2a18a452639771951c662c5ad03aefe4884775454be322c9b     --hash=sha256:09a892e4a9fb47331da06948690ae38eaa2426de97b4ccbfafbdcbe5c8f37ff8     --hash=sha256:0d63c74e3d7ab26de115c49bffc92cc77ed23395303d496eae515d4204a625e7     --hash=sha256:107c0cdefe028703fb5dafe640a409cb146d44a6ae201e55b35a4af8e95457dd     --hash=sha256:141b43360bfd3bdd75f15ed811850763555a251e38b2405967f8e25fb43f7d40     --hash=sha256:14c2976aa9038c2629efa2c148022ed5eb4cb939e15ec7aace7ca932f48f9ba6     --hash=sha256:19fe01cea168585ba0f678cad6f58133db2aa14eccaf22f88e4a6dccadfad8b3     --hash=sha256:1d147090048129ce3c453f0292e7697d333db95e52616b3793922945804a433c     --hash=sha256:1d9ea7a7e779d7a3561aade7d596649fbecfa5c08a7674b11b423783217933f9     --hash=sha256:215ed703caf15f578dca76ee6f6b21b7603791ae090fbf1ef9d865571039ade5     --hash=sha256:21fd81c4ebdb4f214161be351eb5bcf385426bf023041da2fd9e60681f3cebae     --hash=sha256:220dd781e3f7af2c2c1053da9fa96d9cf3072ca58f057f4c5adaaa1cab8fc442     --hash=sha256:228b644ae063c10e7f324ab1ab6b548bdf6f8b47f3ec234fef1093bc2735e5f9     --hash=sha256:29bfeb0dff5cb5fdab2023a7a9947b3b4af63e9c47cae2a10ad58394b517fddc     --hash=sha256:2f4848aa3baa109e6ab81fe2006c77ed4d3cd1e0ac2c1fbddb7b1277c168788c     --hash=sha256:2faa5ae9376faba05f630d7e5e6be05be22913782b927b19d12b8145968a85ea     --hash=sha256:2ffc42c922dbfddb4a4c3b438eb056828719f07608af27d163191cb3e3aa6cc5     --hash=sha256:37b15024f864916b4951adb95d3a80c9431299080341ab9544ed148091b53f50     --hash=sha256:3cc2ad10255f903656017363cd59436f2111443a76f996584d1077e43ee51182     --hash=sha256:3d25f19500588cbc47dc19081d78131c32637c25804df8414463ec908631e453     --hash=sha256:403c0911cd5d5791605808b942c88a8155c2592e05332d2bf78f18697a5fa15e     --hash=sha256:411bf8515f3be9813d06004cac41ccf7d1cd46dfe233705933dd163b60e37600     --hash=sha256:425bf820055005bfc8aa9a0b99ccb52cc2f4070153e34b701acc98d201693733     --hash=sha256:435a0984199d81ca178b9ae2c26ec3d49692d20ee29bc4c11a2a8d4514c67eda     --hash=sha256:4a6a4f196f08c58c59e0b8ef8ec441d12aee4125a7d4f4fef000ccb22f8d7241     --hash=sha256:4cc0ef8b962ac7a5e62b9e826bd0cd5040e7d401bc45a6835910ed699037a461     --hash=sha256:51d035609b86722963404f711db441cf7134f1889107fb171a970c9701f92e1e     --hash=sha256:53689bb4e102200a4fafa9de9c7c3c212ab40a7ab2c8e474491914d2305f187e     --hash=sha256:55205d03e8a598cfc688c71ca8ea5f66447164efff8869517f175ea632c7cb7b     --hash=sha256:5c0631926c4f58e9a5ccce555ad7747d9a9f8b10619621f22f9635f069f6233e     --hash=sha256:5cb241881eefd96b46f89b1a056187ea8e9ba14ab88ba632e68d7a2ecb7aadf7     --hash=sha256:60d698e8179a42ec85172d12f50b1668254628425a6bd611aba022257cac1386     --hash=sha256:612d1156111ae11d14afaf3a0669ebf6c170dbb735e510a7438ffe2369a847fd     --hash=sha256:6214c5a5571802c33f80e6c84713b2c79e024995b9c5897f794b43e714daeec9     --hash=sha256:6939c95381e003f54cd4c5516740faba40cf5ad3eeff460c3ad1d3e0ea2549bf     --hash=sha256:69db76c09796b313331bb7048229e3bee7928eb62bab5e071e9f7fcc4879caee     --hash=sha256:6bf7a982604375a8d49b6cc1b781c1747f243d91b81035a9b43a2126c04766f5     --hash=sha256:766c8f7511df26d9f11cd3a8be623e59cca73d44643abab3f8c8c07620524e4a     --hash=sha256:76c0de87358b192de7ea9649beb392f107dcad9ad27276324c24c91774ca5271     --hash=sha256:76f067f5121dcecf0d63a67f29080b26c43c71a98b10c701b0677e4a065fbd54     --hash=sha256:7901c05ead4b3fb75113fb1dd33eb1253c6d3ee37ce93305acd9d38e0b5f21a4     --hash=sha256:79660376075cfd4b2c80f295528aa6beb2058fd289f4c9252f986751a4cd0496     --hash=sha256:79a6d2ba910adb2cbafc95dad936f8b9386e77c84c35bc0add315b856d7c3abb     --hash=sha256:7afcdd1fc07befad18ec4523a782cde4e93e0a2bf71239894b8d61ee578c1319     --hash=sha256:7be7047bd08accdb7487737631d25735c9a04327911de89ff1b26b81745bd4e3     --hash=sha256:7c6390cf87ff6234643428991b7359b5f59cc15155695deb4eda5c777d2b880f     --hash=sha256:7df704ca8cf4a073334e0427ae2345323613e4df18cc224f647f251e5e75a527     --hash=sha256:85f67aed7bb647f93e7520633d8f51d3cbc6ab96957c71272b286b2f30dc70ed     --hash=sha256:896ebdcf62683551312c30e20614305f53125750803b614e9e6ce74a96232604     --hash=sha256:92d16a3e275e38293623ebf639c471d3e03bb20b8ebb845237e0d3664914caef     --hash=sha256:99f60d34c048c5c2fabc766108c103612344c46e35d4ed9ae0673d33c8fb26e8     --hash=sha256:9fe7b0653ba3d9d65cbe7698cca585bf0f8c83dbbcc710db9c90f478e175f2d5     --hash=sha256:a3145cb08d8625b2d3fee1b2d596a8766352979c9bffe5d7833e0503d0f0b5e5     --hash=sha256:aeaf541ddbad8311a87dd695ed9642401131ea39ad7bc8cf3ef3967fd093b626     --hash=sha256:b55358304d7a73d7bdf5de62494aaf70bd33015831ffd98bc498b433dfe5b10c     --hash=sha256:b82cc8ace10ab5bd93235dfaab2021c70637005e1ac787031f4d1da63d493c1d     --hash=sha256:c0868d64af83169e4d4152ec612637a543f7a336e4a307b119e98042e852ad9c     --hash=sha256:c1c1496e73051918fcd4f58ff2e0f2f3066d1c76a0c6aeffd9b45d53243702cc     --hash=sha256:c9bf56195c6bbd293340ea82eafd0071cb3d450c703d2c93afb89f93b8386ccc     --hash=sha256:cbebcd5bcaf1eaf302617c114aa67569dd3f090dd0ce8ba9e35e9985b41ac35b     --hash=sha256:cd6c8fca38178e12c00418de737aef1261576bd1b6e8c6134d3e729a4e858b38     --hash=sha256:ceb3b7e6a0135e092de86110c5a74e46bda4bd4fbfeeb3a3bcec79c0f861e450     --hash=sha256:cf590b134eb70629e350691ecca88eac3e3b8b3c86992042fb82e3cb1830d5e1     --hash=sha256:d3eb1ceec286eba8220c26f3b0096cf189aea7057b6e7b7a2e60ed36b373b77f     --hash=sha256:d65f25da8e248202bd47445cec78e0025c0fe7582b23ec69c3b27a640dd7a8e3     --hash=sha256:d6f6d4f185481c9669b9447bf9d9cf3b95a0e9df9d169bbc17e363b7d5487755     --hash=sha256:d84a5c3a5f7ce6db1f999fb9438f686bc2e09d38143f2d93d8406ed2dd6b9226     --hash=sha256:d946b0a9eb8aaa590df1fe082cee553ceab173e6cb5b03239716338629c50c7a     --hash=sha256:dce1c6912ab9ff5f179eaf6efe7365c1f425ed690b03341911bf4939ef2f3046     --hash=sha256:de170c7b4fe6859beb8926e84f7d7d6c693dfe8e27372ce3b76f01c46e489fcf     --hash=sha256:e02021f87a5b6932fa6ce916ca004c4d441509d33bbdbeca70d05dff5e9d2479     --hash=sha256:e030047e85cbcedbfc073f71836d62dd5dadfbe7531cae27789ff66bc551bd5e     --hash=sha256:e0e79d91e71b9867c73323a3444724d496c037e578a0e1755ae159ba14f4f3d1     --hash=sha256:e4428b29611e989719874670fd152b6625500ad6c686d464e99f5aaeeaca175a     --hash=sha256:e4972624066095e52b569e02b5ca97dbd7a7ddd4294bf4e7247d52635630dd83     --hash=sha256:e7be68734bd8c9a513f2b0cfd508802d6609da068f40dc57d4e3494cefc92929     --hash=sha256:e8e94e6912639a02ce173341ff62cc1201232ab86b8a8fcc05572741a5dc7d93     --hash=sha256:ea1456df2a27c73ce51120fa2f519f1bea2f4a03a917f4a43c8707cf4cbbae1a     --hash=sha256:ebd8d160f91a764652d3e51ce0d2956b38efe37c9231cd82cfc0bed2e40b581c     --hash=sha256:eca2e9d0cc5a889850e9bbd68e98314ada174ff6ccd1129500103df7a94a7a44     --hash=sha256:edd08e6f2f1a390bf137080507e44ccc086353c8e98c657e666c017718561b89     --hash=sha256:f285e862d2f153a70586579c15c44656f888806ed0e5b56b64489afe4a2dbfba     --hash=sha256:f2a1dee728b52b33eebff5072817176c172050d44d67befd681609b4746e1c2e     --hash=sha256:f7e301075edaf50500f0b341543c41194d8df3ae5caf4702f2095f3ca73dd8da     --hash=sha256:fb616be3538599e797a2017cccca78e354c767165e8858ab5116813146041a24     --hash=sha256:fce28b3c8a81b6b36dfac9feb1de115bab619b3c13905b419ec71d03a3fc1423     --hash=sha256:fe5d7785250541f7f5019ab9cba2c71169dc7d74d0f45253f8313f436458a4ef"}}, "rules_proto_grpc_python_pip_deps_310_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "rules_proto_grpc_python_pip_deps_310", "requirement": "protobuf==5.29.2     --hash=sha256:13d6d617a2a9e0e82a88113d7191a1baa1e42c2cc6f5f1398d3b054c8e7e714a     --hash=sha256:2d2e674c58a06311c8e99e74be43e7f3a8d1e2b2fdf845eaa347fbd866f23355     --hash=sha256:36000f97ea1e76e8398a3f02936aac2a5d2b111aae9920ec1b769fc4a222c4d9     --hash=sha256:494229ecd8c9009dd71eda5fd57528395d1eacdf307dbece6c12ad0dd09e912e     --hash=sha256:842de6d9241134a973aab719ab42b008a18a90f9f07f06ba480df268f86432f9     --hash=sha256:a0c53d78383c851bfa97eb42e3703aefdc96d2036a41482ffd55dc5f529466eb     --hash=sha256:b2cc8e8bb7c9326996f0e160137b0861f1a82162502658df2951209d0cb0309e     --hash=sha256:b6b0d416bbbb9d4fbf9d0561dbfc4e324fd522f61f7af0fe0f282ab67b22477e     --hash=sha256:c12ba8249f5624300cf51c3d0bfe5be71a60c63e4dcf51ffe9a68771d958c851     --hash=sha256:e621a98c0201a7c8afe89d9646859859be97cb22b8bf1d8eacfd90d5bda2eb19     --hash=sha256:fde4554c0e578a5a0bcc9a276339594848d1e89f9ea47b4427c80e5d72f90181"}}, "rules_proto_grpc_python_pip_deps_311_grpcio": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_proto_grpc_python_pip_deps_311", "requirement": "grpcio==1.64.1     --hash=sha256:03b43d0ccf99c557ec671c7dede64f023c7da9bb632ac65dbc57f166e4970040     --hash=sha256:0a12ddb1678ebc6a84ec6b0487feac020ee2b1659cbe69b80f06dbffdb249122     --hash=sha256:0a2813093ddb27418a4c99f9b1c223fab0b053157176a64cc9db0f4557b69bd9     --hash=sha256:0cc79c982ccb2feec8aad0e8fb0d168bcbca85bc77b080d0d3c5f2f15c24ea8f     --hash=sha256:1257b76748612aca0f89beec7fa0615727fd6f2a1ad580a9638816a4b2eb18fd     --hash=sha256:1262402af5a511c245c3ae918167eca57342c72320dffae5d9b51840c4b2f86d     --hash=sha256:19264fc964576ddb065368cae953f8d0514ecc6cb3da8903766d9fb9d4554c33     --hash=sha256:198908f9b22e2672a998870355e226a725aeab327ac4e6ff3a1399792ece4762     --hash=sha256:1de403fc1305fd96cfa75e83be3dee8538f2413a6b1685b8452301c7ba33c294     --hash=sha256:20405cb8b13fd779135df23fabadc53b86522d0f1cba8cca0e87968587f50650     --hash=sha256:2981c7365a9353f9b5c864595c510c983251b1ab403e05b1ccc70a3d9541a73b     --hash=sha256:2c3c1b90ab93fed424e454e93c0ed0b9d552bdf1b0929712b094f5ecfe7a23ad     --hash=sha256:39b9d0acaa8d835a6566c640f48b50054f422d03e77e49716d4c4e8e279665a1     --hash=sha256:3b64ae304c175671efdaa7ec9ae2cc36996b681eb63ca39c464958396697daff     --hash=sha256:4657d24c8063e6095f850b68f2d1ba3b39f2b287a38242dcabc166453e950c59     --hash=sha256:4d6dab6124225496010bd22690f2d9bd35c7cbb267b3f14e7a3eb05c911325d4     --hash=sha256:55260032b95c49bee69a423c2f5365baa9369d2f7d233e933564d8a47b893027     --hash=sha256:55697ecec192bc3f2f3cc13a295ab670f51de29884ca9ae6cd6247df55df2502     --hash=sha256:5841dd1f284bd1b3d8a6eca3a7f062b06f1eec09b184397e1d1d43447e89a7ae     --hash=sha256:58b1041e7c870bb30ee41d3090cbd6f0851f30ae4eb68228955d973d3efa2e61     --hash=sha256:5e42634a989c3aa6049f132266faf6b949ec2a6f7d302dbb5c15395b77d757eb     --hash=sha256:5e56462b05a6f860b72f0fa50dca06d5b26543a4e88d0396259a07dc30f4e5aa     --hash=sha256:5f8b75f64d5d324c565b263c67dbe4f0af595635bbdd93bb1a88189fc62ed2e5     --hash=sha256:62b4e6eb7bf901719fce0ca83e3ed474ae5022bb3827b0a501e056458c51c0a1     --hash=sha256:6503b64c8b2dfad299749cad1b595c650c91e5b2c8a1b775380fcf8d2cbba1e9     --hash=sha256:6c024ffc22d6dc59000faf8ad781696d81e8e38f4078cb0f2630b4a3cf231a90     --hash=sha256:73819689c169417a4f978e562d24f2def2be75739c4bed1992435d007819da1b     --hash=sha256:75dbbf415026d2862192fe1b28d71f209e2fd87079d98470db90bebe57b33179     --hash=sha256:8caee47e970b92b3dd948371230fcceb80d3f2277b3bf7fbd7c0564e7d39068e     --hash=sha256:8d51dd1c59d5fa0f34266b80a3805ec29a1f26425c2a54736133f6d87fc4968a     --hash=sha256:940e3ec884520155f68a3b712d045e077d61c520a195d1a5932c531f11883489     --hash=sha256:a011ac6c03cfe162ff2b727bcb530567826cec85eb8d4ad2bfb4bd023287a52d     --hash=sha256:a3a035c37ce7565b8f4f35ff683a4db34d24e53dc487e47438e434eb3f701b2a     --hash=sha256:a5e771d0252e871ce194d0fdcafd13971f1aae0ddacc5f25615030d5df55c3a2     --hash=sha256:ac15b6c2c80a4d1338b04d42a02d376a53395ddf0ec9ab157cbaf44191f3ffdd     --hash=sha256:b1a82e0b9b3022799c336e1fc0f6210adc019ae84efb7321d668129d28ee1efb     --hash=sha256:bac71b4b28bc9af61efcdc7630b166440bbfbaa80940c9a697271b5e1dabbc61     --hash=sha256:bbc5b1d78a7822b0a84c6f8917faa986c1a744e65d762ef6d8be9d75677af2ca     --hash=sha256:c1a786ac592b47573a5bb7e35665c08064a5d77ab88a076eec11f8ae86b3e3f6     --hash=sha256:c84ad903d0d94311a2b7eea608da163dace97c5fe9412ea311e72c3684925602     --hash=sha256:d4d29cc612e1332237877dfa7fe687157973aab1d63bd0f84cf06692f04c0367     --hash=sha256:e3d9f8d1221baa0ced7ec7322a981e28deb23749c76eeeb3d33e18b72935ab62     --hash=sha256:e7cd5c1325f6808b8ae31657d281aadb2a51ac11ab081ae335f4f7fc44c1721d     --hash=sha256:ed6091fa0adcc7e4ff944090cf203a52da35c37a130efa564ded02b7aff63bcd     --hash=sha256:ee73a2f5ca4ba44fa33b4d7d2c71e2c8a9e9f78d53f6507ad68e7d2ad5f64a22     --hash=sha256:f10193c69fc9d3d726e83bbf0f3d316f1847c3071c8c93d8090cf5f326b14309"}}, "rules_proto_grpc_python_pip_deps_311_grpclib": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_proto_grpc_python_pip_deps_311", "requirement": "grpclib==0.4.7     --hash=sha256:2988ef57c02b22b7a2e8e961792c41ccf97efc2ace91ae7a5b0de03c363823c3"}}, "rules_proto_grpc_python_pip_deps_311_h2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_proto_grpc_python_pip_deps_311", "requirement": "h2==4.1.0     --hash=sha256:03a46bcf682256c95b5fd9e9a99c1323584c3eec6440d379b9903d709476bc6d     --hash=sha256:a83aca08fbe7aacb79fec788c9c0bac936343560ed9ec18b82a13a12c28d2abb"}}, "rules_proto_grpc_python_pip_deps_311_hpack": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_proto_grpc_python_pip_deps_311", "requirement": "hpack==4.0.0     --hash=sha256:84a076fad3dc9a9f8063ccb8041ef100867b1878b25ef0ee63847a5d53818a6c     --hash=sha256:fc41de0c63e687ebffde81187a948221294896f6bdc0ae2312708df339430095"}}, "rules_proto_grpc_python_pip_deps_311_hyperframe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_proto_grpc_python_pip_deps_311", "requirement": "hyperframe==6.0.1     --hash=sha256:0ec6bafd80d8ad2195c4f03aacba3a8265e57bc4cff261e802bf39970ed02a15     --hash=sha256:ae510046231dc8e9ecb1a6586f63d2347bf4c8905914aa84ba585ae85f28a914"}}, "rules_proto_grpc_python_pip_deps_311_multidict": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_proto_grpc_python_pip_deps_311", "requirement": "multidict==6.0.5     --hash=sha256:01265f5e40f5a17f8241d52656ed27192be03bfa8764d88e8220141d1e4b3556     --hash=sha256:0275e35209c27a3f7951e1ce7aaf93ce0d163b28948444bec61dd7badc6d3f8c     --hash=sha256:04bde7a7b3de05732a4eb39c94574db1ec99abb56162d6c520ad26f83267de29     --hash=sha256:04da1bb8c8dbadf2a18a452639771951c662c5ad03aefe4884775454be322c9b     --hash=sha256:09a892e4a9fb47331da06948690ae38eaa2426de97b4ccbfafbdcbe5c8f37ff8     --hash=sha256:0d63c74e3d7ab26de115c49bffc92cc77ed23395303d496eae515d4204a625e7     --hash=sha256:107c0cdefe028703fb5dafe640a409cb146d44a6ae201e55b35a4af8e95457dd     --hash=sha256:141b43360bfd3bdd75f15ed811850763555a251e38b2405967f8e25fb43f7d40     --hash=sha256:14c2976aa9038c2629efa2c148022ed5eb4cb939e15ec7aace7ca932f48f9ba6     --hash=sha256:19fe01cea168585ba0f678cad6f58133db2aa14eccaf22f88e4a6dccadfad8b3     --hash=sha256:1d147090048129ce3c453f0292e7697d333db95e52616b3793922945804a433c     --hash=sha256:1d9ea7a7e779d7a3561aade7d596649fbecfa5c08a7674b11b423783217933f9     --hash=sha256:215ed703caf15f578dca76ee6f6b21b7603791ae090fbf1ef9d865571039ade5     --hash=sha256:21fd81c4ebdb4f214161be351eb5bcf385426bf023041da2fd9e60681f3cebae     --hash=sha256:220dd781e3f7af2c2c1053da9fa96d9cf3072ca58f057f4c5adaaa1cab8fc442     --hash=sha256:228b644ae063c10e7f324ab1ab6b548bdf6f8b47f3ec234fef1093bc2735e5f9     --hash=sha256:29bfeb0dff5cb5fdab2023a7a9947b3b4af63e9c47cae2a10ad58394b517fddc     --hash=sha256:2f4848aa3baa109e6ab81fe2006c77ed4d3cd1e0ac2c1fbddb7b1277c168788c     --hash=sha256:2faa5ae9376faba05f630d7e5e6be05be22913782b927b19d12b8145968a85ea     --hash=sha256:2ffc42c922dbfddb4a4c3b438eb056828719f07608af27d163191cb3e3aa6cc5     --hash=sha256:37b15024f864916b4951adb95d3a80c9431299080341ab9544ed148091b53f50     --hash=sha256:3cc2ad10255f903656017363cd59436f2111443a76f996584d1077e43ee51182     --hash=sha256:3d25f19500588cbc47dc19081d78131c32637c25804df8414463ec908631e453     --hash=sha256:403c0911cd5d5791605808b942c88a8155c2592e05332d2bf78f18697a5fa15e     --hash=sha256:411bf8515f3be9813d06004cac41ccf7d1cd46dfe233705933dd163b60e37600     --hash=sha256:425bf820055005bfc8aa9a0b99ccb52cc2f4070153e34b701acc98d201693733     --hash=sha256:435a0984199d81ca178b9ae2c26ec3d49692d20ee29bc4c11a2a8d4514c67eda     --hash=sha256:4a6a4f196f08c58c59e0b8ef8ec441d12aee4125a7d4f4fef000ccb22f8d7241     --hash=sha256:4cc0ef8b962ac7a5e62b9e826bd0cd5040e7d401bc45a6835910ed699037a461     --hash=sha256:51d035609b86722963404f711db441cf7134f1889107fb171a970c9701f92e1e     --hash=sha256:53689bb4e102200a4fafa9de9c7c3c212ab40a7ab2c8e474491914d2305f187e     --hash=sha256:55205d03e8a598cfc688c71ca8ea5f66447164efff8869517f175ea632c7cb7b     --hash=sha256:5c0631926c4f58e9a5ccce555ad7747d9a9f8b10619621f22f9635f069f6233e     --hash=sha256:5cb241881eefd96b46f89b1a056187ea8e9ba14ab88ba632e68d7a2ecb7aadf7     --hash=sha256:60d698e8179a42ec85172d12f50b1668254628425a6bd611aba022257cac1386     --hash=sha256:612d1156111ae11d14afaf3a0669ebf6c170dbb735e510a7438ffe2369a847fd     --hash=sha256:6214c5a5571802c33f80e6c84713b2c79e024995b9c5897f794b43e714daeec9     --hash=sha256:6939c95381e003f54cd4c5516740faba40cf5ad3eeff460c3ad1d3e0ea2549bf     --hash=sha256:69db76c09796b313331bb7048229e3bee7928eb62bab5e071e9f7fcc4879caee     --hash=sha256:6bf7a982604375a8d49b6cc1b781c1747f243d91b81035a9b43a2126c04766f5     --hash=sha256:766c8f7511df26d9f11cd3a8be623e59cca73d44643abab3f8c8c07620524e4a     --hash=sha256:76c0de87358b192de7ea9649beb392f107dcad9ad27276324c24c91774ca5271     --hash=sha256:76f067f5121dcecf0d63a67f29080b26c43c71a98b10c701b0677e4a065fbd54     --hash=sha256:7901c05ead4b3fb75113fb1dd33eb1253c6d3ee37ce93305acd9d38e0b5f21a4     --hash=sha256:79660376075cfd4b2c80f295528aa6beb2058fd289f4c9252f986751a4cd0496     --hash=sha256:79a6d2ba910adb2cbafc95dad936f8b9386e77c84c35bc0add315b856d7c3abb     --hash=sha256:7afcdd1fc07befad18ec4523a782cde4e93e0a2bf71239894b8d61ee578c1319     --hash=sha256:7be7047bd08accdb7487737631d25735c9a04327911de89ff1b26b81745bd4e3     --hash=sha256:7c6390cf87ff6234643428991b7359b5f59cc15155695deb4eda5c777d2b880f     --hash=sha256:7df704ca8cf4a073334e0427ae2345323613e4df18cc224f647f251e5e75a527     --hash=sha256:85f67aed7bb647f93e7520633d8f51d3cbc6ab96957c71272b286b2f30dc70ed     --hash=sha256:896ebdcf62683551312c30e20614305f53125750803b614e9e6ce74a96232604     --hash=sha256:92d16a3e275e38293623ebf639c471d3e03bb20b8ebb845237e0d3664914caef     --hash=sha256:99f60d34c048c5c2fabc766108c103612344c46e35d4ed9ae0673d33c8fb26e8     --hash=sha256:9fe7b0653ba3d9d65cbe7698cca585bf0f8c83dbbcc710db9c90f478e175f2d5     --hash=sha256:a3145cb08d8625b2d3fee1b2d596a8766352979c9bffe5d7833e0503d0f0b5e5     --hash=sha256:aeaf541ddbad8311a87dd695ed9642401131ea39ad7bc8cf3ef3967fd093b626     --hash=sha256:b55358304d7a73d7bdf5de62494aaf70bd33015831ffd98bc498b433dfe5b10c     --hash=sha256:b82cc8ace10ab5bd93235dfaab2021c70637005e1ac787031f4d1da63d493c1d     --hash=sha256:c0868d64af83169e4d4152ec612637a543f7a336e4a307b119e98042e852ad9c     --hash=sha256:c1c1496e73051918fcd4f58ff2e0f2f3066d1c76a0c6aeffd9b45d53243702cc     --hash=sha256:c9bf56195c6bbd293340ea82eafd0071cb3d450c703d2c93afb89f93b8386ccc     --hash=sha256:cbebcd5bcaf1eaf302617c114aa67569dd3f090dd0ce8ba9e35e9985b41ac35b     --hash=sha256:cd6c8fca38178e12c00418de737aef1261576bd1b6e8c6134d3e729a4e858b38     --hash=sha256:ceb3b7e6a0135e092de86110c5a74e46bda4bd4fbfeeb3a3bcec79c0f861e450     --hash=sha256:cf590b134eb70629e350691ecca88eac3e3b8b3c86992042fb82e3cb1830d5e1     --hash=sha256:d3eb1ceec286eba8220c26f3b0096cf189aea7057b6e7b7a2e60ed36b373b77f     --hash=sha256:d65f25da8e248202bd47445cec78e0025c0fe7582b23ec69c3b27a640dd7a8e3     --hash=sha256:d6f6d4f185481c9669b9447bf9d9cf3b95a0e9df9d169bbc17e363b7d5487755     --hash=sha256:d84a5c3a5f7ce6db1f999fb9438f686bc2e09d38143f2d93d8406ed2dd6b9226     --hash=sha256:d946b0a9eb8aaa590df1fe082cee553ceab173e6cb5b03239716338629c50c7a     --hash=sha256:dce1c6912ab9ff5f179eaf6efe7365c1f425ed690b03341911bf4939ef2f3046     --hash=sha256:de170c7b4fe6859beb8926e84f7d7d6c693dfe8e27372ce3b76f01c46e489fcf     --hash=sha256:e02021f87a5b6932fa6ce916ca004c4d441509d33bbdbeca70d05dff5e9d2479     --hash=sha256:e030047e85cbcedbfc073f71836d62dd5dadfbe7531cae27789ff66bc551bd5e     --hash=sha256:e0e79d91e71b9867c73323a3444724d496c037e578a0e1755ae159ba14f4f3d1     --hash=sha256:e4428b29611e989719874670fd152b6625500ad6c686d464e99f5aaeeaca175a     --hash=sha256:e4972624066095e52b569e02b5ca97dbd7a7ddd4294bf4e7247d52635630dd83     --hash=sha256:e7be68734bd8c9a513f2b0cfd508802d6609da068f40dc57d4e3494cefc92929     --hash=sha256:e8e94e6912639a02ce173341ff62cc1201232ab86b8a8fcc05572741a5dc7d93     --hash=sha256:ea1456df2a27c73ce51120fa2f519f1bea2f4a03a917f4a43c8707cf4cbbae1a     --hash=sha256:ebd8d160f91a764652d3e51ce0d2956b38efe37c9231cd82cfc0bed2e40b581c     --hash=sha256:eca2e9d0cc5a889850e9bbd68e98314ada174ff6ccd1129500103df7a94a7a44     --hash=sha256:edd08e6f2f1a390bf137080507e44ccc086353c8e98c657e666c017718561b89     --hash=sha256:f285e862d2f153a70586579c15c44656f888806ed0e5b56b64489afe4a2dbfba     --hash=sha256:f2a1dee728b52b33eebff5072817176c172050d44d67befd681609b4746e1c2e     --hash=sha256:f7e301075edaf50500f0b341543c41194d8df3ae5caf4702f2095f3ca73dd8da     --hash=sha256:fb616be3538599e797a2017cccca78e354c767165e8858ab5116813146041a24     --hash=sha256:fce28b3c8a81b6b36dfac9feb1de115bab619b3c13905b419ec71d03a3fc1423     --hash=sha256:fe5d7785250541f7f5019ab9cba2c71169dc7d74d0f45253f8313f436458a4ef"}}, "rules_proto_grpc_python_pip_deps_311_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_proto_grpc_python_pip_deps_311", "requirement": "protobuf==5.29.2     --hash=sha256:13d6d617a2a9e0e82a88113d7191a1baa1e42c2cc6f5f1398d3b054c8e7e714a     --hash=sha256:2d2e674c58a06311c8e99e74be43e7f3a8d1e2b2fdf845eaa347fbd866f23355     --hash=sha256:36000f97ea1e76e8398a3f02936aac2a5d2b111aae9920ec1b769fc4a222c4d9     --hash=sha256:494229ecd8c9009dd71eda5fd57528395d1eacdf307dbece6c12ad0dd09e912e     --hash=sha256:842de6d9241134a973aab719ab42b008a18a90f9f07f06ba480df268f86432f9     --hash=sha256:a0c53d78383c851bfa97eb42e3703aefdc96d2036a41482ffd55dc5f529466eb     --hash=sha256:b2cc8e8bb7c9326996f0e160137b0861f1a82162502658df2951209d0cb0309e     --hash=sha256:b6b0d416bbbb9d4fbf9d0561dbfc4e324fd522f61f7af0fe0f282ab67b22477e     --hash=sha256:c12ba8249f5624300cf51c3d0bfe5be71a60c63e4dcf51ffe9a68771d958c851     --hash=sha256:e621a98c0201a7c8afe89d9646859859be97cb22b8bf1d8eacfd90d5bda2eb19     --hash=sha256:fde4554c0e578a5a0bcc9a276339594848d1e89f9ea47b4427c80e5d72f90181"}}, "rules_proto_grpc_python_pip_deps_312_grpcio": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_proto_grpc_python_pip_deps_312", "requirement": "grpcio==1.64.1     --hash=sha256:03b43d0ccf99c557ec671c7dede64f023c7da9bb632ac65dbc57f166e4970040     --hash=sha256:0a12ddb1678ebc6a84ec6b0487feac020ee2b1659cbe69b80f06dbffdb249122     --hash=sha256:0a2813093ddb27418a4c99f9b1c223fab0b053157176a64cc9db0f4557b69bd9     --hash=sha256:0cc79c982ccb2feec8aad0e8fb0d168bcbca85bc77b080d0d3c5f2f15c24ea8f     --hash=sha256:1257b76748612aca0f89beec7fa0615727fd6f2a1ad580a9638816a4b2eb18fd     --hash=sha256:1262402af5a511c245c3ae918167eca57342c72320dffae5d9b51840c4b2f86d     --hash=sha256:19264fc964576ddb065368cae953f8d0514ecc6cb3da8903766d9fb9d4554c33     --hash=sha256:198908f9b22e2672a998870355e226a725aeab327ac4e6ff3a1399792ece4762     --hash=sha256:1de403fc1305fd96cfa75e83be3dee8538f2413a6b1685b8452301c7ba33c294     --hash=sha256:20405cb8b13fd779135df23fabadc53b86522d0f1cba8cca0e87968587f50650     --hash=sha256:2981c7365a9353f9b5c864595c510c983251b1ab403e05b1ccc70a3d9541a73b     --hash=sha256:2c3c1b90ab93fed424e454e93c0ed0b9d552bdf1b0929712b094f5ecfe7a23ad     --hash=sha256:39b9d0acaa8d835a6566c640f48b50054f422d03e77e49716d4c4e8e279665a1     --hash=sha256:3b64ae304c175671efdaa7ec9ae2cc36996b681eb63ca39c464958396697daff     --hash=sha256:4657d24c8063e6095f850b68f2d1ba3b39f2b287a38242dcabc166453e950c59     --hash=sha256:4d6dab6124225496010bd22690f2d9bd35c7cbb267b3f14e7a3eb05c911325d4     --hash=sha256:55260032b95c49bee69a423c2f5365baa9369d2f7d233e933564d8a47b893027     --hash=sha256:55697ecec192bc3f2f3cc13a295ab670f51de29884ca9ae6cd6247df55df2502     --hash=sha256:5841dd1f284bd1b3d8a6eca3a7f062b06f1eec09b184397e1d1d43447e89a7ae     --hash=sha256:58b1041e7c870bb30ee41d3090cbd6f0851f30ae4eb68228955d973d3efa2e61     --hash=sha256:5e42634a989c3aa6049f132266faf6b949ec2a6f7d302dbb5c15395b77d757eb     --hash=sha256:5e56462b05a6f860b72f0fa50dca06d5b26543a4e88d0396259a07dc30f4e5aa     --hash=sha256:5f8b75f64d5d324c565b263c67dbe4f0af595635bbdd93bb1a88189fc62ed2e5     --hash=sha256:62b4e6eb7bf901719fce0ca83e3ed474ae5022bb3827b0a501e056458c51c0a1     --hash=sha256:6503b64c8b2dfad299749cad1b595c650c91e5b2c8a1b775380fcf8d2cbba1e9     --hash=sha256:6c024ffc22d6dc59000faf8ad781696d81e8e38f4078cb0f2630b4a3cf231a90     --hash=sha256:73819689c169417a4f978e562d24f2def2be75739c4bed1992435d007819da1b     --hash=sha256:75dbbf415026d2862192fe1b28d71f209e2fd87079d98470db90bebe57b33179     --hash=sha256:8caee47e970b92b3dd948371230fcceb80d3f2277b3bf7fbd7c0564e7d39068e     --hash=sha256:8d51dd1c59d5fa0f34266b80a3805ec29a1f26425c2a54736133f6d87fc4968a     --hash=sha256:940e3ec884520155f68a3b712d045e077d61c520a195d1a5932c531f11883489     --hash=sha256:a011ac6c03cfe162ff2b727bcb530567826cec85eb8d4ad2bfb4bd023287a52d     --hash=sha256:a3a035c37ce7565b8f4f35ff683a4db34d24e53dc487e47438e434eb3f701b2a     --hash=sha256:a5e771d0252e871ce194d0fdcafd13971f1aae0ddacc5f25615030d5df55c3a2     --hash=sha256:ac15b6c2c80a4d1338b04d42a02d376a53395ddf0ec9ab157cbaf44191f3ffdd     --hash=sha256:b1a82e0b9b3022799c336e1fc0f6210adc019ae84efb7321d668129d28ee1efb     --hash=sha256:bac71b4b28bc9af61efcdc7630b166440bbfbaa80940c9a697271b5e1dabbc61     --hash=sha256:bbc5b1d78a7822b0a84c6f8917faa986c1a744e65d762ef6d8be9d75677af2ca     --hash=sha256:c1a786ac592b47573a5bb7e35665c08064a5d77ab88a076eec11f8ae86b3e3f6     --hash=sha256:c84ad903d0d94311a2b7eea608da163dace97c5fe9412ea311e72c3684925602     --hash=sha256:d4d29cc612e1332237877dfa7fe687157973aab1d63bd0f84cf06692f04c0367     --hash=sha256:e3d9f8d1221baa0ced7ec7322a981e28deb23749c76eeeb3d33e18b72935ab62     --hash=sha256:e7cd5c1325f6808b8ae31657d281aadb2a51ac11ab081ae335f4f7fc44c1721d     --hash=sha256:ed6091fa0adcc7e4ff944090cf203a52da35c37a130efa564ded02b7aff63bcd     --hash=sha256:ee73a2f5ca4ba44fa33b4d7d2c71e2c8a9e9f78d53f6507ad68e7d2ad5f64a22     --hash=sha256:f10193c69fc9d3d726e83bbf0f3d316f1847c3071c8c93d8090cf5f326b14309"}}, "rules_proto_grpc_python_pip_deps_312_grpclib": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_proto_grpc_python_pip_deps_312", "requirement": "grpclib==0.4.7     --hash=sha256:2988ef57c02b22b7a2e8e961792c41ccf97efc2ace91ae7a5b0de03c363823c3"}}, "rules_proto_grpc_python_pip_deps_312_h2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_proto_grpc_python_pip_deps_312", "requirement": "h2==4.1.0     --hash=sha256:03a46bcf682256c95b5fd9e9a99c1323584c3eec6440d379b9903d709476bc6d     --hash=sha256:a83aca08fbe7aacb79fec788c9c0bac936343560ed9ec18b82a13a12c28d2abb"}}, "rules_proto_grpc_python_pip_deps_312_hpack": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_proto_grpc_python_pip_deps_312", "requirement": "hpack==4.0.0     --hash=sha256:84a076fad3dc9a9f8063ccb8041ef100867b1878b25ef0ee63847a5d53818a6c     --hash=sha256:fc41de0c63e687ebffde81187a948221294896f6bdc0ae2312708df339430095"}}, "rules_proto_grpc_python_pip_deps_312_hyperframe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_proto_grpc_python_pip_deps_312", "requirement": "hyperframe==6.0.1     --hash=sha256:0ec6bafd80d8ad2195c4f03aacba3a8265e57bc4cff261e802bf39970ed02a15     --hash=sha256:ae510046231dc8e9ecb1a6586f63d2347bf4c8905914aa84ba585ae85f28a914"}}, "rules_proto_grpc_python_pip_deps_312_multidict": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_proto_grpc_python_pip_deps_312", "requirement": "multidict==6.0.5     --hash=sha256:01265f5e40f5a17f8241d52656ed27192be03bfa8764d88e8220141d1e4b3556     --hash=sha256:0275e35209c27a3f7951e1ce7aaf93ce0d163b28948444bec61dd7badc6d3f8c     --hash=sha256:04bde7a7b3de05732a4eb39c94574db1ec99abb56162d6c520ad26f83267de29     --hash=sha256:04da1bb8c8dbadf2a18a452639771951c662c5ad03aefe4884775454be322c9b     --hash=sha256:09a892e4a9fb47331da06948690ae38eaa2426de97b4ccbfafbdcbe5c8f37ff8     --hash=sha256:0d63c74e3d7ab26de115c49bffc92cc77ed23395303d496eae515d4204a625e7     --hash=sha256:107c0cdefe028703fb5dafe640a409cb146d44a6ae201e55b35a4af8e95457dd     --hash=sha256:141b43360bfd3bdd75f15ed811850763555a251e38b2405967f8e25fb43f7d40     --hash=sha256:14c2976aa9038c2629efa2c148022ed5eb4cb939e15ec7aace7ca932f48f9ba6     --hash=sha256:19fe01cea168585ba0f678cad6f58133db2aa14eccaf22f88e4a6dccadfad8b3     --hash=sha256:1d147090048129ce3c453f0292e7697d333db95e52616b3793922945804a433c     --hash=sha256:1d9ea7a7e779d7a3561aade7d596649fbecfa5c08a7674b11b423783217933f9     --hash=sha256:215ed703caf15f578dca76ee6f6b21b7603791ae090fbf1ef9d865571039ade5     --hash=sha256:21fd81c4ebdb4f214161be351eb5bcf385426bf023041da2fd9e60681f3cebae     --hash=sha256:220dd781e3f7af2c2c1053da9fa96d9cf3072ca58f057f4c5adaaa1cab8fc442     --hash=sha256:228b644ae063c10e7f324ab1ab6b548bdf6f8b47f3ec234fef1093bc2735e5f9     --hash=sha256:29bfeb0dff5cb5fdab2023a7a9947b3b4af63e9c47cae2a10ad58394b517fddc     --hash=sha256:2f4848aa3baa109e6ab81fe2006c77ed4d3cd1e0ac2c1fbddb7b1277c168788c     --hash=sha256:2faa5ae9376faba05f630d7e5e6be05be22913782b927b19d12b8145968a85ea     --hash=sha256:2ffc42c922dbfddb4a4c3b438eb056828719f07608af27d163191cb3e3aa6cc5     --hash=sha256:37b15024f864916b4951adb95d3a80c9431299080341ab9544ed148091b53f50     --hash=sha256:3cc2ad10255f903656017363cd59436f2111443a76f996584d1077e43ee51182     --hash=sha256:3d25f19500588cbc47dc19081d78131c32637c25804df8414463ec908631e453     --hash=sha256:403c0911cd5d5791605808b942c88a8155c2592e05332d2bf78f18697a5fa15e     --hash=sha256:411bf8515f3be9813d06004cac41ccf7d1cd46dfe233705933dd163b60e37600     --hash=sha256:425bf820055005bfc8aa9a0b99ccb52cc2f4070153e34b701acc98d201693733     --hash=sha256:435a0984199d81ca178b9ae2c26ec3d49692d20ee29bc4c11a2a8d4514c67eda     --hash=sha256:4a6a4f196f08c58c59e0b8ef8ec441d12aee4125a7d4f4fef000ccb22f8d7241     --hash=sha256:4cc0ef8b962ac7a5e62b9e826bd0cd5040e7d401bc45a6835910ed699037a461     --hash=sha256:51d035609b86722963404f711db441cf7134f1889107fb171a970c9701f92e1e     --hash=sha256:53689bb4e102200a4fafa9de9c7c3c212ab40a7ab2c8e474491914d2305f187e     --hash=sha256:55205d03e8a598cfc688c71ca8ea5f66447164efff8869517f175ea632c7cb7b     --hash=sha256:5c0631926c4f58e9a5ccce555ad7747d9a9f8b10619621f22f9635f069f6233e     --hash=sha256:5cb241881eefd96b46f89b1a056187ea8e9ba14ab88ba632e68d7a2ecb7aadf7     --hash=sha256:60d698e8179a42ec85172d12f50b1668254628425a6bd611aba022257cac1386     --hash=sha256:612d1156111ae11d14afaf3a0669ebf6c170dbb735e510a7438ffe2369a847fd     --hash=sha256:6214c5a5571802c33f80e6c84713b2c79e024995b9c5897f794b43e714daeec9     --hash=sha256:6939c95381e003f54cd4c5516740faba40cf5ad3eeff460c3ad1d3e0ea2549bf     --hash=sha256:69db76c09796b313331bb7048229e3bee7928eb62bab5e071e9f7fcc4879caee     --hash=sha256:6bf7a982604375a8d49b6cc1b781c1747f243d91b81035a9b43a2126c04766f5     --hash=sha256:766c8f7511df26d9f11cd3a8be623e59cca73d44643abab3f8c8c07620524e4a     --hash=sha256:76c0de87358b192de7ea9649beb392f107dcad9ad27276324c24c91774ca5271     --hash=sha256:76f067f5121dcecf0d63a67f29080b26c43c71a98b10c701b0677e4a065fbd54     --hash=sha256:7901c05ead4b3fb75113fb1dd33eb1253c6d3ee37ce93305acd9d38e0b5f21a4     --hash=sha256:79660376075cfd4b2c80f295528aa6beb2058fd289f4c9252f986751a4cd0496     --hash=sha256:79a6d2ba910adb2cbafc95dad936f8b9386e77c84c35bc0add315b856d7c3abb     --hash=sha256:7afcdd1fc07befad18ec4523a782cde4e93e0a2bf71239894b8d61ee578c1319     --hash=sha256:7be7047bd08accdb7487737631d25735c9a04327911de89ff1b26b81745bd4e3     --hash=sha256:7c6390cf87ff6234643428991b7359b5f59cc15155695deb4eda5c777d2b880f     --hash=sha256:7df704ca8cf4a073334e0427ae2345323613e4df18cc224f647f251e5e75a527     --hash=sha256:85f67aed7bb647f93e7520633d8f51d3cbc6ab96957c71272b286b2f30dc70ed     --hash=sha256:896ebdcf62683551312c30e20614305f53125750803b614e9e6ce74a96232604     --hash=sha256:92d16a3e275e38293623ebf639c471d3e03bb20b8ebb845237e0d3664914caef     --hash=sha256:99f60d34c048c5c2fabc766108c103612344c46e35d4ed9ae0673d33c8fb26e8     --hash=sha256:9fe7b0653ba3d9d65cbe7698cca585bf0f8c83dbbcc710db9c90f478e175f2d5     --hash=sha256:a3145cb08d8625b2d3fee1b2d596a8766352979c9bffe5d7833e0503d0f0b5e5     --hash=sha256:aeaf541ddbad8311a87dd695ed9642401131ea39ad7bc8cf3ef3967fd093b626     --hash=sha256:b55358304d7a73d7bdf5de62494aaf70bd33015831ffd98bc498b433dfe5b10c     --hash=sha256:b82cc8ace10ab5bd93235dfaab2021c70637005e1ac787031f4d1da63d493c1d     --hash=sha256:c0868d64af83169e4d4152ec612637a543f7a336e4a307b119e98042e852ad9c     --hash=sha256:c1c1496e73051918fcd4f58ff2e0f2f3066d1c76a0c6aeffd9b45d53243702cc     --hash=sha256:c9bf56195c6bbd293340ea82eafd0071cb3d450c703d2c93afb89f93b8386ccc     --hash=sha256:cbebcd5bcaf1eaf302617c114aa67569dd3f090dd0ce8ba9e35e9985b41ac35b     --hash=sha256:cd6c8fca38178e12c00418de737aef1261576bd1b6e8c6134d3e729a4e858b38     --hash=sha256:ceb3b7e6a0135e092de86110c5a74e46bda4bd4fbfeeb3a3bcec79c0f861e450     --hash=sha256:cf590b134eb70629e350691ecca88eac3e3b8b3c86992042fb82e3cb1830d5e1     --hash=sha256:d3eb1ceec286eba8220c26f3b0096cf189aea7057b6e7b7a2e60ed36b373b77f     --hash=sha256:d65f25da8e248202bd47445cec78e0025c0fe7582b23ec69c3b27a640dd7a8e3     --hash=sha256:d6f6d4f185481c9669b9447bf9d9cf3b95a0e9df9d169bbc17e363b7d5487755     --hash=sha256:d84a5c3a5f7ce6db1f999fb9438f686bc2e09d38143f2d93d8406ed2dd6b9226     --hash=sha256:d946b0a9eb8aaa590df1fe082cee553ceab173e6cb5b03239716338629c50c7a     --hash=sha256:dce1c6912ab9ff5f179eaf6efe7365c1f425ed690b03341911bf4939ef2f3046     --hash=sha256:de170c7b4fe6859beb8926e84f7d7d6c693dfe8e27372ce3b76f01c46e489fcf     --hash=sha256:e02021f87a5b6932fa6ce916ca004c4d441509d33bbdbeca70d05dff5e9d2479     --hash=sha256:e030047e85cbcedbfc073f71836d62dd5dadfbe7531cae27789ff66bc551bd5e     --hash=sha256:e0e79d91e71b9867c73323a3444724d496c037e578a0e1755ae159ba14f4f3d1     --hash=sha256:e4428b29611e989719874670fd152b6625500ad6c686d464e99f5aaeeaca175a     --hash=sha256:e4972624066095e52b569e02b5ca97dbd7a7ddd4294bf4e7247d52635630dd83     --hash=sha256:e7be68734bd8c9a513f2b0cfd508802d6609da068f40dc57d4e3494cefc92929     --hash=sha256:e8e94e6912639a02ce173341ff62cc1201232ab86b8a8fcc05572741a5dc7d93     --hash=sha256:ea1456df2a27c73ce51120fa2f519f1bea2f4a03a917f4a43c8707cf4cbbae1a     --hash=sha256:ebd8d160f91a764652d3e51ce0d2956b38efe37c9231cd82cfc0bed2e40b581c     --hash=sha256:eca2e9d0cc5a889850e9bbd68e98314ada174ff6ccd1129500103df7a94a7a44     --hash=sha256:edd08e6f2f1a390bf137080507e44ccc086353c8e98c657e666c017718561b89     --hash=sha256:f285e862d2f153a70586579c15c44656f888806ed0e5b56b64489afe4a2dbfba     --hash=sha256:f2a1dee728b52b33eebff5072817176c172050d44d67befd681609b4746e1c2e     --hash=sha256:f7e301075edaf50500f0b341543c41194d8df3ae5caf4702f2095f3ca73dd8da     --hash=sha256:fb616be3538599e797a2017cccca78e354c767165e8858ab5116813146041a24     --hash=sha256:fce28b3c8a81b6b36dfac9feb1de115bab619b3c13905b419ec71d03a3fc1423     --hash=sha256:fe5d7785250541f7f5019ab9cba2c71169dc7d74d0f45253f8313f436458a4ef"}}, "rules_proto_grpc_python_pip_deps_312_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "rules_proto_grpc_python_pip_deps_312", "requirement": "protobuf==5.29.2     --hash=sha256:13d6d617a2a9e0e82a88113d7191a1baa1e42c2cc6f5f1398d3b054c8e7e714a     --hash=sha256:2d2e674c58a06311c8e99e74be43e7f3a8d1e2b2fdf845eaa347fbd866f23355     --hash=sha256:36000f97ea1e76e8398a3f02936aac2a5d2b111aae9920ec1b769fc4a222c4d9     --hash=sha256:494229ecd8c9009dd71eda5fd57528395d1eacdf307dbece6c12ad0dd09e912e     --hash=sha256:842de6d9241134a973aab719ab42b008a18a90f9f07f06ba480df268f86432f9     --hash=sha256:a0c53d78383c851bfa97eb42e3703aefdc96d2036a41482ffd55dc5f529466eb     --hash=sha256:b2cc8e8bb7c9326996f0e160137b0861f1a82162502658df2951209d0cb0309e     --hash=sha256:b6b0d416bbbb9d4fbf9d0561dbfc4e324fd522f61f7af0fe0f282ab67b22477e     --hash=sha256:c12ba8249f5624300cf51c3d0bfe5be71a60c63e4dcf51ffe9a68771d958c851     --hash=sha256:e621a98c0201a7c8afe89d9646859859be97cb22b8bf1d8eacfd90d5bda2eb19     --hash=sha256:fde4554c0e578a5a0bcc9a276339594848d1e89f9ea47b4427c80e5d72f90181"}}, "rules_proto_grpc_python_pip_deps_38_grpcio": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_proto_grpc_python_pip_deps_38", "requirement": "grpcio==1.64.1     --hash=sha256:03b43d0ccf99c557ec671c7dede64f023c7da9bb632ac65dbc57f166e4970040     --hash=sha256:0a12ddb1678ebc6a84ec6b0487feac020ee2b1659cbe69b80f06dbffdb249122     --hash=sha256:0a2813093ddb27418a4c99f9b1c223fab0b053157176a64cc9db0f4557b69bd9     --hash=sha256:0cc79c982ccb2feec8aad0e8fb0d168bcbca85bc77b080d0d3c5f2f15c24ea8f     --hash=sha256:1257b76748612aca0f89beec7fa0615727fd6f2a1ad580a9638816a4b2eb18fd     --hash=sha256:1262402af5a511c245c3ae918167eca57342c72320dffae5d9b51840c4b2f86d     --hash=sha256:19264fc964576ddb065368cae953f8d0514ecc6cb3da8903766d9fb9d4554c33     --hash=sha256:198908f9b22e2672a998870355e226a725aeab327ac4e6ff3a1399792ece4762     --hash=sha256:1de403fc1305fd96cfa75e83be3dee8538f2413a6b1685b8452301c7ba33c294     --hash=sha256:20405cb8b13fd779135df23fabadc53b86522d0f1cba8cca0e87968587f50650     --hash=sha256:2981c7365a9353f9b5c864595c510c983251b1ab403e05b1ccc70a3d9541a73b     --hash=sha256:2c3c1b90ab93fed424e454e93c0ed0b9d552bdf1b0929712b094f5ecfe7a23ad     --hash=sha256:39b9d0acaa8d835a6566c640f48b50054f422d03e77e49716d4c4e8e279665a1     --hash=sha256:3b64ae304c175671efdaa7ec9ae2cc36996b681eb63ca39c464958396697daff     --hash=sha256:4657d24c8063e6095f850b68f2d1ba3b39f2b287a38242dcabc166453e950c59     --hash=sha256:4d6dab6124225496010bd22690f2d9bd35c7cbb267b3f14e7a3eb05c911325d4     --hash=sha256:55260032b95c49bee69a423c2f5365baa9369d2f7d233e933564d8a47b893027     --hash=sha256:55697ecec192bc3f2f3cc13a295ab670f51de29884ca9ae6cd6247df55df2502     --hash=sha256:5841dd1f284bd1b3d8a6eca3a7f062b06f1eec09b184397e1d1d43447e89a7ae     --hash=sha256:58b1041e7c870bb30ee41d3090cbd6f0851f30ae4eb68228955d973d3efa2e61     --hash=sha256:5e42634a989c3aa6049f132266faf6b949ec2a6f7d302dbb5c15395b77d757eb     --hash=sha256:5e56462b05a6f860b72f0fa50dca06d5b26543a4e88d0396259a07dc30f4e5aa     --hash=sha256:5f8b75f64d5d324c565b263c67dbe4f0af595635bbdd93bb1a88189fc62ed2e5     --hash=sha256:62b4e6eb7bf901719fce0ca83e3ed474ae5022bb3827b0a501e056458c51c0a1     --hash=sha256:6503b64c8b2dfad299749cad1b595c650c91e5b2c8a1b775380fcf8d2cbba1e9     --hash=sha256:6c024ffc22d6dc59000faf8ad781696d81e8e38f4078cb0f2630b4a3cf231a90     --hash=sha256:73819689c169417a4f978e562d24f2def2be75739c4bed1992435d007819da1b     --hash=sha256:75dbbf415026d2862192fe1b28d71f209e2fd87079d98470db90bebe57b33179     --hash=sha256:8caee47e970b92b3dd948371230fcceb80d3f2277b3bf7fbd7c0564e7d39068e     --hash=sha256:8d51dd1c59d5fa0f34266b80a3805ec29a1f26425c2a54736133f6d87fc4968a     --hash=sha256:940e3ec884520155f68a3b712d045e077d61c520a195d1a5932c531f11883489     --hash=sha256:a011ac6c03cfe162ff2b727bcb530567826cec85eb8d4ad2bfb4bd023287a52d     --hash=sha256:a3a035c37ce7565b8f4f35ff683a4db34d24e53dc487e47438e434eb3f701b2a     --hash=sha256:a5e771d0252e871ce194d0fdcafd13971f1aae0ddacc5f25615030d5df55c3a2     --hash=sha256:ac15b6c2c80a4d1338b04d42a02d376a53395ddf0ec9ab157cbaf44191f3ffdd     --hash=sha256:b1a82e0b9b3022799c336e1fc0f6210adc019ae84efb7321d668129d28ee1efb     --hash=sha256:bac71b4b28bc9af61efcdc7630b166440bbfbaa80940c9a697271b5e1dabbc61     --hash=sha256:bbc5b1d78a7822b0a84c6f8917faa986c1a744e65d762ef6d8be9d75677af2ca     --hash=sha256:c1a786ac592b47573a5bb7e35665c08064a5d77ab88a076eec11f8ae86b3e3f6     --hash=sha256:c84ad903d0d94311a2b7eea608da163dace97c5fe9412ea311e72c3684925602     --hash=sha256:d4d29cc612e1332237877dfa7fe687157973aab1d63bd0f84cf06692f04c0367     --hash=sha256:e3d9f8d1221baa0ced7ec7322a981e28deb23749c76eeeb3d33e18b72935ab62     --hash=sha256:e7cd5c1325f6808b8ae31657d281aadb2a51ac11ab081ae335f4f7fc44c1721d     --hash=sha256:ed6091fa0adcc7e4ff944090cf203a52da35c37a130efa564ded02b7aff63bcd     --hash=sha256:ee73a2f5ca4ba44fa33b4d7d2c71e2c8a9e9f78d53f6507ad68e7d2ad5f64a22     --hash=sha256:f10193c69fc9d3d726e83bbf0f3d316f1847c3071c8c93d8090cf5f326b14309"}}, "rules_proto_grpc_python_pip_deps_38_grpclib": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_proto_grpc_python_pip_deps_38", "requirement": "grpclib==0.4.7     --hash=sha256:2988ef57c02b22b7a2e8e961792c41ccf97efc2ace91ae7a5b0de03c363823c3"}}, "rules_proto_grpc_python_pip_deps_38_h2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_proto_grpc_python_pip_deps_38", "requirement": "h2==4.1.0     --hash=sha256:03a46bcf682256c95b5fd9e9a99c1323584c3eec6440d379b9903d709476bc6d     --hash=sha256:a83aca08fbe7aacb79fec788c9c0bac936343560ed9ec18b82a13a12c28d2abb"}}, "rules_proto_grpc_python_pip_deps_38_hpack": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_proto_grpc_python_pip_deps_38", "requirement": "hpack==4.0.0     --hash=sha256:84a076fad3dc9a9f8063ccb8041ef100867b1878b25ef0ee63847a5d53818a6c     --hash=sha256:fc41de0c63e687ebffde81187a948221294896f6bdc0ae2312708df339430095"}}, "rules_proto_grpc_python_pip_deps_38_hyperframe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_proto_grpc_python_pip_deps_38", "requirement": "hyperframe==6.0.1     --hash=sha256:0ec6bafd80d8ad2195c4f03aacba3a8265e57bc4cff261e802bf39970ed02a15     --hash=sha256:ae510046231dc8e9ecb1a6586f63d2347bf4c8905914aa84ba585ae85f28a914"}}, "rules_proto_grpc_python_pip_deps_38_multidict": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_proto_grpc_python_pip_deps_38", "requirement": "multidict==6.0.5     --hash=sha256:01265f5e40f5a17f8241d52656ed27192be03bfa8764d88e8220141d1e4b3556     --hash=sha256:0275e35209c27a3f7951e1ce7aaf93ce0d163b28948444bec61dd7badc6d3f8c     --hash=sha256:04bde7a7b3de05732a4eb39c94574db1ec99abb56162d6c520ad26f83267de29     --hash=sha256:04da1bb8c8dbadf2a18a452639771951c662c5ad03aefe4884775454be322c9b     --hash=sha256:09a892e4a9fb47331da06948690ae38eaa2426de97b4ccbfafbdcbe5c8f37ff8     --hash=sha256:0d63c74e3d7ab26de115c49bffc92cc77ed23395303d496eae515d4204a625e7     --hash=sha256:107c0cdefe028703fb5dafe640a409cb146d44a6ae201e55b35a4af8e95457dd     --hash=sha256:141b43360bfd3bdd75f15ed811850763555a251e38b2405967f8e25fb43f7d40     --hash=sha256:14c2976aa9038c2629efa2c148022ed5eb4cb939e15ec7aace7ca932f48f9ba6     --hash=sha256:19fe01cea168585ba0f678cad6f58133db2aa14eccaf22f88e4a6dccadfad8b3     --hash=sha256:1d147090048129ce3c453f0292e7697d333db95e52616b3793922945804a433c     --hash=sha256:1d9ea7a7e779d7a3561aade7d596649fbecfa5c08a7674b11b423783217933f9     --hash=sha256:215ed703caf15f578dca76ee6f6b21b7603791ae090fbf1ef9d865571039ade5     --hash=sha256:21fd81c4ebdb4f214161be351eb5bcf385426bf023041da2fd9e60681f3cebae     --hash=sha256:220dd781e3f7af2c2c1053da9fa96d9cf3072ca58f057f4c5adaaa1cab8fc442     --hash=sha256:228b644ae063c10e7f324ab1ab6b548bdf6f8b47f3ec234fef1093bc2735e5f9     --hash=sha256:29bfeb0dff5cb5fdab2023a7a9947b3b4af63e9c47cae2a10ad58394b517fddc     --hash=sha256:2f4848aa3baa109e6ab81fe2006c77ed4d3cd1e0ac2c1fbddb7b1277c168788c     --hash=sha256:2faa5ae9376faba05f630d7e5e6be05be22913782b927b19d12b8145968a85ea     --hash=sha256:2ffc42c922dbfddb4a4c3b438eb056828719f07608af27d163191cb3e3aa6cc5     --hash=sha256:37b15024f864916b4951adb95d3a80c9431299080341ab9544ed148091b53f50     --hash=sha256:3cc2ad10255f903656017363cd59436f2111443a76f996584d1077e43ee51182     --hash=sha256:3d25f19500588cbc47dc19081d78131c32637c25804df8414463ec908631e453     --hash=sha256:403c0911cd5d5791605808b942c88a8155c2592e05332d2bf78f18697a5fa15e     --hash=sha256:411bf8515f3be9813d06004cac41ccf7d1cd46dfe233705933dd163b60e37600     --hash=sha256:425bf820055005bfc8aa9a0b99ccb52cc2f4070153e34b701acc98d201693733     --hash=sha256:435a0984199d81ca178b9ae2c26ec3d49692d20ee29bc4c11a2a8d4514c67eda     --hash=sha256:4a6a4f196f08c58c59e0b8ef8ec441d12aee4125a7d4f4fef000ccb22f8d7241     --hash=sha256:4cc0ef8b962ac7a5e62b9e826bd0cd5040e7d401bc45a6835910ed699037a461     --hash=sha256:51d035609b86722963404f711db441cf7134f1889107fb171a970c9701f92e1e     --hash=sha256:53689bb4e102200a4fafa9de9c7c3c212ab40a7ab2c8e474491914d2305f187e     --hash=sha256:55205d03e8a598cfc688c71ca8ea5f66447164efff8869517f175ea632c7cb7b     --hash=sha256:5c0631926c4f58e9a5ccce555ad7747d9a9f8b10619621f22f9635f069f6233e     --hash=sha256:5cb241881eefd96b46f89b1a056187ea8e9ba14ab88ba632e68d7a2ecb7aadf7     --hash=sha256:60d698e8179a42ec85172d12f50b1668254628425a6bd611aba022257cac1386     --hash=sha256:612d1156111ae11d14afaf3a0669ebf6c170dbb735e510a7438ffe2369a847fd     --hash=sha256:6214c5a5571802c33f80e6c84713b2c79e024995b9c5897f794b43e714daeec9     --hash=sha256:6939c95381e003f54cd4c5516740faba40cf5ad3eeff460c3ad1d3e0ea2549bf     --hash=sha256:69db76c09796b313331bb7048229e3bee7928eb62bab5e071e9f7fcc4879caee     --hash=sha256:6bf7a982604375a8d49b6cc1b781c1747f243d91b81035a9b43a2126c04766f5     --hash=sha256:766c8f7511df26d9f11cd3a8be623e59cca73d44643abab3f8c8c07620524e4a     --hash=sha256:76c0de87358b192de7ea9649beb392f107dcad9ad27276324c24c91774ca5271     --hash=sha256:76f067f5121dcecf0d63a67f29080b26c43c71a98b10c701b0677e4a065fbd54     --hash=sha256:7901c05ead4b3fb75113fb1dd33eb1253c6d3ee37ce93305acd9d38e0b5f21a4     --hash=sha256:79660376075cfd4b2c80f295528aa6beb2058fd289f4c9252f986751a4cd0496     --hash=sha256:79a6d2ba910adb2cbafc95dad936f8b9386e77c84c35bc0add315b856d7c3abb     --hash=sha256:7afcdd1fc07befad18ec4523a782cde4e93e0a2bf71239894b8d61ee578c1319     --hash=sha256:7be7047bd08accdb7487737631d25735c9a04327911de89ff1b26b81745bd4e3     --hash=sha256:7c6390cf87ff6234643428991b7359b5f59cc15155695deb4eda5c777d2b880f     --hash=sha256:7df704ca8cf4a073334e0427ae2345323613e4df18cc224f647f251e5e75a527     --hash=sha256:85f67aed7bb647f93e7520633d8f51d3cbc6ab96957c71272b286b2f30dc70ed     --hash=sha256:896ebdcf62683551312c30e20614305f53125750803b614e9e6ce74a96232604     --hash=sha256:92d16a3e275e38293623ebf639c471d3e03bb20b8ebb845237e0d3664914caef     --hash=sha256:99f60d34c048c5c2fabc766108c103612344c46e35d4ed9ae0673d33c8fb26e8     --hash=sha256:9fe7b0653ba3d9d65cbe7698cca585bf0f8c83dbbcc710db9c90f478e175f2d5     --hash=sha256:a3145cb08d8625b2d3fee1b2d596a8766352979c9bffe5d7833e0503d0f0b5e5     --hash=sha256:aeaf541ddbad8311a87dd695ed9642401131ea39ad7bc8cf3ef3967fd093b626     --hash=sha256:b55358304d7a73d7bdf5de62494aaf70bd33015831ffd98bc498b433dfe5b10c     --hash=sha256:b82cc8ace10ab5bd93235dfaab2021c70637005e1ac787031f4d1da63d493c1d     --hash=sha256:c0868d64af83169e4d4152ec612637a543f7a336e4a307b119e98042e852ad9c     --hash=sha256:c1c1496e73051918fcd4f58ff2e0f2f3066d1c76a0c6aeffd9b45d53243702cc     --hash=sha256:c9bf56195c6bbd293340ea82eafd0071cb3d450c703d2c93afb89f93b8386ccc     --hash=sha256:cbebcd5bcaf1eaf302617c114aa67569dd3f090dd0ce8ba9e35e9985b41ac35b     --hash=sha256:cd6c8fca38178e12c00418de737aef1261576bd1b6e8c6134d3e729a4e858b38     --hash=sha256:ceb3b7e6a0135e092de86110c5a74e46bda4bd4fbfeeb3a3bcec79c0f861e450     --hash=sha256:cf590b134eb70629e350691ecca88eac3e3b8b3c86992042fb82e3cb1830d5e1     --hash=sha256:d3eb1ceec286eba8220c26f3b0096cf189aea7057b6e7b7a2e60ed36b373b77f     --hash=sha256:d65f25da8e248202bd47445cec78e0025c0fe7582b23ec69c3b27a640dd7a8e3     --hash=sha256:d6f6d4f185481c9669b9447bf9d9cf3b95a0e9df9d169bbc17e363b7d5487755     --hash=sha256:d84a5c3a5f7ce6db1f999fb9438f686bc2e09d38143f2d93d8406ed2dd6b9226     --hash=sha256:d946b0a9eb8aaa590df1fe082cee553ceab173e6cb5b03239716338629c50c7a     --hash=sha256:dce1c6912ab9ff5f179eaf6efe7365c1f425ed690b03341911bf4939ef2f3046     --hash=sha256:de170c7b4fe6859beb8926e84f7d7d6c693dfe8e27372ce3b76f01c46e489fcf     --hash=sha256:e02021f87a5b6932fa6ce916ca004c4d441509d33bbdbeca70d05dff5e9d2479     --hash=sha256:e030047e85cbcedbfc073f71836d62dd5dadfbe7531cae27789ff66bc551bd5e     --hash=sha256:e0e79d91e71b9867c73323a3444724d496c037e578a0e1755ae159ba14f4f3d1     --hash=sha256:e4428b29611e989719874670fd152b6625500ad6c686d464e99f5aaeeaca175a     --hash=sha256:e4972624066095e52b569e02b5ca97dbd7a7ddd4294bf4e7247d52635630dd83     --hash=sha256:e7be68734bd8c9a513f2b0cfd508802d6609da068f40dc57d4e3494cefc92929     --hash=sha256:e8e94e6912639a02ce173341ff62cc1201232ab86b8a8fcc05572741a5dc7d93     --hash=sha256:ea1456df2a27c73ce51120fa2f519f1bea2f4a03a917f4a43c8707cf4cbbae1a     --hash=sha256:ebd8d160f91a764652d3e51ce0d2956b38efe37c9231cd82cfc0bed2e40b581c     --hash=sha256:eca2e9d0cc5a889850e9bbd68e98314ada174ff6ccd1129500103df7a94a7a44     --hash=sha256:edd08e6f2f1a390bf137080507e44ccc086353c8e98c657e666c017718561b89     --hash=sha256:f285e862d2f153a70586579c15c44656f888806ed0e5b56b64489afe4a2dbfba     --hash=sha256:f2a1dee728b52b33eebff5072817176c172050d44d67befd681609b4746e1c2e     --hash=sha256:f7e301075edaf50500f0b341543c41194d8df3ae5caf4702f2095f3ca73dd8da     --hash=sha256:fb616be3538599e797a2017cccca78e354c767165e8858ab5116813146041a24     --hash=sha256:fce28b3c8a81b6b36dfac9feb1de115bab619b3c13905b419ec71d03a3fc1423     --hash=sha256:fe5d7785250541f7f5019ab9cba2c71169dc7d74d0f45253f8313f436458a4ef"}}, "rules_proto_grpc_python_pip_deps_38_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_8_host//:python", "repo": "rules_proto_grpc_python_pip_deps_38", "requirement": "protobuf==5.29.2     --hash=sha256:13d6d617a2a9e0e82a88113d7191a1baa1e42c2cc6f5f1398d3b054c8e7e714a     --hash=sha256:2d2e674c58a06311c8e99e74be43e7f3a8d1e2b2fdf845eaa347fbd866f23355     --hash=sha256:36000f97ea1e76e8398a3f02936aac2a5d2b111aae9920ec1b769fc4a222c4d9     --hash=sha256:494229ecd8c9009dd71eda5fd57528395d1eacdf307dbece6c12ad0dd09e912e     --hash=sha256:842de6d9241134a973aab719ab42b008a18a90f9f07f06ba480df268f86432f9     --hash=sha256:a0c53d78383c851bfa97eb42e3703aefdc96d2036a41482ffd55dc5f529466eb     --hash=sha256:b2cc8e8bb7c9326996f0e160137b0861f1a82162502658df2951209d0cb0309e     --hash=sha256:b6b0d416bbbb9d4fbf9d0561dbfc4e324fd522f61f7af0fe0f282ab67b22477e     --hash=sha256:c12ba8249f5624300cf51c3d0bfe5be71a60c63e4dcf51ffe9a68771d958c851     --hash=sha256:e621a98c0201a7c8afe89d9646859859be97cb22b8bf1d8eacfd90d5bda2eb19     --hash=sha256:fde4554c0e578a5a0bcc9a276339594848d1e89f9ea47b4427c80e5d72f90181"}}, "rules_proto_grpc_python_pip_deps_39_grpcio": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_proto_grpc_python_pip_deps_39", "requirement": "grpcio==1.64.1     --hash=sha256:03b43d0ccf99c557ec671c7dede64f023c7da9bb632ac65dbc57f166e4970040     --hash=sha256:0a12ddb1678ebc6a84ec6b0487feac020ee2b1659cbe69b80f06dbffdb249122     --hash=sha256:0a2813093ddb27418a4c99f9b1c223fab0b053157176a64cc9db0f4557b69bd9     --hash=sha256:0cc79c982ccb2feec8aad0e8fb0d168bcbca85bc77b080d0d3c5f2f15c24ea8f     --hash=sha256:1257b76748612aca0f89beec7fa0615727fd6f2a1ad580a9638816a4b2eb18fd     --hash=sha256:1262402af5a511c245c3ae918167eca57342c72320dffae5d9b51840c4b2f86d     --hash=sha256:19264fc964576ddb065368cae953f8d0514ecc6cb3da8903766d9fb9d4554c33     --hash=sha256:198908f9b22e2672a998870355e226a725aeab327ac4e6ff3a1399792ece4762     --hash=sha256:1de403fc1305fd96cfa75e83be3dee8538f2413a6b1685b8452301c7ba33c294     --hash=sha256:20405cb8b13fd779135df23fabadc53b86522d0f1cba8cca0e87968587f50650     --hash=sha256:2981c7365a9353f9b5c864595c510c983251b1ab403e05b1ccc70a3d9541a73b     --hash=sha256:2c3c1b90ab93fed424e454e93c0ed0b9d552bdf1b0929712b094f5ecfe7a23ad     --hash=sha256:39b9d0acaa8d835a6566c640f48b50054f422d03e77e49716d4c4e8e279665a1     --hash=sha256:3b64ae304c175671efdaa7ec9ae2cc36996b681eb63ca39c464958396697daff     --hash=sha256:4657d24c8063e6095f850b68f2d1ba3b39f2b287a38242dcabc166453e950c59     --hash=sha256:4d6dab6124225496010bd22690f2d9bd35c7cbb267b3f14e7a3eb05c911325d4     --hash=sha256:55260032b95c49bee69a423c2f5365baa9369d2f7d233e933564d8a47b893027     --hash=sha256:55697ecec192bc3f2f3cc13a295ab670f51de29884ca9ae6cd6247df55df2502     --hash=sha256:5841dd1f284bd1b3d8a6eca3a7f062b06f1eec09b184397e1d1d43447e89a7ae     --hash=sha256:58b1041e7c870bb30ee41d3090cbd6f0851f30ae4eb68228955d973d3efa2e61     --hash=sha256:5e42634a989c3aa6049f132266faf6b949ec2a6f7d302dbb5c15395b77d757eb     --hash=sha256:5e56462b05a6f860b72f0fa50dca06d5b26543a4e88d0396259a07dc30f4e5aa     --hash=sha256:5f8b75f64d5d324c565b263c67dbe4f0af595635bbdd93bb1a88189fc62ed2e5     --hash=sha256:62b4e6eb7bf901719fce0ca83e3ed474ae5022bb3827b0a501e056458c51c0a1     --hash=sha256:6503b64c8b2dfad299749cad1b595c650c91e5b2c8a1b775380fcf8d2cbba1e9     --hash=sha256:6c024ffc22d6dc59000faf8ad781696d81e8e38f4078cb0f2630b4a3cf231a90     --hash=sha256:73819689c169417a4f978e562d24f2def2be75739c4bed1992435d007819da1b     --hash=sha256:75dbbf415026d2862192fe1b28d71f209e2fd87079d98470db90bebe57b33179     --hash=sha256:8caee47e970b92b3dd948371230fcceb80d3f2277b3bf7fbd7c0564e7d39068e     --hash=sha256:8d51dd1c59d5fa0f34266b80a3805ec29a1f26425c2a54736133f6d87fc4968a     --hash=sha256:940e3ec884520155f68a3b712d045e077d61c520a195d1a5932c531f11883489     --hash=sha256:a011ac6c03cfe162ff2b727bcb530567826cec85eb8d4ad2bfb4bd023287a52d     --hash=sha256:a3a035c37ce7565b8f4f35ff683a4db34d24e53dc487e47438e434eb3f701b2a     --hash=sha256:a5e771d0252e871ce194d0fdcafd13971f1aae0ddacc5f25615030d5df55c3a2     --hash=sha256:ac15b6c2c80a4d1338b04d42a02d376a53395ddf0ec9ab157cbaf44191f3ffdd     --hash=sha256:b1a82e0b9b3022799c336e1fc0f6210adc019ae84efb7321d668129d28ee1efb     --hash=sha256:bac71b4b28bc9af61efcdc7630b166440bbfbaa80940c9a697271b5e1dabbc61     --hash=sha256:bbc5b1d78a7822b0a84c6f8917faa986c1a744e65d762ef6d8be9d75677af2ca     --hash=sha256:c1a786ac592b47573a5bb7e35665c08064a5d77ab88a076eec11f8ae86b3e3f6     --hash=sha256:c84ad903d0d94311a2b7eea608da163dace97c5fe9412ea311e72c3684925602     --hash=sha256:d4d29cc612e1332237877dfa7fe687157973aab1d63bd0f84cf06692f04c0367     --hash=sha256:e3d9f8d1221baa0ced7ec7322a981e28deb23749c76eeeb3d33e18b72935ab62     --hash=sha256:e7cd5c1325f6808b8ae31657d281aadb2a51ac11ab081ae335f4f7fc44c1721d     --hash=sha256:ed6091fa0adcc7e4ff944090cf203a52da35c37a130efa564ded02b7aff63bcd     --hash=sha256:ee73a2f5ca4ba44fa33b4d7d2c71e2c8a9e9f78d53f6507ad68e7d2ad5f64a22     --hash=sha256:f10193c69fc9d3d726e83bbf0f3d316f1847c3071c8c93d8090cf5f326b14309"}}, "rules_proto_grpc_python_pip_deps_39_grpclib": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_proto_grpc_python_pip_deps_39", "requirement": "grpclib==0.4.7     --hash=sha256:2988ef57c02b22b7a2e8e961792c41ccf97efc2ace91ae7a5b0de03c363823c3"}}, "rules_proto_grpc_python_pip_deps_39_h2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_proto_grpc_python_pip_deps_39", "requirement": "h2==4.1.0     --hash=sha256:03a46bcf682256c95b5fd9e9a99c1323584c3eec6440d379b9903d709476bc6d     --hash=sha256:a83aca08fbe7aacb79fec788c9c0bac936343560ed9ec18b82a13a12c28d2abb"}}, "rules_proto_grpc_python_pip_deps_39_hpack": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_proto_grpc_python_pip_deps_39", "requirement": "hpack==4.0.0     --hash=sha256:84a076fad3dc9a9f8063ccb8041ef100867b1878b25ef0ee63847a5d53818a6c     --hash=sha256:fc41de0c63e687ebffde81187a948221294896f6bdc0ae2312708df339430095"}}, "rules_proto_grpc_python_pip_deps_39_hyperframe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_proto_grpc_python_pip_deps_39", "requirement": "hyperframe==6.0.1     --hash=sha256:0ec6bafd80d8ad2195c4f03aacba3a8265e57bc4cff261e802bf39970ed02a15     --hash=sha256:ae510046231dc8e9ecb1a6586f63d2347bf4c8905914aa84ba585ae85f28a914"}}, "rules_proto_grpc_python_pip_deps_39_multidict": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_proto_grpc_python_pip_deps_39", "requirement": "multidict==6.0.5     --hash=sha256:01265f5e40f5a17f8241d52656ed27192be03bfa8764d88e8220141d1e4b3556     --hash=sha256:0275e35209c27a3f7951e1ce7aaf93ce0d163b28948444bec61dd7badc6d3f8c     --hash=sha256:04bde7a7b3de05732a4eb39c94574db1ec99abb56162d6c520ad26f83267de29     --hash=sha256:04da1bb8c8dbadf2a18a452639771951c662c5ad03aefe4884775454be322c9b     --hash=sha256:09a892e4a9fb47331da06948690ae38eaa2426de97b4ccbfafbdcbe5c8f37ff8     --hash=sha256:0d63c74e3d7ab26de115c49bffc92cc77ed23395303d496eae515d4204a625e7     --hash=sha256:107c0cdefe028703fb5dafe640a409cb146d44a6ae201e55b35a4af8e95457dd     --hash=sha256:141b43360bfd3bdd75f15ed811850763555a251e38b2405967f8e25fb43f7d40     --hash=sha256:14c2976aa9038c2629efa2c148022ed5eb4cb939e15ec7aace7ca932f48f9ba6     --hash=sha256:19fe01cea168585ba0f678cad6f58133db2aa14eccaf22f88e4a6dccadfad8b3     --hash=sha256:1d147090048129ce3c453f0292e7697d333db95e52616b3793922945804a433c     --hash=sha256:1d9ea7a7e779d7a3561aade7d596649fbecfa5c08a7674b11b423783217933f9     --hash=sha256:215ed703caf15f578dca76ee6f6b21b7603791ae090fbf1ef9d865571039ade5     --hash=sha256:21fd81c4ebdb4f214161be351eb5bcf385426bf023041da2fd9e60681f3cebae     --hash=sha256:220dd781e3f7af2c2c1053da9fa96d9cf3072ca58f057f4c5adaaa1cab8fc442     --hash=sha256:228b644ae063c10e7f324ab1ab6b548bdf6f8b47f3ec234fef1093bc2735e5f9     --hash=sha256:29bfeb0dff5cb5fdab2023a7a9947b3b4af63e9c47cae2a10ad58394b517fddc     --hash=sha256:2f4848aa3baa109e6ab81fe2006c77ed4d3cd1e0ac2c1fbddb7b1277c168788c     --hash=sha256:2faa5ae9376faba05f630d7e5e6be05be22913782b927b19d12b8145968a85ea     --hash=sha256:2ffc42c922dbfddb4a4c3b438eb056828719f07608af27d163191cb3e3aa6cc5     --hash=sha256:37b15024f864916b4951adb95d3a80c9431299080341ab9544ed148091b53f50     --hash=sha256:3cc2ad10255f903656017363cd59436f2111443a76f996584d1077e43ee51182     --hash=sha256:3d25f19500588cbc47dc19081d78131c32637c25804df8414463ec908631e453     --hash=sha256:403c0911cd5d5791605808b942c88a8155c2592e05332d2bf78f18697a5fa15e     --hash=sha256:411bf8515f3be9813d06004cac41ccf7d1cd46dfe233705933dd163b60e37600     --hash=sha256:425bf820055005bfc8aa9a0b99ccb52cc2f4070153e34b701acc98d201693733     --hash=sha256:435a0984199d81ca178b9ae2c26ec3d49692d20ee29bc4c11a2a8d4514c67eda     --hash=sha256:4a6a4f196f08c58c59e0b8ef8ec441d12aee4125a7d4f4fef000ccb22f8d7241     --hash=sha256:4cc0ef8b962ac7a5e62b9e826bd0cd5040e7d401bc45a6835910ed699037a461     --hash=sha256:51d035609b86722963404f711db441cf7134f1889107fb171a970c9701f92e1e     --hash=sha256:53689bb4e102200a4fafa9de9c7c3c212ab40a7ab2c8e474491914d2305f187e     --hash=sha256:55205d03e8a598cfc688c71ca8ea5f66447164efff8869517f175ea632c7cb7b     --hash=sha256:5c0631926c4f58e9a5ccce555ad7747d9a9f8b10619621f22f9635f069f6233e     --hash=sha256:5cb241881eefd96b46f89b1a056187ea8e9ba14ab88ba632e68d7a2ecb7aadf7     --hash=sha256:60d698e8179a42ec85172d12f50b1668254628425a6bd611aba022257cac1386     --hash=sha256:612d1156111ae11d14afaf3a0669ebf6c170dbb735e510a7438ffe2369a847fd     --hash=sha256:6214c5a5571802c33f80e6c84713b2c79e024995b9c5897f794b43e714daeec9     --hash=sha256:6939c95381e003f54cd4c5516740faba40cf5ad3eeff460c3ad1d3e0ea2549bf     --hash=sha256:69db76c09796b313331bb7048229e3bee7928eb62bab5e071e9f7fcc4879caee     --hash=sha256:6bf7a982604375a8d49b6cc1b781c1747f243d91b81035a9b43a2126c04766f5     --hash=sha256:766c8f7511df26d9f11cd3a8be623e59cca73d44643abab3f8c8c07620524e4a     --hash=sha256:76c0de87358b192de7ea9649beb392f107dcad9ad27276324c24c91774ca5271     --hash=sha256:76f067f5121dcecf0d63a67f29080b26c43c71a98b10c701b0677e4a065fbd54     --hash=sha256:7901c05ead4b3fb75113fb1dd33eb1253c6d3ee37ce93305acd9d38e0b5f21a4     --hash=sha256:79660376075cfd4b2c80f295528aa6beb2058fd289f4c9252f986751a4cd0496     --hash=sha256:79a6d2ba910adb2cbafc95dad936f8b9386e77c84c35bc0add315b856d7c3abb     --hash=sha256:7afcdd1fc07befad18ec4523a782cde4e93e0a2bf71239894b8d61ee578c1319     --hash=sha256:7be7047bd08accdb7487737631d25735c9a04327911de89ff1b26b81745bd4e3     --hash=sha256:7c6390cf87ff6234643428991b7359b5f59cc15155695deb4eda5c777d2b880f     --hash=sha256:7df704ca8cf4a073334e0427ae2345323613e4df18cc224f647f251e5e75a527     --hash=sha256:85f67aed7bb647f93e7520633d8f51d3cbc6ab96957c71272b286b2f30dc70ed     --hash=sha256:896ebdcf62683551312c30e20614305f53125750803b614e9e6ce74a96232604     --hash=sha256:92d16a3e275e38293623ebf639c471d3e03bb20b8ebb845237e0d3664914caef     --hash=sha256:99f60d34c048c5c2fabc766108c103612344c46e35d4ed9ae0673d33c8fb26e8     --hash=sha256:9fe7b0653ba3d9d65cbe7698cca585bf0f8c83dbbcc710db9c90f478e175f2d5     --hash=sha256:a3145cb08d8625b2d3fee1b2d596a8766352979c9bffe5d7833e0503d0f0b5e5     --hash=sha256:aeaf541ddbad8311a87dd695ed9642401131ea39ad7bc8cf3ef3967fd093b626     --hash=sha256:b55358304d7a73d7bdf5de62494aaf70bd33015831ffd98bc498b433dfe5b10c     --hash=sha256:b82cc8ace10ab5bd93235dfaab2021c70637005e1ac787031f4d1da63d493c1d     --hash=sha256:c0868d64af83169e4d4152ec612637a543f7a336e4a307b119e98042e852ad9c     --hash=sha256:c1c1496e73051918fcd4f58ff2e0f2f3066d1c76a0c6aeffd9b45d53243702cc     --hash=sha256:c9bf56195c6bbd293340ea82eafd0071cb3d450c703d2c93afb89f93b8386ccc     --hash=sha256:cbebcd5bcaf1eaf302617c114aa67569dd3f090dd0ce8ba9e35e9985b41ac35b     --hash=sha256:cd6c8fca38178e12c00418de737aef1261576bd1b6e8c6134d3e729a4e858b38     --hash=sha256:ceb3b7e6a0135e092de86110c5a74e46bda4bd4fbfeeb3a3bcec79c0f861e450     --hash=sha256:cf590b134eb70629e350691ecca88eac3e3b8b3c86992042fb82e3cb1830d5e1     --hash=sha256:d3eb1ceec286eba8220c26f3b0096cf189aea7057b6e7b7a2e60ed36b373b77f     --hash=sha256:d65f25da8e248202bd47445cec78e0025c0fe7582b23ec69c3b27a640dd7a8e3     --hash=sha256:d6f6d4f185481c9669b9447bf9d9cf3b95a0e9df9d169bbc17e363b7d5487755     --hash=sha256:d84a5c3a5f7ce6db1f999fb9438f686bc2e09d38143f2d93d8406ed2dd6b9226     --hash=sha256:d946b0a9eb8aaa590df1fe082cee553ceab173e6cb5b03239716338629c50c7a     --hash=sha256:dce1c6912ab9ff5f179eaf6efe7365c1f425ed690b03341911bf4939ef2f3046     --hash=sha256:de170c7b4fe6859beb8926e84f7d7d6c693dfe8e27372ce3b76f01c46e489fcf     --hash=sha256:e02021f87a5b6932fa6ce916ca004c4d441509d33bbdbeca70d05dff5e9d2479     --hash=sha256:e030047e85cbcedbfc073f71836d62dd5dadfbe7531cae27789ff66bc551bd5e     --hash=sha256:e0e79d91e71b9867c73323a3444724d496c037e578a0e1755ae159ba14f4f3d1     --hash=sha256:e4428b29611e989719874670fd152b6625500ad6c686d464e99f5aaeeaca175a     --hash=sha256:e4972624066095e52b569e02b5ca97dbd7a7ddd4294bf4e7247d52635630dd83     --hash=sha256:e7be68734bd8c9a513f2b0cfd508802d6609da068f40dc57d4e3494cefc92929     --hash=sha256:e8e94e6912639a02ce173341ff62cc1201232ab86b8a8fcc05572741a5dc7d93     --hash=sha256:ea1456df2a27c73ce51120fa2f519f1bea2f4a03a917f4a43c8707cf4cbbae1a     --hash=sha256:ebd8d160f91a764652d3e51ce0d2956b38efe37c9231cd82cfc0bed2e40b581c     --hash=sha256:eca2e9d0cc5a889850e9bbd68e98314ada174ff6ccd1129500103df7a94a7a44     --hash=sha256:edd08e6f2f1a390bf137080507e44ccc086353c8e98c657e666c017718561b89     --hash=sha256:f285e862d2f153a70586579c15c44656f888806ed0e5b56b64489afe4a2dbfba     --hash=sha256:f2a1dee728b52b33eebff5072817176c172050d44d67befd681609b4746e1c2e     --hash=sha256:f7e301075edaf50500f0b341543c41194d8df3ae5caf4702f2095f3ca73dd8da     --hash=sha256:fb616be3538599e797a2017cccca78e354c767165e8858ab5116813146041a24     --hash=sha256:fce28b3c8a81b6b36dfac9feb1de115bab619b3c13905b419ec71d03a3fc1423     --hash=sha256:fe5d7785250541f7f5019ab9cba2c71169dc7d74d0f45253f8313f436458a4ef"}}, "rules_proto_grpc_python_pip_deps_39_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_proto_grpc_python_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "rules_proto_grpc_python_pip_deps_39", "requirement": "protobuf==5.29.2     --hash=sha256:13d6d617a2a9e0e82a88113d7191a1baa1e42c2cc6f5f1398d3b054c8e7e714a     --hash=sha256:2d2e674c58a06311c8e99e74be43e7f3a8d1e2b2fdf845eaa347fbd866f23355     --hash=sha256:36000f97ea1e76e8398a3f02936aac2a5d2b111aae9920ec1b769fc4a222c4d9     --hash=sha256:494229ecd8c9009dd71eda5fd57528395d1eacdf307dbece6c12ad0dd09e912e     --hash=sha256:842de6d9241134a973aab719ab42b008a18a90f9f07f06ba480df268f86432f9     --hash=sha256:a0c53d78383c851bfa97eb42e3703aefdc96d2036a41482ffd55dc5f529466eb     --hash=sha256:b2cc8e8bb7c9326996f0e160137b0861f1a82162502658df2951209d0cb0309e     --hash=sha256:b6b0d416bbbb9d4fbf9d0561dbfc4e324fd522f61f7af0fe0f282ab67b22477e     --hash=sha256:c12ba8249f5624300cf51c3d0bfe5be71a60c63e4dcf51ffe9a68771d958c851     --hash=sha256:e621a98c0201a7c8afe89d9646859859be97cb22b8bf1d8eacfd90d5bda2eb19     --hash=sha256:fde4554c0e578a5a0bcc9a276339594848d1e89f9ea47b4427c80e5d72f90181"}}, "rules_python_publish_deps_311_backports_tarfile_py3_none_any_77e284d7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "backports.tarfile-1.2.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "backports-tarfile==1.2.0", "sha256": "77e284d754527b01fb1e6fa8a1afe577858ebe4e9dad8919e34c862cb399bc34", "urls": ["https://files.pythonhosted.org/packages/b9/fa/123043af240e49752f1c4bd24da5053b6bd00cad78c2be53c0d1e8b975bc/backports.tarfile-1.2.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_backports_tarfile_sdist_d75e02c2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "backports_tarfile-1.2.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "backports-tarfile==1.2.0", "sha256": "d75e02c268746e1b8144c278978b6e98e85de6ad16f8e4b0844a154557eca991", "urls": ["https://files.pythonhosted.org/packages/86/72/cd9b395f25e290e633655a100af28cb253e4393396264a98bd5f5951d50f/backports_tarfile-1.2.0.tar.gz"]}}, "rules_python_publish_deps_311_certifi_py3_none_any_922820b5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "certifi-2024.8.30-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "certifi==2024.8.30", "sha256": "922820b53db7a7257ffbda3f597266d435245903d80737e34f8a45ff3e3230d8", "urls": ["https://files.pythonhosted.org/packages/12/90/3c9ff0512038035f59d279fddeb79f5f1eccd8859f06d6163c58798b9487/certifi-2024.8.30-py3-none-any.whl"]}}, "rules_python_publish_deps_311_certifi_sdist_bec941d2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "certifi-2024.8.30.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "certifi==2024.8.30", "sha256": "bec941d2aa8195e248a60b31ff9f0558284cf01a52591ceda73ea9afffd69fd9", "urls": ["https://files.pythonhosted.org/packages/b0/ee/9b19140fe824b367c04c5e1b369942dd754c4c5462d5674002f75c4dedc1/certifi-2024.8.30.tar.gz"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_aarch64_a1ed2dd2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "a1ed2dd2972641495a3ec98445e09766f077aee98a1c896dcb4ad0d303628e41", "urls": ["https://files.pythonhosted.org/packages/2e/ea/70ce63780f096e16ce8588efe039d3c4f91deb1dc01e9c73a287939c79a6/cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_ppc64le_46bf4316": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "46bf43160c1a35f7ec506d254e5c890f3c03648a4dbac12d624e4490a7046cd1", "urls": ["https://files.pythonhosted.org/packages/1c/a0/a4fa9f4f781bda074c3ddd57a572b060fa0df7655d2a4247bbe277200146/cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_s390x_a24ed04c": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "a24ed04c8ffd54b0729c07cee15a81d964e6fee0e3d4d342a27b020d22959dc6", "urls": ["https://files.pythonhosted.org/packages/62/12/ce8710b5b8affbcdd5c6e367217c242524ad17a02fe5beec3ee339f69f85/cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_x86_64_610faea7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "610faea79c43e44c71e1ec53a554553fa22321b65fae24889706c0a84d4ad86d", "urls": ["https://files.pythonhosted.org/packages/ff/6b/d45873c5e0242196f042d555526f92aa9e0c32355a1be1ff8c27f077fd37/cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_aarch64_a9b15d49": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "a9b15d491f3ad5d692e11f6b71f7857e7835eb677955c00cc0aefcd0669adaf6", "urls": ["https://files.pythonhosted.org/packages/1a/52/d9a0e523a572fbccf2955f5abe883cfa8bcc570d7faeee06336fbd50c9fc/cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_x86_64_fc48c783": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "fc48c783f9c87e60831201f2cce7f3b2e4846bf4d8728eabe54d60700b318a0b", "urls": ["https://files.pythonhosted.org/packages/f8/4a/34599cac7dfcd888ff54e801afe06a19c17787dfd94495ab0c8d35fe99fb/cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl"]}}, "rules_python_publish_deps_311_cffi_sdist_1c39c601": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "cffi-1.17.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824", "urls": ["https://files.pythonhosted.org/packages/fc/97/c783634659c2920c3fc70419e3af40972dbaf758daa229a7d6ea6135c90d/cffi-1.17.1.tar.gz"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_universal2_0d99dd8f": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_universal2.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "0d99dd8ff461990f12d6e42c7347fd9ab2532fb70e9621ba520f9e8637161d7c", "urls": ["https://files.pythonhosted.org/packages/9c/61/73589dcc7a719582bf56aae309b6103d2762b526bffe189d635a7fcfd998/charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_universal2.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_x86_64_c57516e5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "c57516e58fd17d03ebe67e181a4e4e2ccab1168f8c2976c6a334d4f819fe5944", "urls": ["https://files.pythonhosted.org/packages/77/d5/8c982d58144de49f59571f940e329ad6e8615e1e82ef84584c5eeb5e1d72/charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_x86_64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_11_0_arm64_6dba5d19": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-macosx_11_0_arm64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "6dba5d19c4dfab08e58d5b36304b3f92f3bd5d42c1a3fa37b5ba5cdf6dfcbcee", "urls": ["https://files.pythonhosted.org/packages/bf/19/411a64f01ee971bed3231111b69eb56f9331a769072de479eae7de52296d/charset_normalizer-3.4.0-cp311-cp311-macosx_11_0_arm64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_aarch64_bf4475b8": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "bf4475b82be41b07cc5e5ff94810e6a01f276e37c2d55571e3fe175e467a1a1c", "urls": ["https://files.pythonhosted.org/packages/4c/92/97509850f0d00e9f14a46bc751daabd0ad7765cff29cdfb66c68b6dad57f/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_ppc64le_ce031db0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "ce031db0408e487fd2775d745ce30a7cd2923667cf3b69d48d219f1d8f5ddeb6", "urls": ["https://files.pythonhosted.org/packages/e2/29/d227805bff72ed6d6cb1ce08eec707f7cfbd9868044893617eb331f16295/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_s390x_8ff4e7cd": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "8ff4e7cdfdb1ab5698e675ca622e72d58a6fa2a8aa58195de0c0061288e6e3ea", "urls": ["https://files.pythonhosted.org/packages/13/bc/87c2c9f2c144bedfa62f894c3007cd4530ba4b5351acb10dc786428a50f0/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_x86_64_3710a975": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "3710a9751938947e6327ea9f3ea6332a09bf0ba0c09cae9cb1f250bd1f1549bc", "urls": ["https://files.pythonhosted.org/packages/eb/5b/6f10bad0f6461fa272bfbbdf5d0023b5fb9bc6217c92bf068fa5a99820f5/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_aarch64_47334db7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "47334db71978b23ebcf3c0f9f5ee98b8d65992b65c9c4f2d34c2eaf5bcaf0594", "urls": ["https://files.pythonhosted.org/packages/d7/a1/493919799446464ed0299c8eef3c3fad0daf1c3cd48bff9263c731b0d9e2/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_aarch64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_ppc64le_f1a2f519": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "f1a2f519ae173b5b6a2c9d5fa3116ce16e48b3462c8b96dfdded11055e3d6365", "urls": ["https://files.pythonhosted.org/packages/75/d2/0ab54463d3410709c09266dfb416d032a08f97fd7d60e94b8c6ef54ae14b/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_ppc64le.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_s390x_63bc5c4a": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "63bc5c4ae26e4bc6be6469943b8253c0fd4e4186c43ad46e713ea61a0ba49129", "urls": ["https://files.pythonhosted.org/packages/8d/c9/27e41d481557be53d51e60750b85aa40eaf52b841946b3cdeff363105737/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_s390x.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_x86_64_bcb4f8ea": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "bcb4f8ea87d03bc51ad04add8ceaf9b0f085ac045ab4d74e73bbc2dc033f0236", "urls": ["https://files.pythonhosted.org/packages/ee/44/4f62042ca8cdc0cabf87c0fc00ae27cd8b53ab68be3605ba6d071f742ad3/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_x86_64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_win_amd64_cee4373f": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-win_amd64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "cee4373f4d3ad28f1ab6290684d8e2ebdb9e7a1b74fdc39e4c211995f77bec27", "urls": ["https://files.pythonhosted.org/packages/0b/6e/b13bd47fa9023b3699e94abf565b5a2f0b0be6e9ddac9812182596ee62e4/charset_normalizer-3.4.0-cp311-cp311-win_amd64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_py3_none_any_fe9f97fe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "fe9f97feb71aa9896b81973a7bbada8c49501dc73e58a10fcef6663af95e5079", "urls": ["https://files.pythonhosted.org/packages/bf/9b/08c0432272d77b04803958a4598a51e2a4b51c06640af8b8f0f908c18bf2/charset_normalizer-3.4.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_sdist_223217c3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "charset_normalizer-3.4.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "223217c3d4f82c3ac5e29032b3f1c2eb0fb591b72161f86d93f5719079dae93e", "urls": ["https://files.pythonhosted.org/packages/f2/4f/e1808dc01273379acc506d18f1504eb2d299bd4131743b9fc54d7be4df1e/charset_normalizer-3.4.0.tar.gz"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_aarch64_846da004": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "846da004a5804145a5f441b8530b4bf35afbf7da70f82409f151695b127213d5", "urls": ["https://files.pythonhosted.org/packages/2f/78/55356eb9075d0be6e81b59f45c7b48df87f76a20e73893872170471f3ee8/cryptography-43.0.3-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_x86_64_0f996e72": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "0f996e7268af62598f2fc1204afa98a3b5712313a55c4c9d434aef49cadc91d4", "urls": ["https://files.pythonhosted.org/packages/2a/2c/488776a3dc843f95f86d2f957ca0fc3407d0242b50bede7fad1e339be03f/cryptography-43.0.3-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_aarch64_f7b178f1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_28_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "f7b178f11ed3664fd0e995a47ed2b5ff0a12d893e41dd0494f406d1cf555cab7", "urls": ["https://files.pythonhosted.org/packages/7c/04/2345ca92f7a22f601a9c62961741ef7dd0127c39f7310dffa0041c80f16f/cryptography-43.0.3-cp39-abi3-manylinux_2_28_aarch64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_x86_64_c2e6fc39": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_28_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "c2e6fc39c4ab499049df3bdf567f768a723a5e8464816e8f009f121a5a9f4405", "urls": ["https://files.pythonhosted.org/packages/ac/25/e715fa0bc24ac2114ed69da33adf451a38abb6f3f24ec207908112e9ba53/cryptography-43.0.3-cp39-abi3-manylinux_2_28_x86_64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_aarch64_e1be4655": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-musllinux_1_2_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "e1be4655c7ef6e1bbe6b5d0403526601323420bcf414598955968c9ef3eb7d16", "urls": ["https://files.pythonhosted.org/packages/21/ce/b9c9ff56c7164d8e2edfb6c9305045fbc0df4508ccfdb13ee66eb8c95b0e/cryptography-43.0.3-cp39-abi3-musllinux_1_2_aarch64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_x86_64_df6b6c6d": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-musllinux_1_2_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "df6b6c6d742395dd77a23ea3728ab62f98379eff8fb61be2744d4679ab678f73", "urls": ["https://files.pythonhosted.org/packages/2a/33/b3682992ab2e9476b9c81fff22f02c8b0a1e6e1d49ee1750a67d85fd7ed2/cryptography-43.0.3-cp39-abi3-musllinux_1_2_x86_64.whl"]}}, "rules_python_publish_deps_311_cryptography_sdist_315b9001": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "cryptography-43.0.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "315b9001266a492a6ff443b61238f956b214dbec9910a081ba5b6646a055a805", "urls": ["https://files.pythonhosted.org/packages/0d/05/07b55d1fa21ac18c3a8c79f764e2514e6f6a9698f1be44994f5adf0d29db/cryptography-43.0.3.tar.gz"]}}, "rules_python_publish_deps_311_docutils_py3_none_any_dafca5b9": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "docutils-0.21.2-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "docutils==0.21.2", "sha256": "dafca5b9e384f0e419294eb4d2ff9fa826435bf15f15b7bd45723e8ad76811b2", "urls": ["https://files.pythonhosted.org/packages/8f/d7/9322c609343d929e75e7e5e6255e614fcc67572cfd083959cdef3b7aad79/docutils-0.21.2-py3-none-any.whl"]}}, "rules_python_publish_deps_311_docutils_sdist_3a6b1873": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "docutils-0.21.2.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "docutils==0.21.2", "sha256": "3a6b18732edf182daa3cd12775bbb338cf5691468f91eeeb109deff6ebfa986f", "urls": ["https://files.pythonhosted.org/packages/ae/ed/aefcc8cd0ba62a0560c3c18c33925362d46c6075480bfa4df87b28e169a9/docutils-0.21.2.tar.gz"]}}, "rules_python_publish_deps_311_idna_py3_none_any_946d195a": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "idna-3.10-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "idna==3.10", "sha256": "946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", "urls": ["https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl"]}}, "rules_python_publish_deps_311_idna_sdist_12f65c9b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "idna-3.10.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "idna==3.10", "sha256": "12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9", "urls": ["https://files.pythonhosted.org/packages/f1/70/7703c29685631f5a7590aa73f1f1d3fa9a380e654b86af429e0934a32f7d/idna-3.10.tar.gz"]}}, "rules_python_publish_deps_311_importlib_metadata_py3_none_any_45e54197": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "importlib_metadata-8.5.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "importlib-metadata==8.5.0", "sha256": "45e54197d28b7a7f1559e60b95e7c567032b602131fbd588f1497f47880aa68b", "urls": ["https://files.pythonhosted.org/packages/a0/d9/a1e041c5e7caa9a05c925f4bdbdfb7f006d1f74996af53467bc394c97be7/importlib_metadata-8.5.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_importlib_metadata_sdist_71522656": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "importlib_metadata-8.5.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "importlib-metadata==8.5.0", "sha256": "71522656f0abace1d072b9e5481a48f07c138e00f079c38c8f883823f9c26bd7", "urls": ["https://files.pythonhosted.org/packages/cd/12/33e59336dca5be0c398a7482335911a33aa0e20776128f038019f1a95f1b/importlib_metadata-8.5.0.tar.gz"]}}, "rules_python_publish_deps_311_jaraco_classes_py3_none_any_f662826b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "jaraco.classes-3.4.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-classes==3.4.0", "sha256": "f662826b6bed8cace05e7ff873ce0f9283b5c924470fe664fff1c2f00f581790", "urls": ["https://files.pythonhosted.org/packages/7f/66/b15ce62552d84bbfcec9a4873ab79d993a1dd4edb922cbfccae192bd5b5f/jaraco.classes-3.4.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jaraco_classes_sdist_47a024b5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jaraco.classes-3.4.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-classes==3.4.0", "sha256": "47a024b51d0239c0dd8c8540c6c7f484be3b8fcf0b2d85c13825780d3b3f3acd", "urls": ["https://files.pythonhosted.org/packages/06/c0/ed4a27bc5571b99e3cff68f8a9fa5b56ff7df1c2251cc715a652ddd26402/jaraco.classes-3.4.0.tar.gz"]}}, "rules_python_publish_deps_311_jaraco_context_py3_none_any_f797fc48": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "jaraco.context-6.0.1-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-context==6.0.1", "sha256": "f797fc481b490edb305122c9181830a3a5b76d84ef6d1aef2fb9b47ab956f9e4", "urls": ["https://files.pythonhosted.org/packages/ff/db/0c52c4cf5e4bd9f5d7135ec7669a3a767af21b3a308e1ed3674881e52b62/jaraco.context-6.0.1-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jaraco_context_sdist_9bae4ea5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jaraco_context-6.0.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-context==6.0.1", "sha256": "9bae4ea555cf0b14938dc0aee7c9f32ed303aa20a3b73e7dc80111628792d1b3", "urls": ["https://files.pythonhosted.org/packages/df/ad/f3777b81bf0b6e7bc7514a1656d3e637b2e8e15fab2ce3235730b3e7a4e6/jaraco_context-6.0.1.tar.gz"]}}, "rules_python_publish_deps_311_jaraco_functools_py3_none_any_ad159f13": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "jaraco.functools-4.1.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-functools==4.1.0", "sha256": "ad159f13428bc4acbf5541ad6dec511f91573b90fba04df61dafa2a1231cf649", "urls": ["https://files.pythonhosted.org/packages/9f/4f/24b319316142c44283d7540e76c7b5a6dbd5db623abd86bb7b3491c21018/jaraco.functools-4.1.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jaraco_functools_sdist_70f7e0e2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jaraco_functools-4.1.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-functools==4.1.0", "sha256": "70f7e0e2ae076498e212562325e805204fc092d7b4c17e0e86c959e249701a9d", "urls": ["https://files.pythonhosted.org/packages/ab/23/9894b3df5d0a6eb44611c36aec777823fc2e07740dabbd0b810e19594013/jaraco_functools-4.1.0.tar.gz"]}}, "rules_python_publish_deps_311_jeepney_py3_none_any_c0a454ad": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "jeepney-0.8.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jeepney==0.8.0", "sha256": "c0a454ad016ca575060802ee4d590dd912e35c122fa04e70306de3d076cce755", "urls": ["https://files.pythonhosted.org/packages/ae/72/2a1e2290f1ab1e06f71f3d0f1646c9e4634e70e1d37491535e19266e8dc9/jeepney-0.8.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jeepney_sdist_5efe48d2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jeepney-0.8.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jeepney==0.8.0", "sha256": "5efe48d255973902f6badc3ce55e2aa6c5c3b3bc642059ef3a91247bcfcc5806", "urls": ["https://files.pythonhosted.org/packages/d6/f4/154cf374c2daf2020e05c3c6a03c91348d59b23c5366e968feb198306fdf/jeepney-0.8.0.tar.gz"]}}, "rules_python_publish_deps_311_keyring_py3_none_any_5426f817": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "keyring-25.4.1-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "keyring==25.4.1", "sha256": "5426f817cf7f6f007ba5ec722b1bcad95a75b27d780343772ad76b17cb47b0bf", "urls": ["https://files.pythonhosted.org/packages/83/25/e6d59e5f0a0508d0dca8bb98c7f7fd3772fc943ac3f53d5ab18a218d32c0/keyring-25.4.1-py3-none-any.whl"]}}, "rules_python_publish_deps_311_keyring_sdist_b07ebc55": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "keyring-25.4.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "keyring==25.4.1", "sha256": "b07ebc55f3e8ed86ac81dd31ef14e81ace9dd9c3d4b5d77a6e9a2016d0d71a1b", "urls": ["https://files.pythonhosted.org/packages/a5/1c/2bdbcfd5d59dc6274ffb175bc29aa07ecbfab196830e0cfbde7bd861a2ea/keyring-25.4.1.tar.gz"]}}, "rules_python_publish_deps_311_markdown_it_py_py3_none_any_35521684": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "markdown_it_py-3.0.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "markdown-it-py==3.0.0", "sha256": "355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1", "urls": ["https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_markdown_it_py_sdist_e3f60a94": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "markdown-it-py-3.0.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "markdown-it-py==3.0.0", "sha256": "e3f60a94fa066dc52ec76661e37c851cb232d92f9886b15cb560aaada2df8feb", "urls": ["https://files.pythonhosted.org/packages/38/71/3b932df36c1a044d397a1f92d1cf91ee0a503d91e470cbd670aa66b07ed0/markdown-it-py-3.0.0.tar.gz"]}}, "rules_python_publish_deps_311_mdurl_py3_none_any_84008a41": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "mdurl-0.1.2-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "mdurl==0.1.2", "sha256": "84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8", "urls": ["https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl"]}}, "rules_python_publish_deps_311_mdurl_sdist_bb413d29": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "mdurl-0.1.2.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "mdurl==0.1.2", "sha256": "bb413d29f5eea38f31dd4754dd7377d4465116fb207585f97bf925588687c1ba", "urls": ["https://files.pythonhosted.org/packages/d6/54/cfe61301667036ec958cb99bd3efefba235e65cdeb9c84d24a8293ba1d90/mdurl-0.1.2.tar.gz"]}}, "rules_python_publish_deps_311_more_itertools_py3_none_any_037b0d32": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "more_itertools-10.5.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "more-itertools==10.5.0", "sha256": "037b0d3203ce90cca8ab1defbbdac29d5f993fc20131f3664dc8d6acfa872aef", "urls": ["https://files.pythonhosted.org/packages/48/7e/3a64597054a70f7c86eb0a7d4fc315b8c1ab932f64883a297bdffeb5f967/more_itertools-10.5.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_more_itertools_sdist_5482bfef": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "more-itertools-10.5.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "more-itertools==10.5.0", "sha256": "5482bfef7849c25dc3c6dd53a6173ae4795da2a41a80faea6700d9f5846c5da6", "urls": ["https://files.pythonhosted.org/packages/51/78/65922308c4248e0eb08ebcbe67c95d48615cc6f27854b6f2e57143e9178f/more-itertools-10.5.0.tar.gz"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_14c5a72e": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "14c5a72e9fe82aea5fe3072116ad4661af5cf8e8ff8fc5ad3450f123e4925e86", "urls": ["https://files.pythonhosted.org/packages/b3/89/1daff5d9ba5a95a157c092c7c5f39b8dd2b1ddb4559966f808d31cfb67e0/nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_7b7c2a3c": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "7b7c2a3c9eb1a827d42539aa64091640bd275b81e097cd1d8d82ef91ffa2e811", "urls": ["https://files.pythonhosted.org/packages/2c/b6/42fc3c69cabf86b6b81e4c051a9b6e249c5ba9f8155590222c2622961f58/nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_aarch64_42c64511": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "42c64511469005058cd17cc1537578eac40ae9f7200bedcfd1fc1a05f4f8c200", "urls": ["https://files.pythonhosted.org/packages/45/b9/833f385403abaf0023c6547389ec7a7acf141ddd9d1f21573723a6eab39a/nh3-0.2.18-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_armv7l_0411beb0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "0411beb0589eacb6734f28d5497ca2ed379eafab8ad8c84b31bb5c34072b7164", "urls": ["https://files.pythonhosted.org/packages/05/2b/85977d9e11713b5747595ee61f381bc820749daf83f07b90b6c9964cf932/nh3-0.2.18-cp37-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64_5f36b271": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "5f36b271dae35c465ef5e9090e1fdaba4a60a56f0bb0ba03e0932a66f28b9189", "urls": ["https://files.pythonhosted.org/packages/72/f2/5c894d5265ab80a97c68ca36f25c8f6f0308abac649aaf152b74e7e854a8/nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64le_34c03fa7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "34c03fa78e328c691f982b7c03d4423bdfd7da69cd707fe572f544cf74ac23ad", "urls": ["https://files.pythonhosted.org/packages/ab/a7/375afcc710dbe2d64cfbd69e31f82f3e423d43737258af01f6a56d844085/nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_s390x_19aaba96": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "19aaba96e0f795bd0a6c56291495ff59364f4300d4a39b29a0abc9cb3774a84b", "urls": ["https://files.pythonhosted.org/packages/c2/a8/3bb02d0c60a03ad3a112b76c46971e9480efa98a8946677b5a59f60130ca/nh3-0.2.18-cp37-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_x86_64_de3ceed6": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "de3ceed6e661954871d6cd78b410213bdcb136f79aafe22aa7182e028b8c7307", "urls": ["https://files.pythonhosted.org/packages/1b/63/6ab90d0e5225ab9780f6c9fb52254fa36b52bb7c188df9201d05b647e5e1/nh3-0.2.18-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_aarch64_f0eca9ca": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-musllinux_1_2_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "f0eca9ca8628dbb4e916ae2491d72957fdd35f7a5d326b7032a345f111ac07fe", "urls": ["https://files.pythonhosted.org/packages/a3/da/0c4e282bc3cff4a0adf37005fa1fb42257673fbc1bbf7d1ff639ec3d255a/nh3-0.2.18-cp37-abi3-musllinux_1_2_aarch64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_armv7l_3a157ab1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-musllinux_1_2_armv7l.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "3a157ab149e591bb638a55c8c6bcb8cdb559c8b12c13a8affaba6cedfe51713a", "urls": ["https://files.pythonhosted.org/packages/de/81/c291231463d21da5f8bba82c8167a6d6893cc5419b0639801ee5d3aeb8a9/nh3-0.2.18-cp37-abi3-musllinux_1_2_armv7l.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_x86_64_36c95d4b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-musllinux_1_2_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "36c95d4b70530b320b365659bb5034341316e6a9b30f0b25fa9c9eff4c27a204", "urls": ["https://files.pythonhosted.org/packages/eb/61/73a007c74c37895fdf66e0edcd881f5eaa17a348ff02f4bb4bc906d61085/nh3-0.2.18-cp37-abi3-musllinux_1_2_x86_64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_win_amd64_8ce0f819": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-win_amd64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "8ce0f819d2f1933953fca255db2471ad58184a60508f03e6285e5114b6254844", "urls": ["https://files.pythonhosted.org/packages/26/8d/53c5b19c4999bdc6ba95f246f4ef35ca83d7d7423e5e38be43ad66544e5d/nh3-0.2.18-cp37-abi3-win_amd64.whl"]}}, "rules_python_publish_deps_311_nh3_sdist_94a16692": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "nh3-0.2.18.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "94a166927e53972a9698af9542ace4e38b9de50c34352b962f4d9a7d4c927af4", "urls": ["https://files.pythonhosted.org/packages/62/73/10df50b42ddb547a907deeb2f3c9823022580a7a47281e8eae8e003a9639/nh3-0.2.18.tar.gz"]}}, "rules_python_publish_deps_311_pkginfo_py3_none_any_889a6da2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "pkginfo-1.10.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pkginfo==1.10.0", "sha256": "889a6da2ed7ffc58ab5b900d888ddce90bce912f2d2de1dc1c26f4cb9fe65097", "urls": ["https://files.pythonhosted.org/packages/56/09/054aea9b7534a15ad38a363a2bd974c20646ab1582a387a95b8df1bfea1c/pkginfo-1.10.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pkginfo_sdist_5df73835": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pkginfo-1.10.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pkginfo==1.10.0", "sha256": "5df73835398d10db79f8eecd5cd86b1f6d29317589ea70796994d49399af6297", "urls": ["https://files.pythonhosted.org/packages/2f/72/347ec5be4adc85c182ed2823d8d1c7b51e13b9a6b0c1aae59582eca652df/pkginfo-1.10.0.tar.gz"]}}, "rules_python_publish_deps_311_pycparser_py3_none_any_c3702b6d": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "pycparser-2.22-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pycparser==2.22", "sha256": "c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc", "urls": ["https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pycparser_sdist_491c8be9": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pycparser-2.22.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pycparser==2.22", "sha256": "491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6", "urls": ["https://files.pythonhosted.org/packages/1d/b2/31537cf4b1ca988837256c910a668b553fceb8f069bedc4b1c826024b52c/pycparser-2.22.tar.gz"]}}, "rules_python_publish_deps_311_pygments_py3_none_any_b8e6aca0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "pygments-2.18.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pygments==2.18.0", "sha256": "b8e6aca0523f3ab76fee51799c488e38782ac06eafcf95e7ba832985c8e7b13a", "urls": ["https://files.pythonhosted.org/packages/f7/3f/01c8b82017c199075f8f788d0d906b9ffbbc5a47dc9918a945e13d5a2bda/pygments-2.18.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pygments_sdist_786ff802": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pygments-2.18.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pygments==2.18.0", "sha256": "786ff802f32e91311bff3889f6e9a86e81505fe99f2735bb6d60ae0c5004f199", "urls": ["https://files.pythonhosted.org/packages/8e/62/8336eff65bcbc8e4cb5d05b55faf041285951b6e80f33e2bff2024788f31/pygments-2.18.0.tar.gz"]}}, "rules_python_publish_deps_311_pywin32_ctypes_py3_none_any_8a151337": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_windows_x86_64"], "filename": "pywin32_ctypes-0.2.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pywin32-ctypes==0.2.3", "sha256": "8a1513379d709975552d202d942d9837758905c8d01eb82b8bcc30918929e7b8", "urls": ["https://files.pythonhosted.org/packages/de/3d/8161f7711c017e01ac9f008dfddd9410dff3674334c233bde66e7ba65bbf/pywin32_ctypes-0.2.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pywin32_ctypes_sdist_d162dc04": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pywin32-ctypes-0.2.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pywin32-ctypes==0.2.3", "sha256": "d162dc04946d704503b2edc4d55f3dba5c1d539ead017afa00142c38b9885755", "urls": ["https://files.pythonhosted.org/packages/85/9f/01a1a99704853cb63f253eea009390c88e7131c67e66a0a02099a8c917cb/pywin32-ctypes-0.2.3.tar.gz"]}}, "rules_python_publish_deps_311_readme_renderer_py3_none_any_2fbca89b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "readme_renderer-44.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "readme-renderer==44.0", "sha256": "2fbca89b81a08526aadf1357a8c2ae889ec05fb03f5da67f9769c9a592166151", "urls": ["https://files.pythonhosted.org/packages/e1/67/921ec3024056483db83953ae8e48079ad62b92db7880013ca77632921dd0/readme_renderer-44.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_readme_renderer_sdist_8712034e": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "readme_renderer-44.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "readme-renderer==44.0", "sha256": "8712034eabbfa6805cacf1402b4eeb2a73028f72d1166d6f5cb7f9c047c5d1e1", "urls": ["https://files.pythonhosted.org/packages/5a/a9/104ec9234c8448c4379768221ea6df01260cd6c2ce13182d4eac531c8342/readme_renderer-44.0.tar.gz"]}}, "rules_python_publish_deps_311_requests_py3_none_any_70761cfe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "requests-2.32.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests==2.32.3", "sha256": "70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6", "urls": ["https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_requests_sdist_55365417": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "requests-2.32.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests==2.32.3", "sha256": "55365417734eb18255590a9ff9eb97e9e1da868d4ccd6402399eaf68af20a760", "urls": ["https://files.pythonhosted.org/packages/63/70/2bf7780ad2d390a8d301ad0b550f1581eadbd9a20f896afe06353c2a2913/requests-2.32.3.tar.gz"]}}, "rules_python_publish_deps_311_requests_toolbelt_py2_none_any_cccfdd66": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "requests_toolbelt-1.0.0-py2.py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests-toolbelt==1.0.0", "sha256": "cccfdd665f0a24fcf4726e690f65639d272bb0637b9b92dfd91a5568ccf6bd06", "urls": ["https://files.pythonhosted.org/packages/3f/51/d4db610ef29373b879047326cbf6fa98b6c1969d6f6dc423279de2b1be2c/requests_toolbelt-1.0.0-py2.py3-none-any.whl"]}}, "rules_python_publish_deps_311_requests_toolbelt_sdist_7681a0a3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "requests-toolbelt-1.0.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests-toolbelt==1.0.0", "sha256": "7681a0a3d047012b5bdc0ee37d7f8f07ebe76ab08caeccfc3921ce23c88d5bc6", "urls": ["https://files.pythonhosted.org/packages/f3/61/d7545dafb7ac2230c70d38d31cbfe4cc64f7144dc41f6e4e4b78ecd9f5bb/requests-toolbelt-1.0.0.tar.gz"]}}, "rules_python_publish_deps_311_rfc3986_py2_none_any_50b1502b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "rfc3986-2.0.0-py2.py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rfc3986==2.0.0", "sha256": "50b1502b60e289cb37883f3dfd34532b8873c7de9f49bb546641ce9cbd256ebd", "urls": ["https://files.pythonhosted.org/packages/ff/9a/9afaade874b2fa6c752c36f1548f718b5b83af81ed9b76628329dab81c1b/rfc3986-2.0.0-py2.py3-none-any.whl"]}}, "rules_python_publish_deps_311_rfc3986_sdist_97aacf9d": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "rfc3986-2.0.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rfc3986==2.0.0", "sha256": "97aacf9dbd4bfd829baad6e6309fa6573aaf1be3f6fa735c8ab05e46cecb261c", "urls": ["https://files.pythonhosted.org/packages/85/40/1520d68bfa07ab5a6f065a186815fb6610c86fe957bc065754e47f7b0840/rfc3986-2.0.0.tar.gz"]}}, "rules_python_publish_deps_311_rich_py3_none_any_9836f509": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "rich-13.9.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rich==13.9.3", "sha256": "9836f5096eb2172c9e77df411c1b009bace4193d6a481d534fea75ebba758283", "urls": ["https://files.pythonhosted.org/packages/9a/e2/10e9819cf4a20bd8ea2f5dabafc2e6bf4a78d6a0965daeb60a4b34d1c11f/rich-13.9.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_rich_sdist_bc1e01b8": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "rich-13.9.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rich==13.9.3", "sha256": "bc1e01b899537598cf02579d2b9f4a415104d3fc439313a7a2c165d76557a08e", "urls": ["https://files.pythonhosted.org/packages/d9/e9/cf9ef5245d835065e6673781dbd4b8911d352fb770d56cf0879cf11b7ee1/rich-13.9.3.tar.gz"]}}, "rules_python_publish_deps_311_secretstorage_py3_none_any_f356e662": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "SecretStorage-3.3.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "secretstorage==3.3.3", "sha256": "f356e6628222568e3af06f2eba8df495efa13b3b63081dafd4f7d9a7b7bc9f99", "urls": ["https://files.pythonhosted.org/packages/54/24/b4293291fa1dd830f353d2cb163295742fa87f179fcc8a20a306a81978b7/SecretStorage-3.3.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_secretstorage_sdist_2403533e": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "SecretStorage-3.3.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "secretstorage==3.3.3", "sha256": "2403533ef369eca6d2ba81718576c5e0f564d5cca1b58f73a8b23e7d4eeebd77", "urls": ["https://files.pythonhosted.org/packages/53/a4/f48c9d79cb507ed1373477dbceaba7401fd8a23af63b837fa61f1dcd3691/SecretStorage-3.3.3.tar.gz"]}}, "rules_python_publish_deps_311_twine_py3_none_any_215dbe7b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "twine-5.1.1-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "twine==5.1.1", "sha256": "215dbe7b4b94c2c50a7315c0275d2258399280fbb7d04182c7e55e24b5f93997", "urls": ["https://files.pythonhosted.org/packages/5d/ec/00f9d5fd040ae29867355e559a94e9a8429225a0284a3f5f091a3878bfc0/twine-5.1.1-py3-none-any.whl"]}}, "rules_python_publish_deps_311_twine_sdist_9aa08251": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "twine-5.1.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "twine==5.1.1", "sha256": "9aa0825139c02b3434d913545c7b847a21c835e11597f5255842d457da2322db", "urls": ["https://files.pythonhosted.org/packages/77/68/bd982e5e949ef8334e6f7dcf76ae40922a8750aa2e347291ae1477a4782b/twine-5.1.1.tar.gz"]}}, "rules_python_publish_deps_311_urllib3_py3_none_any_ca899ca0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "urllib3-2.2.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "urllib3==2.2.3", "sha256": "ca899ca043dcb1bafa3e262d73aa25c465bfb49e0bd9dd5d59f1d0acba2f8fac", "urls": ["https://files.pythonhosted.org/packages/ce/d9/5f4c13cecde62396b0d3fe530a50ccea91e7dfc1ccf0e09c228841bb5ba8/urllib3-2.2.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_urllib3_sdist_e7d814a8": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "urllib3-2.2.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "urllib3==2.2.3", "sha256": "e7d814a81dad81e6caf2ec9fdedb284ecc9c73076b62654547cc64ccdcae26e9", "urls": ["https://files.pythonhosted.org/packages/ed/63/22ba4ebfe7430b76388e7cd448d5478814d3032121827c12a2cc287e2260/urllib3-2.2.3.tar.gz"]}}, "rules_python_publish_deps_311_zipp_py3_none_any_a817ac80": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "zipp-3.20.2-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "zipp==3.20.2", "sha256": "a817ac80d6cf4b23bf7f2828b7cabf326f15a001bea8b1f9b49631780ba28350", "urls": ["https://files.pythonhosted.org/packages/62/8b/5ba542fa83c90e09eac972fc9baca7a88e7e7ca4b221a89251954019308b/zipp-3.20.2-py3-none-any.whl"]}}, "rules_python_publish_deps_311_zipp_sdist_bc9eb26f": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "zipp-3.20.2.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "zipp==3.20.2", "sha256": "bc9eb26f4506fda01b81bcde0ca78103b6e62f991b381fec825435c836edbc29", "urls": ["https://files.pythonhosted.org/packages/54/bf/5c0000c44ebc80123ecbdddba1f5dcd94a5ada602a9c225d84b5aaa55e86/zipp-3.20.2.tar.gz"]}}, "grpc_python_dependencies": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "grpc_python_dependencies", "extra_hub_aliases": {}, "whl_map": {"absl_py": "{\"grpc_python_dependencies_310_absl_py\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_absl_py\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_absl_py\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_absl_py\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_absl_py\":[{\"version\":\"3.9\"}]}", "cachetools": "{\"grpc_python_dependencies_310_cachetools\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_cachetools\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_cachetools\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_cachetools\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_cachetools\":[{\"version\":\"3.9\"}]}", "certifi": "{\"grpc_python_dependencies_310_certifi\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_certifi\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_certifi\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_certifi\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_certifi\":[{\"version\":\"3.9\"}]}", "chardet": "{\"grpc_python_dependencies_310_chardet\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_chardet\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_chardet\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_chardet\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_chardet\":[{\"version\":\"3.9\"}]}", "charset_normalizer": "{\"grpc_python_dependencies_310_charset_normalizer\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_charset_normalizer\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_charset_normalizer\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_charset_normalizer\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_charset_normalizer\":[{\"version\":\"3.9\"}]}", "coverage": "{\"grpc_python_dependencies_310_coverage\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_coverage\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_coverage\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_coverage\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_coverage\":[{\"version\":\"3.9\"}]}", "cython": "{\"grpc_python_dependencies_310_cython\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_cython\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_cython\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_cython\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_cython\":[{\"version\":\"3.9\"}]}", "deprecated": "{\"grpc_python_dependencies_310_deprecated\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_deprecated\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_deprecated\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_deprecated\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_deprecated\":[{\"version\":\"3.9\"}]}", "gevent": "{\"grpc_python_dependencies_310_gevent\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_gevent\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_gevent\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_gevent\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_gevent\":[{\"version\":\"3.9\"}]}", "google_api_core": "{\"grpc_python_dependencies_310_google_api_core\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_api_core\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_api_core\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_google_api_core\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_google_api_core\":[{\"version\":\"3.9\"}]}", "google_auth": "{\"grpc_python_dependencies_310_google_auth\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_auth\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_auth\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_google_auth\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_google_auth\":[{\"version\":\"3.9\"}]}", "google_cloud_monitoring": "{\"grpc_python_dependencies_310_google_cloud_monitoring\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_cloud_monitoring\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_cloud_monitoring\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_google_cloud_monitoring\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_google_cloud_monitoring\":[{\"version\":\"3.9\"}]}", "google_cloud_trace": "{\"grpc_python_dependencies_310_google_cloud_trace\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_cloud_trace\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_cloud_trace\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_google_cloud_trace\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_google_cloud_trace\":[{\"version\":\"3.9\"}]}", "googleapis_common_protos": "{\"grpc_python_dependencies_310_googleapis_common_protos\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_googleapis_common_protos\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_googleapis_common_protos\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_googleapis_common_protos\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_googleapis_common_protos\":[{\"version\":\"3.9\"}]}", "greenlet": "{\"grpc_python_dependencies_310_greenlet\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_greenlet\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_greenlet\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_greenlet\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_greenlet\":[{\"version\":\"3.9\"}]}", "idna": "{\"grpc_python_dependencies_310_idna\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_idna\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_idna\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_idna\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_idna\":[{\"version\":\"3.9\"}]}", "importlib_metadata": "{\"grpc_python_dependencies_310_importlib_metadata\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_importlib_metadata\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_importlib_metadata\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_importlib_metadata\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_importlib_metadata\":[{\"version\":\"3.9\"}]}", "oauth2client": "{\"grpc_python_dependencies_310_oauth2client\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_oauth2client\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_oauth2client\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_oauth2client\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_oauth2client\":[{\"version\":\"3.9\"}]}", "opencensus_context": "{\"grpc_python_dependencies_310_opencensus_context\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opencensus_context\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opencensus_context\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_opencensus_context\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_opencensus_context\":[{\"version\":\"3.9\"}]}", "opentelemetry_api": "{\"grpc_python_dependencies_310_opentelemetry_api\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_api\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_api\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_opentelemetry_api\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_opentelemetry_api\":[{\"version\":\"3.9\"}]}", "opentelemetry_exporter_prometheus": "{\"grpc_python_dependencies_310_opentelemetry_exporter_prometheus\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_exporter_prometheus\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_exporter_prometheus\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_opentelemetry_exporter_prometheus\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_opentelemetry_exporter_prometheus\":[{\"version\":\"3.9\"}]}", "opentelemetry_resourcedetector_gcp": "{\"grpc_python_dependencies_310_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.9\"}]}", "opentelemetry_sdk": "{\"grpc_python_dependencies_310_opentelemetry_sdk\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_sdk\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_sdk\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_opentelemetry_sdk\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_opentelemetry_sdk\":[{\"version\":\"3.9\"}]}", "opentelemetry_semantic_conventions": "{\"grpc_python_dependencies_310_opentelemetry_semantic_conventions\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_semantic_conventions\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_semantic_conventions\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_opentelemetry_semantic_conventions\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_opentelemetry_semantic_conventions\":[{\"version\":\"3.9\"}]}", "prometheus_client": "{\"grpc_python_dependencies_310_prometheus_client\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_prometheus_client\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_prometheus_client\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_prometheus_client\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_prometheus_client\":[{\"version\":\"3.9\"}]}", "proto_plus": "{\"grpc_python_dependencies_310_proto_plus\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_proto_plus\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_proto_plus\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_proto_plus\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_proto_plus\":[{\"version\":\"3.9\"}]}", "protobuf": "{\"grpc_python_dependencies_310_protobuf\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_protobuf\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_protobuf\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_protobuf\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_protobuf\":[{\"version\":\"3.9\"}]}", "pyasn1": "{\"grpc_python_dependencies_310_pyasn1\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_pyasn1\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_pyasn1\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_pyasn1\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_pyasn1\":[{\"version\":\"3.9\"}]}", "pyasn1_modules": "{\"grpc_python_dependencies_310_pyasn1_modules\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_pyasn1_modules\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_pyasn1_modules\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_pyasn1_modules\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_pyasn1_modules\":[{\"version\":\"3.9\"}]}", "requests": "{\"grpc_python_dependencies_310_requests\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_requests\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_requests\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_requests\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_requests\":[{\"version\":\"3.9\"}]}", "rsa": "{\"grpc_python_dependencies_310_rsa\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_rsa\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_rsa\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_rsa\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_rsa\":[{\"version\":\"3.9\"}]}", "setuptools": "{\"grpc_python_dependencies_310_setuptools\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_setuptools\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_setuptools\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_setuptools\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_setuptools\":[{\"version\":\"3.9\"}]}", "typing_extensions": "{\"grpc_python_dependencies_310_typing_extensions\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_typing_extensions\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_typing_extensions\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_typing_extensions\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_typing_extensions\":[{\"version\":\"3.9\"}]}", "urllib3": "{\"grpc_python_dependencies_310_urllib3\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_urllib3\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_urllib3\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_urllib3\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_urllib3\":[{\"version\":\"3.9\"}]}", "wheel": "{\"grpc_python_dependencies_310_wheel\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_wheel\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_wheel\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_wheel\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_wheel\":[{\"version\":\"3.9\"}]}", "wrapt": "{\"grpc_python_dependencies_310_wrapt\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_wrapt\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_wrapt\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_wrapt\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_wrapt\":[{\"version\":\"3.9\"}]}", "zipp": "{\"grpc_python_dependencies_310_zipp\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_zipp\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_zipp\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_zipp\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_zipp\":[{\"version\":\"3.9\"}]}", "zope_event": "{\"grpc_python_dependencies_310_zope_event\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_zope_event\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_zope_event\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_zope_event\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_zope_event\":[{\"version\":\"3.9\"}]}", "zope_interface": "{\"grpc_python_dependencies_310_zope_interface\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_zope_interface\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_zope_interface\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_38_zope_interface\":[{\"version\":\"3.8\"}],\"grpc_python_dependencies_39_zope_interface\":[{\"version\":\"3.9\"}]}"}, "packages": ["absl_py", "cachetools", "certifi", "chardet", "charset_normalizer", "coverage", "cython", "deprecated", "gevent", "google_api_core", "google_auth", "google_cloud_monitoring", "google_cloud_trace", "googleapis_common_protos", "greenlet", "idna", "importlib_metadata", "oauth2client", "opencensus_context", "opentelemetry_api", "opentelemetry_exporter_prometheus", "opentelemetry_resourcedetector_gcp", "opentelemetry_sdk", "opentelemetry_semantic_conventions", "prometheus_client", "proto_plus", "protobuf", "pyasn1", "pyasn1_modules", "requests", "rsa", "setuptools", "typing_extensions", "urllib3", "wheel", "wrapt", "zipp", "zope_event", "zope_interface"], "groups": {}}}, "pip": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "pip", "extra_hub_aliases": {}, "whl_map": {"aiohttp": "{\"pip_311_aiohttp\":[{\"version\":\"3.11\"}]}", "aiosignal": "{\"pip_311_aiosignal\":[{\"version\":\"3.11\"}]}", "annotated_types": "{\"pip_311_annotated_types\":[{\"version\":\"3.11\"}]}", "anthropic": "{\"pip_311_anthropic\":[{\"version\":\"3.11\"}]}", "anyio": "{\"pip_311_anyio\":[{\"version\":\"3.11\"}]}", "async_timeout": "{\"pip_311_async_timeout\":[{\"version\":\"3.11\"}]}", "attrs": "{\"pip_311_attrs\":[{\"version\":\"3.11\"}]}", "certifi": "{\"pip_311_certifi\":[{\"version\":\"3.11\"}]}", "charset_normalizer": "{\"pip_311_charset_normalizer\":[{\"version\":\"3.11\"}]}", "click": "{\"pip_311_click\":[{\"version\":\"3.11\"}]}", "dataclasses_json": "{\"pip_311_dataclasses_json\":[{\"version\":\"3.11\"}]}", "distro": "{\"pip_311_distro\":[{\"version\":\"3.11\"}]}", "dnspython": "{\"pip_311_dnspython\":[{\"version\":\"3.11\"}]}", "elastic_transport": "{\"pip_311_elastic_transport\":[{\"version\":\"3.11\"}]}", "elasticsearch": "{\"pip_311_elasticsearch\":[{\"version\":\"3.11\"}]}", "exceptiongroup": "{\"pip_311_exceptiongroup\":[{\"version\":\"3.11\"}]}", "frozenlist": "{\"pip_311_frozenlist\":[{\"version\":\"3.11\"}]}", "gitdb": "{\"pip_311_gitdb\":[{\"version\":\"3.11\"}]}", "gitpython": "{\"pip_311_gitpython\":[{\"version\":\"3.11\"}]}", "greenlet": "{\"pip_311_greenlet\":[{\"version\":\"3.11\"}]}", "h11": "{\"pip_311_h11\":[{\"version\":\"3.11\"}]}", "httpcore": "{\"pip_311_httpcore\":[{\"version\":\"3.11\"}]}", "httpx": "{\"pip_311_httpx\":[{\"version\":\"3.11\"}]}", "httpx_sse": "{\"pip_311_httpx_sse\":[{\"version\":\"3.11\"}]}", "idna": "{\"pip_311_idna\":[{\"version\":\"3.11\"}]}", "jiter": "{\"pip_311_jiter\":[{\"version\":\"3.11\"}]}", "jsonpatch": "{\"pip_311_jsonpatch\":[{\"version\":\"3.11\"}]}", "jsonpointer": "{\"pip_311_jsonpointer\":[{\"version\":\"3.11\"}]}", "langchain": "{\"pip_311_langchain\":[{\"version\":\"3.11\"}]}", "langchain_anthropic": "{\"pip_311_langchain_anthropic\":[{\"version\":\"3.11\"}]}", "langchain_community": "{\"pip_311_langchain_community\":[{\"version\":\"3.11\"}]}", "langchain_core": "{\"pip_311_langchain_core\":[{\"version\":\"3.11\"}]}", "langchain_mcp_adapters": "{\"pip_311_langchain_mcp_adapters\":[{\"version\":\"3.11\"}]}", "langchain_openai": "{\"pip_311_langchain_openai\":[{\"version\":\"3.11\"}]}", "langchain_text_splitters": "{\"pip_311_langchain_text_splitters\":[{\"version\":\"3.11\"}]}", "langgraph": "{\"pip_311_langgraph\":[{\"version\":\"3.11\"}]}", "langgraph_checkpoint": "{\"pip_311_langgraph_checkpoint\":[{\"version\":\"3.11\"}]}", "langgraph_prebuilt": "{\"pip_311_langgraph_prebuilt\":[{\"version\":\"3.11\"}]}", "langgraph_sdk": "{\"pip_311_langgraph_sdk\":[{\"version\":\"3.11\"}]}", "langsmith": "{\"pip_311_langsmith\":[{\"version\":\"3.11\"}]}", "loguru": "{\"pip_311_loguru\":[{\"version\":\"3.11\"}]}", "marshmallow": "{\"pip_311_marshmallow\":[{\"version\":\"3.11\"}]}", "mcp": "{\"pip_311_mcp\":[{\"version\":\"3.11\"}]}", "mmh3": "{\"pip_311_mmh3\":[{\"version\":\"3.11\"}]}", "multidict": "{\"pip_311_multidict\":[{\"version\":\"3.11\"}]}", "mypy_extensions": "{\"pip_311_mypy_extensions\":[{\"version\":\"3.11\"}]}", "numexpr": "{\"pip_311_numexpr\":[{\"version\":\"3.11\"}]}", "numpy": "{\"pip_311_numpy\":[{\"version\":\"3.11\"}]}", "openai": "{\"pip_311_openai\":[{\"version\":\"3.11\"}]}", "orjson": "{\"pip_311_orjson\":[{\"version\":\"3.11\"}]}", "ormsgpack": "{\"pip_311_ormsgpack\":[{\"version\":\"3.11\"}]}", "packaging": "{\"pip_311_packaging\":[{\"version\":\"3.11\"}]}", "pydantic": "{\"pip_311_pydantic\":[{\"version\":\"3.11\"}]}", "pydantic_core": "{\"pip_311_pydantic_core\":[{\"version\":\"3.11\"}]}", "pydantic_settings": "{\"pip_311_pydantic_settings\":[{\"version\":\"3.11\"}]}", "python_dateutil": "{\"pip_311_python_dateutil\":[{\"version\":\"3.11\"}]}", "python_dotenv": "{\"pip_311_python_dotenv\":[{\"version\":\"3.11\"}]}", "python_multipart": "{\"pip_311_python_multipart\":[{\"version\":\"3.11\"}]}", "python_telegram_bot": "{\"pip_311_python_telegram_bot\":[{\"version\":\"3.11\"}]}", "pyyaml": "{\"pip_311_pyyaml\":[{\"version\":\"3.11\"}]}", "redis": "{\"pip_311_redis\":[{\"version\":\"3.11\"}]}", "regex": "{\"pip_311_regex\":[{\"version\":\"3.11\"}]}", "requests": "{\"pip_311_requests\":[{\"version\":\"3.11\"}]}", "requests_toolbelt": "{\"pip_311_requests_toolbelt\":[{\"version\":\"3.11\"}]}", "scipy": "{\"pip_311_scipy\":[{\"version\":\"3.11\"}]}", "six": "{\"pip_311_six\":[{\"version\":\"3.11\"}]}", "smmap": "{\"pip_311_smmap\":[{\"version\":\"3.11\"}]}", "sniffio": "{\"pip_311_sniffio\":[{\"version\":\"3.11\"}]}", "sqlalchemy": "{\"pip_311_sqlalchemy\":[{\"version\":\"3.11\"}]}", "sse_starlette": "{\"pip_311_sse_starlette\":[{\"version\":\"3.11\"}]}", "starlette": "{\"pip_311_starlette\":[{\"version\":\"3.11\"}]}", "tenacity": "{\"pip_311_tenacity\":[{\"version\":\"3.11\"}]}", "tiktoken": "{\"pip_311_tiktoken\":[{\"version\":\"3.11\"}]}", "tqdm": "{\"pip_311_tqdm\":[{\"version\":\"3.11\"}]}", "typing_extensions": "{\"pip_311_typing_extensions\":[{\"version\":\"3.11\"}]}", "typing_inspect": "{\"pip_311_typing_inspect\":[{\"version\":\"3.11\"}]}", "typing_inspection": "{\"pip_311_typing_inspection\":[{\"version\":\"3.11\"}]}", "urllib3": "{\"pip_311_urllib3\":[{\"version\":\"3.11\"}]}", "uvicorn": "{\"pip_311_uvicorn\":[{\"version\":\"3.11\"}]}", "xxhash": "{\"pip_311_xxhash\":[{\"version\":\"3.11\"}]}", "yarl": "{\"pip_311_yarl\":[{\"version\":\"3.11\"}]}"}, "packages": ["aiohttp", "aiosignal", "annotated_types", "anthropic", "anyio", "async_timeout", "attrs", "certifi", "charset_normalizer", "click", "dataclasses_json", "distro", "dnspython", "elastic_transport", "elasticsearch", "exceptiongroup", "frozenlist", "gitdb", "git<PERSON><PERSON>on", "greenlet", "h11", "httpcore", "httpx", "httpx_sse", "idna", "jiter", "jsonpatch", "<PERSON><PERSON><PERSON>er", "langchain", "langchain_anthropic", "langchain_community", "langchain_core", "langchain_mcp_adapters", "langchain_openai", "langchain_text_splitters", "langgraph", "langgraph_checkpoint", "langgraph_prebuilt", "langgraph_sdk", "langsmith", "loguru", "marshmallow", "mcp", "mmh3", "multidict", "mypy_extensions", "numexpr", "numpy", "openai", "<PERSON><PERSON><PERSON>", "ormsgpack", "packaging", "pydantic", "pydantic_core", "pydantic_settings", "python_dateutil", "python_dotenv", "python_multipart", "python_telegram_bot", "pyyaml", "redis", "regex", "requests", "requests_toolbelt", "scipy", "six", "smmap", "sniffio", "sqlalchemy", "sse_starlette", "starlette", "tenacity", "tiktoken", "tqdm", "typing_extensions", "typing_inspect", "typing_inspection", "urllib3", "u<PERSON><PERSON>", "xxhash", "yarl"], "groups": {}}}, "pip_deps": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "pip_deps", "extra_hub_aliases": {}, "whl_map": {"numpy": "{\"pip_deps_310_numpy\":[{\"version\":\"3.10\"}],\"pip_deps_311_numpy\":[{\"version\":\"3.11\"}],\"pip_deps_312_numpy\":[{\"version\":\"3.12\"}],\"pip_deps_38_numpy\":[{\"version\":\"3.8\"}],\"pip_deps_39_numpy\":[{\"version\":\"3.9\"}]}", "setuptools": "{\"pip_deps_310_setuptools\":[{\"version\":\"3.10\"}],\"pip_deps_311_setuptools\":[{\"version\":\"3.11\"}],\"pip_deps_312_setuptools\":[{\"version\":\"3.12\"}],\"pip_deps_38_setuptools\":[{\"version\":\"3.8\"}],\"pip_deps_39_setuptools\":[{\"version\":\"3.9\"}]}"}, "packages": ["numpy", "setuptools"], "groups": {}}}, "rules_fuzzing_py_deps": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "rules_fuzzing_py_deps", "extra_hub_aliases": {}, "whl_map": {"absl_py": "{\"rules_fuzzing_py_deps_310_absl_py\":[{\"version\":\"3.10\"}],\"rules_fuzzing_py_deps_311_absl_py\":[{\"version\":\"3.11\"}],\"rules_fuzzing_py_deps_312_absl_py\":[{\"version\":\"3.12\"}],\"rules_fuzzing_py_deps_38_absl_py\":[{\"version\":\"3.8\"}],\"rules_fuzzing_py_deps_39_absl_py\":[{\"version\":\"3.9\"}]}", "six": "{\"rules_fuzzing_py_deps_310_six\":[{\"version\":\"3.10\"}],\"rules_fuzzing_py_deps_311_six\":[{\"version\":\"3.11\"}],\"rules_fuzzing_py_deps_312_six\":[{\"version\":\"3.12\"}],\"rules_fuzzing_py_deps_38_six\":[{\"version\":\"3.8\"}],\"rules_fuzzing_py_deps_39_six\":[{\"version\":\"3.9\"}]}"}, "packages": ["absl_py", "six"], "groups": {}}}, "rules_proto_grpc_python_pip_deps": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "rules_proto_grpc_python_pip_deps", "extra_hub_aliases": {}, "whl_map": {"grpcio": "{\"rules_proto_grpc_python_pip_deps_310_grpcio\":[{\"version\":\"3.10\"}],\"rules_proto_grpc_python_pip_deps_311_grpcio\":[{\"version\":\"3.11\"}],\"rules_proto_grpc_python_pip_deps_312_grpcio\":[{\"version\":\"3.12\"}],\"rules_proto_grpc_python_pip_deps_38_grpcio\":[{\"version\":\"3.8\"}],\"rules_proto_grpc_python_pip_deps_39_grpcio\":[{\"version\":\"3.9\"}]}", "grpclib": "{\"rules_proto_grpc_python_pip_deps_310_grpclib\":[{\"version\":\"3.10\"}],\"rules_proto_grpc_python_pip_deps_311_grpclib\":[{\"version\":\"3.11\"}],\"rules_proto_grpc_python_pip_deps_312_grpclib\":[{\"version\":\"3.12\"}],\"rules_proto_grpc_python_pip_deps_38_grpclib\":[{\"version\":\"3.8\"}],\"rules_proto_grpc_python_pip_deps_39_grpclib\":[{\"version\":\"3.9\"}]}", "h2": "{\"rules_proto_grpc_python_pip_deps_310_h2\":[{\"version\":\"3.10\"}],\"rules_proto_grpc_python_pip_deps_311_h2\":[{\"version\":\"3.11\"}],\"rules_proto_grpc_python_pip_deps_312_h2\":[{\"version\":\"3.12\"}],\"rules_proto_grpc_python_pip_deps_38_h2\":[{\"version\":\"3.8\"}],\"rules_proto_grpc_python_pip_deps_39_h2\":[{\"version\":\"3.9\"}]}", "hpack": "{\"rules_proto_grpc_python_pip_deps_310_hpack\":[{\"version\":\"3.10\"}],\"rules_proto_grpc_python_pip_deps_311_hpack\":[{\"version\":\"3.11\"}],\"rules_proto_grpc_python_pip_deps_312_hpack\":[{\"version\":\"3.12\"}],\"rules_proto_grpc_python_pip_deps_38_hpack\":[{\"version\":\"3.8\"}],\"rules_proto_grpc_python_pip_deps_39_hpack\":[{\"version\":\"3.9\"}]}", "hyperframe": "{\"rules_proto_grpc_python_pip_deps_310_hyperframe\":[{\"version\":\"3.10\"}],\"rules_proto_grpc_python_pip_deps_311_hyperframe\":[{\"version\":\"3.11\"}],\"rules_proto_grpc_python_pip_deps_312_hyperframe\":[{\"version\":\"3.12\"}],\"rules_proto_grpc_python_pip_deps_38_hyperframe\":[{\"version\":\"3.8\"}],\"rules_proto_grpc_python_pip_deps_39_hyperframe\":[{\"version\":\"3.9\"}]}", "multidict": "{\"rules_proto_grpc_python_pip_deps_310_multidict\":[{\"version\":\"3.10\"}],\"rules_proto_grpc_python_pip_deps_311_multidict\":[{\"version\":\"3.11\"}],\"rules_proto_grpc_python_pip_deps_312_multidict\":[{\"version\":\"3.12\"}],\"rules_proto_grpc_python_pip_deps_38_multidict\":[{\"version\":\"3.8\"}],\"rules_proto_grpc_python_pip_deps_39_multidict\":[{\"version\":\"3.9\"}]}", "protobuf": "{\"rules_proto_grpc_python_pip_deps_310_protobuf\":[{\"version\":\"3.10\"}],\"rules_proto_grpc_python_pip_deps_311_protobuf\":[{\"version\":\"3.11\"}],\"rules_proto_grpc_python_pip_deps_312_protobuf\":[{\"version\":\"3.12\"}],\"rules_proto_grpc_python_pip_deps_38_protobuf\":[{\"version\":\"3.8\"}],\"rules_proto_grpc_python_pip_deps_39_protobuf\":[{\"version\":\"3.9\"}]}"}, "packages": ["grpcio", "grpclib", "h2", "hpack", "hyperframe", "multidict", "protobuf"], "groups": {}}}, "rules_python_publish_deps": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "rules_python_publish_deps", "extra_hub_aliases": {}, "whl_map": {"backports_tarfile": "{\"rules_python_publish_deps_311_backports_tarfile_py3_none_any_77e284d7\":[{\"filename\":\"backports.tarfile-1.2.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_backports_tarfile_sdist_d75e02c2\":[{\"filename\":\"backports_tarfile-1.2.0.tar.gz\",\"version\":\"3.11\"}]}", "certifi": "{\"rules_python_publish_deps_311_certifi_py3_none_any_922820b5\":[{\"filename\":\"certifi-2024.8.30-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_certifi_sdist_bec941d2\":[{\"filename\":\"certifi-2024.8.30.tar.gz\",\"version\":\"3.11\"}]}", "cffi": "{\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_aarch64_a1ed2dd2\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_ppc64le_46bf4316\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_s390x_a24ed04c\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_x86_64_610faea7\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_aarch64_a9b15d49\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_x86_64_fc48c783\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_sdist_1c39c601\":[{\"filename\":\"cffi-1.17.1.tar.gz\",\"version\":\"3.11\"}]}", "charset_normalizer": "{\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_universal2_0d99dd8f\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_universal2.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_x86_64_c57516e5\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_11_0_arm64_6dba5d19\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-macosx_11_0_arm64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_aarch64_bf4475b8\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_ppc64le_ce031db0\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_s390x_8ff4e7cd\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_x86_64_3710a975\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_aarch64_47334db7\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_ppc64le_f1a2f519\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_s390x_63bc5c4a\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_x86_64_bcb4f8ea\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_win_amd64_cee4373f\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-win_amd64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_py3_none_any_fe9f97fe\":[{\"filename\":\"charset_normalizer-3.4.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_sdist_223217c3\":[{\"filename\":\"charset_normalizer-3.4.0.tar.gz\",\"version\":\"3.11\"}]}", "cryptography": "{\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_aarch64_846da004\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_x86_64_0f996e72\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_aarch64_f7b178f1\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_28_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_x86_64_c2e6fc39\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_28_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_aarch64_e1be4655\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-musllinux_1_2_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_x86_64_df6b6c6d\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-musllinux_1_2_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_sdist_315b9001\":[{\"filename\":\"cryptography-43.0.3.tar.gz\",\"version\":\"3.11\"}]}", "docutils": "{\"rules_python_publish_deps_311_docutils_py3_none_any_dafca5b9\":[{\"filename\":\"docutils-0.21.2-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_docutils_sdist_3a6b1873\":[{\"filename\":\"docutils-0.21.2.tar.gz\",\"version\":\"3.11\"}]}", "idna": "{\"rules_python_publish_deps_311_idna_py3_none_any_946d195a\":[{\"filename\":\"idna-3.10-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_idna_sdist_12f65c9b\":[{\"filename\":\"idna-3.10.tar.gz\",\"version\":\"3.11\"}]}", "importlib_metadata": "{\"rules_python_publish_deps_311_importlib_metadata_py3_none_any_45e54197\":[{\"filename\":\"importlib_metadata-8.5.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_importlib_metadata_sdist_71522656\":[{\"filename\":\"importlib_metadata-8.5.0.tar.gz\",\"version\":\"3.11\"}]}", "jaraco_classes": "{\"rules_python_publish_deps_311_jaraco_classes_py3_none_any_f662826b\":[{\"filename\":\"jaraco.classes-3.4.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jaraco_classes_sdist_47a024b5\":[{\"filename\":\"jaraco.classes-3.4.0.tar.gz\",\"version\":\"3.11\"}]}", "jaraco_context": "{\"rules_python_publish_deps_311_jaraco_context_py3_none_any_f797fc48\":[{\"filename\":\"jaraco.context-6.0.1-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jaraco_context_sdist_9bae4ea5\":[{\"filename\":\"jaraco_context-6.0.1.tar.gz\",\"version\":\"3.11\"}]}", "jaraco_functools": "{\"rules_python_publish_deps_311_jaraco_functools_py3_none_any_ad159f13\":[{\"filename\":\"jaraco.functools-4.1.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jaraco_functools_sdist_70f7e0e2\":[{\"filename\":\"jaraco_functools-4.1.0.tar.gz\",\"version\":\"3.11\"}]}", "jeepney": "{\"rules_python_publish_deps_311_jeepney_py3_none_any_c0a454ad\":[{\"filename\":\"jeepney-0.8.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jeepney_sdist_5efe48d2\":[{\"filename\":\"jeepney-0.8.0.tar.gz\",\"version\":\"3.11\"}]}", "keyring": "{\"rules_python_publish_deps_311_keyring_py3_none_any_5426f817\":[{\"filename\":\"keyring-25.4.1-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_keyring_sdist_b07ebc55\":[{\"filename\":\"keyring-25.4.1.tar.gz\",\"version\":\"3.11\"}]}", "markdown_it_py": "{\"rules_python_publish_deps_311_markdown_it_py_py3_none_any_35521684\":[{\"filename\":\"markdown_it_py-3.0.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_markdown_it_py_sdist_e3f60a94\":[{\"filename\":\"markdown-it-py-3.0.0.tar.gz\",\"version\":\"3.11\"}]}", "mdurl": "{\"rules_python_publish_deps_311_mdurl_py3_none_any_84008a41\":[{\"filename\":\"mdurl-0.1.2-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_mdurl_sdist_bb413d29\":[{\"filename\":\"mdurl-0.1.2.tar.gz\",\"version\":\"3.11\"}]}", "more_itertools": "{\"rules_python_publish_deps_311_more_itertools_py3_none_any_037b0d32\":[{\"filename\":\"more_itertools-10.5.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_more_itertools_sdist_5482bfef\":[{\"filename\":\"more-itertools-10.5.0.tar.gz\",\"version\":\"3.11\"}]}", "nh3": "{\"rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_14c5a72e\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_7b7c2a3c\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_aarch64_42c64511\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_armv7l_0411beb0\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64_5f36b271\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64le_34c03fa7\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_s390x_19aaba96\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_x86_64_de3ceed6\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_aarch64_f0eca9ca\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-musllinux_1_2_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_armv7l_3a157ab1\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-musllinux_1_2_armv7l.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_x86_64_36c95d4b\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-musllinux_1_2_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_win_amd64_8ce0f819\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-win_amd64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_sdist_94a16692\":[{\"filename\":\"nh3-0.2.18.tar.gz\",\"version\":\"3.11\"}]}", "pkginfo": "{\"rules_python_publish_deps_311_pkginfo_py3_none_any_889a6da2\":[{\"filename\":\"pkginfo-1.10.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pkginfo_sdist_5df73835\":[{\"filename\":\"pkginfo-1.10.0.tar.gz\",\"version\":\"3.11\"}]}", "pycparser": "{\"rules_python_publish_deps_311_pycparser_py3_none_any_c3702b6d\":[{\"filename\":\"pycparser-2.22-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pycparser_sdist_491c8be9\":[{\"filename\":\"pycparser-2.22.tar.gz\",\"version\":\"3.11\"}]}", "pygments": "{\"rules_python_publish_deps_311_pygments_py3_none_any_b8e6aca0\":[{\"filename\":\"pygments-2.18.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pygments_sdist_786ff802\":[{\"filename\":\"pygments-2.18.0.tar.gz\",\"version\":\"3.11\"}]}", "pywin32_ctypes": "{\"rules_python_publish_deps_311_pywin32_ctypes_py3_none_any_8a151337\":[{\"filename\":\"pywin32_ctypes-0.2.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pywin32_ctypes_sdist_d162dc04\":[{\"filename\":\"pywin32-ctypes-0.2.3.tar.gz\",\"version\":\"3.11\"}]}", "readme_renderer": "{\"rules_python_publish_deps_311_readme_renderer_py3_none_any_2fbca89b\":[{\"filename\":\"readme_renderer-44.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_readme_renderer_sdist_8712034e\":[{\"filename\":\"readme_renderer-44.0.tar.gz\",\"version\":\"3.11\"}]}", "requests": "{\"rules_python_publish_deps_311_requests_py3_none_any_70761cfe\":[{\"filename\":\"requests-2.32.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_requests_sdist_55365417\":[{\"filename\":\"requests-2.32.3.tar.gz\",\"version\":\"3.11\"}]}", "requests_toolbelt": "{\"rules_python_publish_deps_311_requests_toolbelt_py2_none_any_cccfdd66\":[{\"filename\":\"requests_toolbelt-1.0.0-py2.py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_requests_toolbelt_sdist_7681a0a3\":[{\"filename\":\"requests-toolbelt-1.0.0.tar.gz\",\"version\":\"3.11\"}]}", "rfc3986": "{\"rules_python_publish_deps_311_rfc3986_py2_none_any_50b1502b\":[{\"filename\":\"rfc3986-2.0.0-py2.py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_rfc3986_sdist_97aacf9d\":[{\"filename\":\"rfc3986-2.0.0.tar.gz\",\"version\":\"3.11\"}]}", "rich": "{\"rules_python_publish_deps_311_rich_py3_none_any_9836f509\":[{\"filename\":\"rich-13.9.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_rich_sdist_bc1e01b8\":[{\"filename\":\"rich-13.9.3.tar.gz\",\"version\":\"3.11\"}]}", "secretstorage": "{\"rules_python_publish_deps_311_secretstorage_py3_none_any_f356e662\":[{\"filename\":\"SecretStorage-3.3.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_secretstorage_sdist_2403533e\":[{\"filename\":\"SecretStorage-3.3.3.tar.gz\",\"version\":\"3.11\"}]}", "twine": "{\"rules_python_publish_deps_311_twine_py3_none_any_215dbe7b\":[{\"filename\":\"twine-5.1.1-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_twine_sdist_9aa08251\":[{\"filename\":\"twine-5.1.1.tar.gz\",\"version\":\"3.11\"}]}", "urllib3": "{\"rules_python_publish_deps_311_urllib3_py3_none_any_ca899ca0\":[{\"filename\":\"urllib3-2.2.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_urllib3_sdist_e7d814a8\":[{\"filename\":\"urllib3-2.2.3.tar.gz\",\"version\":\"3.11\"}]}", "zipp": "{\"rules_python_publish_deps_311_zipp_py3_none_any_a817ac80\":[{\"filename\":\"zipp-3.20.2-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_zipp_sdist_bc9eb26f\":[{\"filename\":\"zipp-3.20.2.tar.gz\",\"version\":\"3.11\"}]}"}, "packages": ["backports_tarfile", "certifi", "charset_normalizer", "docutils", "idna", "importlib_metadata", "jaraco_classes", "jaraco_context", "jaraco_functools", "keyring", "markdown_it_py", "mdurl", "more_itertools", "nh3", "pkginfo", "pygments", "readme_renderer", "requests", "requests_toolbelt", "rfc3986", "rich", "twine", "urllib3", "zipp"], "groups": {}}}}, "moduleExtensionMetadata": {"useAllRepos": "NO", "reproducible": false}, "recordedRepoMappingEntries": [["bazel_features+", "bazel_features_globals", "bazel_features++version_extension+bazel_features_globals"], ["bazel_features+", "bazel_features_version", "bazel_features++version_extension+bazel_features_version"], ["rules_python+", "bazel_features", "bazel_features+"], ["rules_python+", "bazel_skylib", "bazel_skylib+"], ["rules_python+", "bazel_tools", "bazel_tools"], ["rules_python+", "pypi__build", "rules_python++internal_deps+pypi__build"], ["rules_python+", "pypi__click", "rules_python++internal_deps+pypi__click"], ["rules_python+", "pypi__colorama", "rules_python++internal_deps+pypi__colorama"], ["rules_python+", "pypi__importlib_metadata", "rules_python++internal_deps+pypi__importlib_metadata"], ["rules_python+", "pypi__installer", "rules_python++internal_deps+pypi__installer"], ["rules_python+", "pypi__more_itertools", "rules_python++internal_deps+pypi__more_itertools"], ["rules_python+", "pypi__packaging", "rules_python++internal_deps+pypi__packaging"], ["rules_python+", "pypi__pep517", "rules_python++internal_deps+pypi__pep517"], ["rules_python+", "pypi__pip", "rules_python++internal_deps+pypi__pip"], ["rules_python+", "pypi__pip_tools", "rules_python++internal_deps+pypi__pip_tools"], ["rules_python+", "pypi__pyproject_hooks", "rules_python++internal_deps+pypi__pyproject_hooks"], ["rules_python+", "pypi__setuptools", "rules_python++internal_deps+pypi__setuptools"], ["rules_python+", "pypi__tomli", "rules_python++internal_deps+pypi__tomli"], ["rules_python+", "pypi__wheel", "rules_python++internal_deps+pypi__wheel"], ["rules_python+", "pypi__zipp", "rules_python++internal_deps+pypi__zipp"], ["rules_python+", "pythons_hub", "rules_python++python+pythons_hub"], ["rules_python++python+pythons_hub", "python_3_10_host", "rules_python++python+python_3_10_host"], ["rules_python++python+pythons_hub", "python_3_11_host", "rules_python++python+python_3_11_host"], ["rules_python++python+pythons_hub", "python_3_12_host", "rules_python++python+python_3_12_host"], ["rules_python++python+pythons_hub", "python_3_8_host", "rules_python++python+python_3_8_host"], ["rules_python++python+pythons_hub", "python_3_9_host", "rules_python++python+python_3_9_host"]]}}}}