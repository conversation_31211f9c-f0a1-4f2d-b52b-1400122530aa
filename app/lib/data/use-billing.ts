import useApi from './use-api'
import {
  BillingService,
  PaymentGateway,
  PaymentPeriod,
  Subscription,
  SubscriptionStatus,
  UpdateAccountRequest
} from '../../gen/service/billing/protos/service.pb'
import { useEffect, useMemo, useState, useCallback } from 'react'
import dayjs from 'dayjs'
import withJsonApi from './with-json-api'
import { CheckOverLimitExtRequest, UsageService } from '@sentio/service/usage/protos/service.pb'
import { ProjectOwnerAndSlug, WebService } from '@sentio/service/web'

export function useAccount(accountId?: string) {
  const { data, loading, mutate } = useApi(BillingService.GetAccount, accountId && { nameOrId: accountId })

  async function updateAccount(request: UpdateAccountRequest) {
    const f = withJsonApi(BillingService.UpdateAccount)
    const result = await f(request)
    mutate()
    return result
  }

  return { account: data, loading, mutate, updateAccount }
}

export function useSubscriptions(accountId?: string, before?: dayjs.Dayjs) {
  const { data, loading, mutate } = useApi(BillingService.GetSubscription, accountId && { nameOrId: accountId })
  const subscriptions = useMemo(() => {
    if (before) {
      return data?.subscriptions?.filter((s) => dayjs(s.startAt).isBefore(before)) ?? []
    }
    return data?.subscriptions ?? []
  }, [data?.subscriptions, before])

  const currentSubscription = useMemo(() => {
    return subscriptions.find((s) => s.status === SubscriptionStatus.ACTIVE)
  }, [subscriptions])

  const lastSubscription = useMemo(() => {
    const last: Subscription = subscriptions[0]
    for (const sub of subscriptions) {
      if (sub.status === SubscriptionStatus.ACTIVE) {
        return sub
      }
    }
    return last
  }, [subscriptions])

  const updateSubscription = async (
    accountId: string,
    planId: string,
    tier?: string,
    paymentPeriod?: PaymentPeriod
  ) => {
    const f = withJsonApi(BillingService.UpdateSubscription)
    await f({
      accountId,
      planId,
      tier,
      paymentPeriod
    })
    mutate()
  }

  const adminSubscription = async (subscription: Subscription) => {
    const f = withJsonApi(BillingService.AdminSubscription)
    await f(subscription)
    mutate()
  }

  return {
    subscriptions,
    currentSubscription,
    lastSubscription,
    adminSubscription,
    updateSubscription,
    loading,
    mutate
  }
}

export function useInvoice(invoiceId: string) {
  const { data, loading, mutate } = useApi(BillingService.GetInvoice, { invoiceId })
  return {
    invoice: data,
    loading,
    mutate
  }
}

export function usePlans(accountId?: string) {
  const { data, loading, mutate } = useApi(BillingService.ListPlans, accountId && { accountId })
  return {
    plans: data?.plans || [],
    loading,
    mutate
  }
}

export function usePaymentMethods(accountId?: string) {
  const { data, loading, mutate } = useApi(BillingService.GetPaymentMethods, accountId && { accountId })

  async function setupPayment(accountId: string) {
    const f = withJsonApi(BillingService.SetupPayment)
    const res = await f({ accountId })
    mutate()
    return res
  }

  return {
    cardInfo: data,
    loading,
    mutate,
    setupPayment
  }
}

export function useInvoices(accountId?: string) {
  const { data, loading, mutate } = useApi(
    BillingService.ListInvoices,
    accountId && {
      accountId
    }
  )

  return {
    invoices: data?.invoices || [],
    loading,
    mutate
  }
}

export function usePaymentIntents(id?: string, hash?: string) {
  const req = id && hash ? { id, hash } : null
  const { data, loading, ready, error, mutate } = useApi(BillingService.GetPaymentIntent, req)

  async function generatePaymentIntent(invoiceId?: string, paymentMethod?: PaymentGateway) {
    if (invoiceId) {
      const f = withJsonApi(BillingService.GenPaymentIntent)
      const resp = await f({ invoiceId, paymentMethod })
      mutate()
      return resp.paymentIntent
    }
  }

  return {
    paymentIntent: data?.paymentIntent,
    loading,
    ready,
    error,
    generatePaymentIntent
  }
}

export function useBillingWarning(accountId?: string, projectOwner?: string, projectSlug?: string) {
  const { account } = useAccount(accountId)
  const [overLimit, setOverLimit] = useState<string[]>([])
  const [overLimitLink, setOverLimitLink] = useState('')
  const [isDismissed, setIsDismissed] = useState(false)

  useEffect(() => {
    // Check if alert was dismissed and if the dismissal is still valid (within 1 week)
    if (projectOwner && projectSlug) {
      const dismissedKey = `over-quota-alert-dismissed-${projectOwner}-${projectSlug}`
      const dismissedData = localStorage.getItem(dismissedKey)

      if (dismissedData) {
        try {
          const { timestamp } = JSON.parse(dismissedData)
          const oneWeekInMs = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
          const isExpired = Date.now() - timestamp > oneWeekInMs

          if (!isExpired) {
            setIsDismissed(true)
          } else {
            // Clear expired dismissal data
            localStorage.removeItem(dismissedKey)
            setIsDismissed(false)
          }
        } catch (error) {
          // If parsing fails, clear the invalid data
          localStorage.removeItem(dismissedKey)
          setIsDismissed(false)
        }
      } else {
        setIsDismissed(false)
      }
    }
  }, [projectOwner, projectSlug])

  const dismissAlert = useCallback(() => {
    if (projectOwner && projectSlug) {
      const dismissedKey = `over-quota-alert-dismissed-${projectOwner}-${projectSlug}`
      const dismissalData = {
        timestamp: Date.now()
      }
      localStorage.setItem(dismissedKey, JSON.stringify(dismissalData))
      setIsDismissed(true)
    }
  }, [projectOwner, projectSlug])

  useEffect(() => {
    const abortController = new AbortController()
    async function checkOverLimit(owner: string, slug: string) {
      try {
        const projectRes = await withJsonApi(WebService.GetProject)(
          {
            ownerName: owner,
            slug: slug
          } as ProjectOwnerAndSlug,
          {
            signal: abortController.signal
          }
        )

        const projectId = projectRes?.project?.id
        const projectOwner = projectRes?.project?.owner
        setOverLimitLink(
          projectOwner?.organization?.id
            ? `/organizations/${projectOwner.organization.id}/billing#tab=plan`
            : `/profile/billing#tab=plan`
        )

        // Skip CheckOverLimit request if alert is dismissed
        if (projectId && !isDismissed) {
          const res = await withJsonApi(UsageService.CheckOverLimitExt)(
            {
              projectOwner: owner,
              projectSlug: slug
            } as CheckOverLimitExtRequest,
            {
              signal: abortController.signal
            }
          )
          setOverLimit(res.over || [])
        }
      } catch (e) {
        console.error(e)
      }
    }
    if (projectOwner && projectSlug) {
      checkOverLimit(projectOwner, projectSlug)
      return () => {
        abortController.abort('abort')
      }
    }
  }, [projectOwner, projectSlug, isDismissed])

  return useMemo(() => {
    return {
      isSuspended: account?.status === 'suspended',
      isOverLimit: overLimit.length > 0,
      overLimitLink,
      isDismissed,
      dismissAlert,
      overLimit
    }
  }, [account, overLimit, overLimitLink, isDismissed, dismissAlert])
}
