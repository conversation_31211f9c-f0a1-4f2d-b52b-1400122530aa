import {
  WebService,
  ListExternalDashboardsRequest,
  ListExternalDashboardsRequestOrderBy,
  ListExternalDashboardsRequestNameTagsFilter,
  GetExternalDashboardRequest,
  Dashboard,
  Panel,
  DashboardLayoutsLayout,
  DashboardResponsiveLayouts,
  ListExternalDashboardsResponse
} from '@sentio/service/web'
import useApi from './use-api'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { pickBy, identity, isEqual, cloneDeep, isNull, isEmpty, isUndefined } from 'lodash'
import { MutatorOptions } from 'swr'
import withJsonApi, { withJsonApiAutoToken } from './with-json-api'
import { nanoid } from 'nanoid'
import { Permission } from '@sentio/service/common'
import { useInfinite } from './use-infinite'

const PAGE_SIZE = 20

export const useExternalDashboards = (
  projectId?: string,
  orderBy?: ListExternalDashboardsRequestOrderBy,
  orderByTimeRange?: string,
  tagsFilter?: ListExternalDashboardsRequestNameTagsFilter,
  starFilter?: boolean,
  ownerFilter?: string, // owner id
  pageSize = PAGE_SIZE
) => {
  const req: ListExternalDashboardsRequest | null = useMemo(() => {
    if (!projectId) return null
    return pickBy(
      {
        projectId,
        orderBy,
        orderByTimeRange,
        tagsFilter,
        starFilter,
        ownerFilter
      },
      identity
    )
  }, [projectId, orderBy, orderByTimeRange, tagsFilter, starFilter, ownerFilter])

  const getKey = useCallback(
    (pageIndex: number, previousPageData?: ListExternalDashboardsResponse) => {
      if (!projectId) return null

      // reched the end
      if (previousPageData && (previousPageData.dashboards || []).length === 0) {
        return null
      }

      return pickBy(
        {
          projectId,
          orderBy,
          orderByTimeRange,
          tagsFilter,
          starFilter,
          ownerFilter,
          offset: pageIndex * pageSize,
          limit: pageSize
        },
        identity
      )
    },
    [projectId, orderBy, orderByTimeRange, tagsFilter, starFilter, ownerFilter]
  )

  const fetcher = useCallback(async (req: ListExternalDashboardsRequest) => {
    return await withJsonApiAutoToken(WebService.ListExternalDashboards)(req)
  }, [])

  return useInfinite(getKey, fetcher, (d) => d.dashboards || [], pageSize)

  // return useApi(WebService.ListExternalDashboards, req)
}

function getInitLayout(dashboard: Dashboard): DashboardResponsiveLayouts | undefined {
  const { panels, layouts } = dashboard
  if (isUndefined(panels)) {
    return undefined
  }
  if ((isNull(layouts) || isEmpty(layouts)) && Object.keys(panels!).length > 0) {
    // no layout, create a new one
    return {
      responsiveLayouts: {
        md: {
          layouts: Object.keys(panels!).map((id, index) => {
            return {
              i: id,
              x: (index % 2) * 6,
              y: Math.floor(index / 2) * 4,
              w: 6,
              h: 4
            }
          })
        }
      }
    }
  }
  return layouts
}

export const useExternalDashboard = (dashboardId?: string, projectId?: string, dashboardUrl?: string) => {
  const req: GetExternalDashboardRequest | null = useMemo(() => {
    if ((!dashboardId && !dashboardUrl) || !projectId) return null
    if (dashboardId) {
      return { dashboardId, projectId }
    } else {
      return { url: dashboardUrl, projectId }
    }
  }, [dashboardId, projectId, dashboardUrl])

  const res = useApi(WebService.GetExternalDashboard, req)
  const tokenRef = useRef(res.token)
  tokenRef.current = res.token
  const dashboardRef = useRef(res.data?.dashboard?.dashboard)
  dashboardRef.current = res.data?.dashboard?.dashboard
  const mutateRef = useRef(res.mutate)
  mutateRef.current = res.mutate
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    // reset state
    if (req) {
      setSaving(false)
    }
  }, [req])

  const saveDashboard = useCallback(
    async (data: Dashboard, options?: MutatorOptions, forceSave?: boolean) => {
      const dashboard = dashboardRef.current as Dashboard
      const token = tokenRef.current
      if (!forceSave && isEqual(data, dashboard)) {
        return
      }
      setSaving(true)
      const newDashboard = await withJsonApi(WebService.SaveDashboard, token)(data)
      const newExternalDashboard = cloneDeep(res?.data)
      newExternalDashboard.dashboard = {
        dashboard: newDashboard
      }
      mutateRef.current?.(newExternalDashboard, options)
      setSaving(false)
    },
    [res?.mutate, res?.data]
  )

  const savePanel = useCallback(
    async (p: Panel, layout?: DashboardLayoutsLayout) => {
      const createPanels: string[] = []
      const editPanels: string[] = []
      if (!p.id) {
        p = cloneDeep(p)
        p.id = nanoid(16)
        createPanels.push(p.id)
      } else {
        editPanels.push(p.id)
      }
      const _dashboard = dashboardRef.current as Dashboard
      if (_dashboard) {
        const dashboard = cloneDeep(_dashboard)
        dashboard.panels = dashboard.panels || {}
        dashboard.panels[p.id!] = p
        if (layout && dashboard.layouts?.responsiveLayouts) {
          Object.values(dashboard.layouts?.responsiveLayouts).forEach((l) => {
            l?.layouts?.push(layout)
          })
        } else if (!dashboard.layouts) {
          dashboard.layouts = getInitLayout(dashboard)
        }
        dashboard.createPanels = createPanels
        dashboard.editPanels = editPanels
        await saveDashboard(dashboard)
      }
    },
    [saveDashboard]
  )

  const removePanel = useCallback(
    (id?: string) => {
      const dashboard = dashboardRef.current as Dashboard
      if (dashboard && id) {
        const newDashboard = cloneDeep(dashboard)
        newDashboard.panels = newDashboard.panels || {}
        delete newDashboard.panels[id]
        for (const v of Object.values(newDashboard.layouts?.responsiveLayouts || {}) || []) {
          v.layouts = v.layouts || []
          v.layouts = v.layouts.filter((l) => l.i !== id)
        }
        saveDashboard(newDashboard)
      }
    },
    [saveDashboard]
  )

  const allowEdit = (res.data?.dashboard?.permissions || []).includes(Permission.WRITE)

  return { ...res, saveDashboard, savePanel, removePanel, allowEdit, saving }
}
