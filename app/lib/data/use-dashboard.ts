import { useEffect, useState, useMemo, useCallback } from 'react'
import { Panel, WebService, Dashboard, DashboardLayoutsLayout, GetDashboardRequest } from 'gen/service/web'
import useApi from './use-api'
import { cloneDeep, isEmpty, isEqual, uniqBy } from 'lodash'
import withJsonApi from './with-json-api'
import { nanoid } from 'nanoid'
import { Permission } from 'gen/service/common'
import dayjs from 'dayjs'
import { produce } from 'immer'
import { MutatorOptions } from 'swr'
import { Project } from '../../gen/service/common'
import posthog from 'posthog-js'

function trackDashboardSaving(dashboard: Dashboard, from: string) {
  try {
    posthog.capture('Save Dashboard', {
      from,
      layouts: dashboard.layouts?.responsiveLayouts,
      panels: dashboard.panels
        ? Object.values(dashboard.panels).map((p) => {
            return {
              id: p.id,
              name: p.name
            }
          })
        : []
    })
  } catch {
    //ignore
  }
}

function deduplicateLayouts(dashboard?: Dashboard) {
  if (dashboard?.layouts?.responsiveLayouts) {
    for (const [_, layout] of Object.entries(dashboard.layouts.responsiveLayouts)) {
      layout.layouts = uniqBy(layout.layouts, 'i')
    }
  }
  return dashboard
}

function calculateBottomLayout(layouts: DashboardLayoutsLayout[]): number {
  if (layouts.length === 0) return 0
  return Math.max(...layouts.map((l) => (l.y ?? 0) + (l.h ?? 0)))
}

export function useProjectDashboards(projectId?: string, projectOwner?: string, projectSlug?: string) {
  const [saving, setSaving] = useState(false)
  const req = useMemo(() => {
    if (projectId) {
      return {
        projectId
      } as GetDashboardRequest
    } else if (projectOwner && projectSlug) {
      return {
        ownerName: projectOwner,
        slug: projectSlug
      } as GetDashboardRequest
    }
    return null
  }, [projectId, projectOwner, projectSlug])
  const { data, loading, token, mutate } = useApi(WebService.ListDashboards, req)

  const createDashboard = async (dashboard: Dashboard) => {
    if (dashboard?.projectId === undefined) {
      return
    }
    setSaving(true)
    const newDashboard = await withJsonApi(WebService.SaveDashboard, token)(dashboard)
    trackDashboardSaving(dashboard, 'createDashboard')
    mutate({ dashboards: [...(data?.dashboards || []), newDashboard] })
    setSaving(false)
    return newDashboard
  }

  const deleteDashboard = async (dashboardId?: string) => {
    setSaving(true)
    await withJsonApi(WebService.DeleteDashboard, token)({ dashboardId })
    mutate(
      produce(data, (draft) => {
        if (draft.dashboards) {
          draft.dashboards = draft.dashboards.filter((d) => d.id !== dashboardId)
        }
      })
    )
    setSaving(false)
  }

  const { dashboards = [] } = data || {}
  const defaultDashboard = useMemo(() => {
    const result = dashboards?.find((d) => d.default)
    return (
      result ??
      (dashboards ?? []).reduce((prev, curr) => (dayjs(prev.createdAt).isBefore(curr.createdAt) ? prev : curr), {})
    )
  }, [dashboards])

  return {
    loading,
    saving,
    dashboards: data?.dashboards || [],
    defaultDashboard,
    token,
    createDashboard,
    deleteDashboard,
    mutate
  }
}

export function useDashboard(project?: Project | string, dashboardId?: string) {
  const request =
    typeof project == 'string'
      ? { dashboardId, projectId: project }
      : {
          dashboardId,
          ownerName: project?.ownerName,
          slug: project?.slug
        }
  const { data, mutate, error, token, loading } = useApi(WebService.GetDashboard, project && dashboardId && request)
  if (error) {
    if (error.status == 404 || error.status == 403 || error.status == 401) {
      throw error
    }
  }

  const [dashboard, setDashboard] = useState(data?.dashboards && data?.dashboards[0])
  const [saving, setSaving] = useState(false)
  const allowEdit = (data?.permissions || []).includes(Permission.WRITE)
  const resetDashboard = () => {
    setDashboard(cloneDeep(data?.dashboards && data?.dashboards[0]))
  }

  useEffect(resetDashboard, [data])

  const removePanel = (id?: string) => {
    if (dashboard && id) {
      const newDashboard = cloneDeep(dashboard)
      newDashboard.panels = newDashboard.panels || {}
      delete newDashboard.panels[id]
      for (const v of Object.values(newDashboard.layouts?.responsiveLayouts || {}) || []) {
        v.layouts = v.layouts || []
        v.layouts = v.layouts.filter((l) => l.i !== id)
      }
      withJsonApi(
        WebService.SaveDashboard,
        token
      )(newDashboard).then((data) => {
        setDashboard(data)
      })
      trackDashboardSaving(newDashboard, 'removePanel')
    }
  }

  const saveDashboard = useCallback(
    async (options?: MutatorOptions) => {
      const deduplicatedDashboard = deduplicateLayouts(dashboard)
      if (isEqual(deduplicatedDashboard, data?.dashboards && data?.dashboards[0])) {
        return
      }
      setSaving(true)
      const newDashboard = await withJsonApi(WebService.SaveDashboard, token)(deduplicatedDashboard)
      trackDashboardSaving(newDashboard, 'saveDashboard')
      mutate(
        produce(data, (draft) => {
          draft.dashboards = [newDashboard]
        }),
        options
      )
      setSaving(false)
    },
    [dashboard, data, mutate, token]
  )

  const updateDashboard = useCallback(
    async (dashboard: Dashboard, options?: MutatorOptions) => {
      if (isEqual(dashboard, data?.dashboards && data?.dashboards[0])) {
        return
      }
      setSaving(true)
      const newDashboard = await withJsonApi(WebService.SaveDashboard, token)(dashboard)
      trackDashboardSaving(newDashboard, 'updateDashboard')
      mutate(
        produce(data, (draft) => {
          draft.dashboards = [newDashboard]
        }),
        options
      )
      setSaving(false)
      return newDashboard
    },
    [data, mutate, token]
  )

  const savePanel = useCallback(
    async (p: Panel, layout?: DashboardLayoutsLayout) => {
      const createPanels: string[] = []
      const editPanels: string[] = []
      if (!p.id) {
        p = produce(p, (draft) => {
          draft.id = nanoid(16)
        })
        createPanels.push(p.id!)
      } else {
        editPanels.push(p.id!)
      }
      if (dashboard) {
        dashboard.panels = dashboard.panels || {}
        const isNewPanel = !dashboard.panels[p.id!]
        dashboard.panels[p.id!] = p

        // Initialize layouts if not exists
        if (!dashboard.layouts) {
          dashboard.layouts = {
            responsiveLayouts: {
              md: { layouts: [] },
              xxs: { layouts: [] }
            }
          }
        } else if (!dashboard.layouts.responsiveLayouts || isEmpty(dashboard.layouts.responsiveLayouts)) {
          dashboard.layouts.responsiveLayouts = {
            md: { layouts: [] },
            xxs: { layouts: [] }
          }
        }

        // If no layout provided and the panel is new, create a default one
        if (!layout && isNewPanel) {
          const currentLayouts = dashboard.layouts.responsiveLayouts?.md?.layouts || []
          layout = {
            i: p.id!,
            x: 0,
            y: calculateBottomLayout(currentLayouts),
            w: 6,
            h: 4
          }
        }

        // Add layout to all responsive layouts
        const responsiveLayouts = dashboard.layouts.responsiveLayouts
        if (responsiveLayouts) {
          Object.entries(responsiveLayouts).forEach(([name, l]) => {
            const uniqLayouts = uniqBy(l.layouts, 'i')
            if (uniqLayouts && uniqLayouts && layout) {
              const existingIndex = uniqLayouts.findIndex((item) => item.i === layout!.i)
              if (existingIndex !== -1) {
                return
              } else {
                uniqLayouts.push(layout)
              }
            }
            responsiveLayouts[name].layouts = uniqLayouts
          })
        }
        dashboard.createPanels = createPanels
        dashboard.editPanels = editPanels
        await saveDashboard()
        delete dashboard.createPanels
        delete dashboard.editPanels
      }
    },
    [dashboard, saveDashboard]
  )

  const importJSON = async (json: string, overrideLayouts = true) => {
    setSaving(true)
    const newDashboard = await withJsonApi(
      WebService.ImportDashboard,
      token
    )({
      dashboardId,
      dashboardJson: JSON.parse(json),
      overrideLayouts
    })
    mutate(
      produce(data, (draft) => {
        draft.dashboards = [newDashboard as Dashboard]
      })
    )
    setSaving(false)
  }

  const shareDashboard = async () => {
    setSaving(true)
    const newSharing = await withJsonApi(
      WebService.SaveDashboardSharing,
      token
    )({
      dashboardId,
      isPublic: true
    })
    mutate(
      produce(data, (draft) => {
        if (draft.dashboards?.[0]) {
          draft.dashboards[0].sharing = newSharing
        }
      })
    )
    setSaving(false)
  }

  const removeDashboardShare = async () => {
    setSaving(true)
    await withJsonApi(WebService.RemoveDashboardSharing, token)({ dashboardId })
    mutate(
      produce(data, (draft) => {
        if (draft.dashboards?.[0]) {
          draft.dashboards[0].sharing = undefined
        }
      })
    )
    setSaving(false)
  }

  return {
    dashboard,
    loading,
    allowEdit,
    error,
    setDashboard,
    savePanel,
    removePanel,
    resetDashboard,
    saveDashboard,
    importJSON,
    shareDashboard,
    removeDashboardShare,
    saving,
    updateDashboard,
    mutate
  }
}

export function useDashboardJSON(dashboardId?: string) {
  const { data, loading, error } = useApi(
    WebService.ExportDashboard,
    dashboardId && {
      dashboardId
    }
  )

  return {
    loading,
    dashboardJSON: data?.dashboardJson,
    error
  }
}

export async function savePanel(project: Project, dashboardId: string, p: Panel) {
  const res = await withJsonApi(WebService.GetDashboard)({ projectId: project.id, dashboardId })
  const dashboard = res.dashboards?.[0]
  if (!p.id) {
    p.id = nanoid(16)
  }
  if (dashboard) {
    dashboard.panels = dashboard.panels || {}
    dashboard.panels[p.id] = p
    await withJsonApi(WebService.SaveDashboard)(dashboard)
  }
}
