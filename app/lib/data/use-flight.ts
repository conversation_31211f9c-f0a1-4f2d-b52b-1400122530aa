import { atomWithHash } from 'jotai-location'
import { Router } from 'next/router'
import { atom, useAtom } from 'jotai'
import { useState, useEffect } from 'react'

/**
 * Flight Configuration
 *
 * NOTE: The following flights were completely removed on April 8, 2025 as they were always enabled ('*': true):
 * - LogFlight, OrgFlight, InsightsFlight, NewEditFlight, NewLayoutFlight, UsageFlight
 * - EventLogView, EventLogPanel, SimulatorFlight, ForkedChainFlight, ImportProjectFlight
 * - DataExplorerFlight, GraphqlFlight, AiFlight, AccountsFlight, SolidityIntelligenceFlight
 * - RetentionFlight, CompilationFlight, ExtensionFlight, CompareFlight, BundleSimulateFlight
 * - ContractStorageFlight, ContractOverrideFlight, CoderWorkspaceFlight, QueryEndpointFlight
 * - NoneTimeSQLFlight, DatasourceMetricsFlight, ChartSQLQueryIdFlight, NodeRPCFlight
 * - SimulationShareFlight, EmailAlertsFlight, TableColumnsFlight, OobFlight, AutoProcessorFlight
 *
 * All references to these flights have been updated to use `true` directly since these features
 * were always enabled.
 */

interface Flight {
  name: string
  enable: { [key: string]: boolean }
}

export const DemoFlight: Flight = {
  name: 'demo',
  enable: {
    dev: true,
    '*': false
  }
}

export const AnalyticsFlight: Flight = {
  name: 'analytics',
  enable: {
    dev: true,
    '*': false
  }
}

export const ClickHouseFlight: Flight = {
  name: 'clickhouse',
  enable: {
    dev: true,
    '*': false
  }
}

export const SnapFlight: Flight = {
  name: 'snap',
  enable: {
    dev: true,
    '*': false
  }
}

export const SuiFundflowFlight: Flight = {
  name: 'sui-fundflow',
  enable: {
    dev: true,
    '*': false
  }
}

export const NewThemeFlight: Flight = {
  name: 'new-theme',
  enable: {
    dev: true,
    '*': false
  }
}

export const CommunityFlight: Flight = {
  name: 'community',
  enable: {
    dev: true,
    '*': true
  }
}

export const ShareQueryFlight: Flight = {
  name: 'share-query',
  enable: {
    dev: true,
    '*': true
  }
}

export const OverLimitFlight: Flight = {
  name: 'over-limit',
  enable: {
    dev: true,
    '*': false
  }
}

const AllFlights = [
  DemoFlight,
  AnalyticsFlight,
  ClickHouseFlight,
  SnapFlight,
  SuiFundflowFlight,
  NewThemeFlight,
  CommunityFlight,
  ShareQueryFlight,
  OverLimitFlight
]

function getDefaultFlights() {
  const hostname = typeof window !== 'undefined' ? window.location.hostname : ''
  const isDev =
    hostname === 'localhost' ||
    hostname.startsWith('dev') ||
    hostname.startsWith('test') ||
    hostname.startsWith('sui-test')
  const flights = AllFlights.filter((f) => {
    return f.enable['dev'] && isDev ? true : f.enable[hostname] || f.enable['*']
  })
  return flights.map((f) => f.name).join(',')
}

const flightAtom = atomWithHash('flight', getDefaultFlights(), {
  serialize: (value) => value,
  deserialize: (value) => value, // || unstable_NO_STORAGE_VALUE,
  setHash: 'replaceState',
  subscribe: (callback) => {
    Router.events.on('routeChangeComplete', callback)
    window.addEventListener('hashchange', callback)
    return () => {
      Router.events.off('routeChangeComplete', callback)
      window.removeEventListener('hashchange', callback)
    }
  }
})

export const flightAtoms = atom((get) => get(flightAtom).split(','))

const flightMap: { [key: string]: Flight } = AllFlights.reduce((acc, flight) => {
  acc[flight.name] = flight
  return acc
}, {})

export function useFlight() {
  const [flight] = useAtom(flightAtom)
  const flights = flight.split(',')

  const flightEnabled = (f: Flight) => {
    if (!f?.name) return false
    return flights.includes(f.name)
  }

  const getFlights = () => {
    return flights.map((f) => flightMap[f]) as Flight[]
  }

  return {
    isFlightEnabled: flightEnabled,
    getFlights
  }
}

export function useFlightWith(initFlight: Flight) {
  const [flights] = useAtom(flightAtoms)
  const isEnabled = flights.includes(initFlight.name)
  const [flag, setFlag] = useState(false)
  useEffect(() => {
    setFlag(isEnabled)
  }, [isEnabled])
  return flag
}
