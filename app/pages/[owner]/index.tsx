import { humanizeDate } from 'components/endpoint/utils'
import Layout from 'components/layout/OrgLayout'
import { ProjectCard } from 'components/project/ProjectCard'
import UserIcon from 'components/user/UserIcon'
import { useOrganization } from 'lib/data/use-organization'
import { useProjects, useStarredProjects } from 'lib/data/use-projects'
import useUser, { useUserInfo } from 'lib/data/use-user'
import { useRouter } from 'next/router'
import React, { useCallback, useContext, useMemo, useState } from 'react'
import router from 'next/router'
import { OrganizationRole, Project } from '@sentio/service/common'
import { CreateProjectFrom, ProjectSlideOver } from 'components/project/ProjectSlideOver'
import { CloneProject } from 'components/project/CloneProject'
import { ProjectDeleteConfirm } from 'components/project/DeleteConfirm'
import { getProjectUrl } from 'lib/data/use-project'
import { NotificationContext } from 'lib/data/use-notification'
import { PopupMenuButton } from 'components/menu/PopupMenuButton'
import { usePlans, useSubscriptions } from 'lib/data/use-billing'
import { Money, Zero } from 'lib/money'
import { Plan } from '@sentio/service/billing/protos/service.pb'
import dayjs from 'dayjs'
import { useStatistic } from 'lib/data/use-statistic'
import BigDecimal from '@sentio/bigdecimal'
import { PopoverTooltip } from 'components/common/tooltip/DivTooltip'
import { Tag } from 'components/common/tags/Tag'
import NewButton from 'components/common/buttons/NewButton'
import Link from 'next/link'

function goProject(project: Project) {
  router.push(`/${project.owner}/${project.slug}`)
}

// Skeleton components for loading states
const HeaderSkeleton = () => {
  return (
    <div className="mb-8 flex flex-col md:flex-row md:items-center">
      <div className="mr-6 h-20 w-20 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
      <div className="flex-1">
        <div className="h-8 w-48 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
        <div className="mt-2 h-5 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
      </div>
    </div>
  )
}
const PinnedSkeleton = () => {
  return (
    <div className="mb-8">
      <div className="mb-4 h-7 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
      <div className="flex w-full flex-wrap gap-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="h-[126px] w-[290px] animate-pulse rounded-md bg-gray-200 dark:bg-gray-700"></div>
        ))}
      </div>
    </div>
  )
}

const RepositoryListSkeleton = () => {
  return (
    <div>
      <div className="mb-4 h-7 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>

      {/* Search and Filters Skeleton */}
      <div className="mb-4 flex flex-wrap gap-2">
        <div className="relative flex-1">
          <div className="h-10 w-full animate-pulse rounded-md bg-gray-200 dark:bg-gray-700"></div>
        </div>
        <div className="flex gap-2">
          <div className="h-10 w-16 animate-pulse rounded-md bg-gray-200 dark:bg-gray-700"></div>
        </div>
      </div>

      {/* Repository List Skeleton */}
      <div>
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="mb-2 h-16 w-full animate-pulse rounded-md bg-gray-200 dark:bg-gray-700"></div>
        ))}
      </div>
    </div>
  )
}

const PeopleSectionSkeleton = () => {
  return (
    <div className="mb-6">
      <div className="mb-3 h-6 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
      <div className="flex flex-wrap gap-2">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="h-10 w-10 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
        ))}
      </div>
    </div>
  )
}

const ZeroPlan = {
  name: 'Free',
  unitCap: '0',
  flatFee: Zero,
  unitPrice: Zero
} as Plan

function OrgHomePage() {
  const router = useRouter()
  const { owner } = router.query
  const { organization, loading: isOrgLoading } = useOrganization(owner as string)
  const { data: userData, loading: isUserLoading } = useUserInfo(owner ? { userName: owner as string } : null, {
    onError: () => {},
    shouldRetryOnError: false,
    revalidateIfStale: false,
    revalidateOnFocus: false,
    revalidateOnReconnect: false
  })
  const { deleteProject, projects, sharedProjects } = useProjects()
  const isOrg = Boolean(organization)
  const isUser = !isOrg && Boolean(userData)
  const { starredProjects, isStarred, toggleStar, loading: isStarredLoading } = useStarredProjects()
  const [openCreateSider, setOpenCreateSider] = useState(false)
  const { user } = useUser()
  const [editProject, setEditProject] = useState<undefined | Project>(undefined)
  const [createProjectFrom, setCreateProjectFrom] = useState<CreateProjectFrom>()
  const [deleting, setDeleting] = useState(false)
  const [cloneProject, setCloneProject] = useState<Project | undefined>(undefined)
  const { showNotification } = useContext(NotificationContext)
  const { currentSubscription: subscription } = useSubscriptions(organization?.id)
  const { plans, loading } = usePlans(organization?.id)
  const [start, end] = useMemo(() => {
    return [dayjs().utc().utcOffset(0).startOf('month'), dayjs().utc().utcOffset(0).endOf('month')]
  }, [])

  const { data: usages } = useStatistic(
    organization?.id,
    'organizations',
    start?.toISOString(),
    end?.toISOString(),
    '-',
    false
  )

  const plan = useMemo(() => {
    if (subscription?.plan) {
      return subscription.plan
    } else {
      // no subscription, find the free plan
      return plans?.find((p) => p.tierDefaultPlan === 'FREE') || ZeroPlan
    }
  }, [subscription, plans])
  const { totalUsage, additionUsage, additionFee, totalFee } = useMemo(() => {
    let totalUsage = 0
    let additionUsage = 0
    let additionFee = Zero
    let totalFee = Zero
    if (usages) {
      totalUsage = usages.reduce((acc, cur) => acc + parseFloat(cur.cost || '0'), 0)
      additionUsage = totalUsage - parseFloat(plan.unitCap || '0')
      if (additionUsage < 0) {
        additionUsage = 0
      }
      additionFee = new Money(plan.unitPrice).multiply(additionUsage)
      totalFee = new Money(plan.flatFee).add(additionFee)
    }
    return {
      totalUsage,
      additionUsage,
      additionFee,
      totalFee
    }
  }, [usages, plan])

  // Loading states
  const isLoading = isOrgLoading || isUserLoading || isStarredLoading

  // Search state
  const [searchQuery, setSearchQuery] = useState('')

  const currentProjects = useMemo(() => {
    if (isOrg) {
      return organization?.projects || []
    } else if (isUser) {
      return [...projects, ...sharedProjects].filter((p) => p.owner?.user?.id === userData?.id)
    }
    return []
  }, [isOrg, isUser, organization, userData, projects, sharedProjects])

  const currentStarredProjects = useMemo(() => {
    return currentProjects.filter((p) => starredProjects.some((sp) => p.id === sp.id))
  }, [currentProjects, starredProjects])

  // Filtered projects based on search query
  const filteredProjects = useMemo(() => {
    if (!searchQuery.trim()) {
      return currentProjects
    }

    const query = searchQuery.toLowerCase().trim()
    return currentProjects.filter((project) => {
      // Search by slug
      if (project.slug?.toLowerCase().includes(query)) {
        return true
      }

      // Search by display name
      if (project.displayName?.toLowerCase().includes(query)) {
        return true
      }

      // Search by description
      if (project.description?.toLowerCase().includes(query)) {
        return true
      }

      return false
    })
  }, [currentProjects, searchQuery])

  const allowAdmin = useMemo(() => {
    let result = false
    if (isOrg) {
      result = (organization?.members || []).find((m) => m.user?.id === user?.id)?.role === OrganizationRole.ORG_ADMIN
    } else if (isUser) {
      result = user?.id === userData?.id
    }
    return result
  }, [user, organization?.members, isOrg])

  const onCreate = useCallback((preferOwner?: string, createFrom?: CreateProjectFrom) => {
    if (preferOwner) {
      setEditProject({ ownerName: preferOwner } as Project)
    } else {
      setEditProject(undefined)
    }
    setCreateProjectFrom(createFrom)
    setOpenCreateSider(true)
  }, [])

  const onSelect = useCallback((method, ...args) => {
    if (method === 'edit') {
      setEditProject(args[0])
      setOpenCreateSider(true)
    }
    if (method === 'delete') {
      setEditProject(args[0])
      setDeleting(true)
    }
    if (method === 'clone') {
      setCloneProject(args[0])
    }
  }, [])

  const _deleteProject = async () => {
    if (editProject?.id) {
      const projectName = getProjectUrl(editProject)
      await deleteProject(editProject.id)
      showNotification(
        {
          type: 'success',
          title: 'Delete Project',
          message: `Successfully deleted project ${projectName}`
        },
        3
      )
      setOpenCreateSider(false)
    }
  }

  return (
    <Layout title={`${owner || 'Organization'}`}>
      <div className="container mx-auto px-4 py-12">
        {/* Organization Header */}
        {isLoading ? (
          <HeaderSkeleton />
        ) : (
          <div className="group relative mb-8 flex flex-col md:flex-row md:items-center">
            {/* Organization Logo */}
            <div className="mr-6 grid h-20 w-20 items-center justify-items-center overflow-hidden rounded-full">
              {isOrg ? (
                <UserIcon src={organization?.logoUrl} username={organization?.name} size="xl" />
              ) : isUser ? (
                <UserIcon src={userData?.picture} username={userData?.username} size="xl" />
              ) : null}
            </div>

            {/* Organization Info */}
            <div className="flex-1">
              <div className="flex items-center">
                {isOrg ? (
                  <h1 className="text-text-foreground text-2xl font-bold">
                    {organization?.displayName || organization?.name || 'Organization'}
                  </h1>
                ) : isUser ? (
                  <h1 className="text-text-foreground text-2xl font-bold">
                    {userData?.firstName && userData?.lastName
                      ? `${userData.firstName} ${userData.lastName}`
                      : userData?.username || 'User'}
                  </h1>
                ) : (
                  <h1 className="text-text-foreground text-2xl font-bold">Sentio</h1>
                )}
                {isOrg && (
                  <div className="border-primary-500 text-primary-500 ml-2 rounded-full border px-2 py-0.5 text-xs">
                    Verfied Organization
                  </div>
                )}
                {isUser && (
                  <div className="border-primary-500 text-primary-500 ml-2 rounded-full border px-2 py-0.5 text-xs">
                    Verified User
                  </div>
                )}
                {!isOrg && !isUser && (
                  <div className="ml-2 rounded-full border border-gray-500 px-2 py-0.5 text-xs text-gray-500">
                    Unknown
                  </div>
                )}
                {isOrg && organization?.tier && (
                  <div
                    className={`ml-2 rounded-full px-2 py-0.5 text-xs font-medium ${
                      organization.tier === 'FREE'
                        ? 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                        : organization.tier === 'DEV'
                          ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-300'
                          : organization.tier === 'PRO'
                            ? 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-300'
                            : organization.tier === 'ENTERPRISE'
                              ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-300'
                              : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {organization.tier}
                  </div>
                )}
              </div>

              <div className="mt-2 flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <span className="mr-1">{currentProjects.length} projects</span>
                </div>
                {isOrg && organization?.updatedAt && (
                  <div className="flex items-center">
                    <span>Last updated at {humanizeDate(parseInt(organization.updatedAt, 10))}</span>
                  </div>
                )}
              </div>
            </div>

            {isOrg && allowAdmin && (
              <div className="absolute bottom-4 right-4">
                <Link href={`/organizations/${owner}`}>
                  <NewButton size="lg">Edit Organization</NewButton>
                </Link>
              </div>
            )}
          </div>
        )}

        <hr className="border-border-color" />

        {/* Main Content Area */}
        <div className="mt-8 flex flex-col lg:flex-row">
          {/* Left Content - Repositories */}
          <div className="flex-1 lg:mr-8">
            {/* Pinned Section */}
            {isLoading ? (
              <PinnedSkeleton />
            ) : (
              <div className="mb-8">
                <h2 className="text-text-foreground mb-4 text-xl font-medium">Starred Projects</h2>
                <div className="flex w-full flex-wrap gap-4">
                  {currentStarredProjects.length === 0 ? (
                    <div className="text-text-foreground w-full py-4 text-center">No starred projects</div>
                  ) : (
                    currentStarredProjects.map((project: any) => (
                      <div
                        className="flex-0 cursor-pointer"
                        key={project.id}
                        onClick={() => {
                          goProject(project)
                        }}
                      >
                        <ProjectCard
                          view="grid"
                          project={project}
                          isStarred={isStarred}
                          toggleStar={toggleStar}
                          allowAdmin={allowAdmin}
                          allowEdit={allowAdmin}
                          onSelect={onSelect}
                        />
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            {/* Repositories Section */}
            {isLoading ? (
              <RepositoryListSkeleton />
            ) : (
              <div>
                <h2 className="text-text-foreground mb-4 text-xl font-medium">Projects</h2>

                {/* Search and Filters */}
                <div className="mb-4 flex flex-wrap gap-2">
                  <div className="relative flex-1">
                    <input
                      type="text"
                      placeholder="Find a project..."
                      className="border-border-color text-text-foreground w-full rounded-md border bg-transparent px-3 py-2 pr-8 text-sm"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    {searchQuery && (
                      <button
                        className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        onClick={() => setSearchQuery('')}
                        type="button"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                  {allowAdmin && (
                    <div className="flex gap-2">
                      <PopupMenuButton
                        buttonIcon={
                          <button className="rounded-md bg-green-600 px-3 py-2 text-sm text-white">New</button>
                        }
                        items={[
                          [
                            {
                              label: 'Empty Project',
                              key: 'empty'
                            },
                            {
                              label: 'From Example',
                              key: 'example'
                            }
                          ]
                        ]}
                        onSelect={(key: string) => {
                          onCreate(owner as string, key as CreateProjectFrom)
                        }}
                      />
                    </div>
                  )}
                </div>

                {/* Repository List */}
                <div>
                  {currentProjects.length === 0 ? (
                    <div className="text-text-foreground py-4 text-center">No projects found</div>
                  ) : filteredProjects.length === 0 ? (
                    <div className="text-text-foreground py-4 text-center">No projects match your search</div>
                  ) : (
                    filteredProjects.map((project: any) => (
                      <div
                        className="cursor-pointer"
                        key={project.id}
                        onClick={() => {
                          goProject(project)
                        }}
                      >
                        <ProjectCard
                          view="list"
                          project={project}
                          isStarred={isStarred}
                          toggleStar={toggleStar}
                          allowAdmin={allowAdmin}
                          allowEdit={allowAdmin}
                          onSelect={onSelect}
                        />
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Right Sidebar */}
          {isOrg && (
            <div className="mt-8 w-full lg:mt-0 lg:w-80">
              {/* People Section */}
              {isLoading ? (
                <PeopleSectionSkeleton />
              ) : (
                <>
                  <div className="mb-4 sm:mb-16">
                    <h3 className="text-text-foreground mb-3 text-lg font-medium">Members</h3>
                    <div className="group relative flex flex-wrap gap-2 pb-8">
                      {/* Avatar placeholders */}
                      {organization?.members?.length === 0 ? (
                        <div className="text-text-foreground py-2 text-sm">No members found</div>
                      ) : (
                        organization?.members?.map((member, i) => (
                          <PopoverTooltip
                            hideArrow
                            placementOption="top-start"
                            text={
                              <div className="min-w-[120px] p-2">
                                <div className="flex items-center gap-2">
                                  <div className="space-y-2">
                                    <div>
                                      <div className="text-xs font-medium text-gray-500">Username</div>
                                      <div className="font-medium">{member.user?.username}</div>
                                    </div>
                                    <div>
                                      <div className="text-xs font-medium text-gray-500">Full Name</div>
                                      <div className="font-ilabel text-gray block truncate text-xs">
                                        {member.user?.firstName} {member.user?.lastName}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-xs font-medium text-gray-500">Role</div>
                                      {member.role === OrganizationRole.ORG_ADMIN ? (
                                        <Tag text="Admin" className="bg-cyan-600/10 text-cyan-600" size="small" />
                                      ) : (
                                        <span className="font-ilabel text-gray block truncate text-xs">Member</span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            }
                          >
                            <UserIcon key={i} src={member.user?.picture} username={member.user?.username} size="md" />
                          </PopoverTooltip>
                        ))
                      )}
                      <a
                        className="text-icontent text-primary-600 absolute bottom-0 right-4 hidden group-hover:block"
                        href={`/organizations/${owner}/members`}
                      >
                        View more
                      </a>
                    </div>
                  </div>
                  <div className="border-border-color group relative mb-4 border-t pb-8 pt-4 sm:mb-16">
                    <div className="flex w-full items-center justify-between">
                      <h3 className="text-text-foreground mb-3 text-lg font-medium">Plan & Usage</h3>
                    </div>
                    <div className="mt-1 inline-flex items-center gap-2">
                      <span className="text-sm">Current plan:</span>
                      <span className="text-primary-600 text-sm font-semibold">{plan.name}</span>
                    </div>
                    <div className="mt-1">
                      <span className="text-sm">
                        Sentio units:
                        <span className="ml-2 font-medium text-gray-800">
                          {totalUsage.toLocaleString()} /{' '}
                          {plan?.unitCap ? BigDecimal(plan.unitCap).toFormat(0) : 'Unlimited'}
                        </span>
                      </span>
                    </div>
                    <a
                      className="text-icontent text-primary-600 absolute bottom-0 right-4 hidden group-hover:block"
                      href={`/organizations/${owner}/billing`}
                    >
                      View more
                    </a>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>
      <ProjectSlideOver
        open={openCreateSider}
        onClose={() => {
          setOpenCreateSider(false)
        }}
        user={user}
        allowAdmin={allowAdmin}
        onCancel={() => {
          setOpenCreateSider(false)
        }}
        defaultValue={editProject}
        createFrom={createProjectFrom}
        onDelete={() => setDeleting(true)}
      />
      <ProjectDeleteConfirm
        projectName={getProjectUrl(editProject)}
        title={'Delete project'}
        open={deleting}
        onClose={(show) => setDeleting(show)}
        onConfirm={() => _deleteProject()}
      />
      <CloneProject
        open={cloneProject != null}
        user={user}
        allowAdmin={allowAdmin}
        onCancel={() => {
          setCloneProject(undefined)
        }}
        project={cloneProject}
        onClose={() => setCloneProject(undefined)}
      />
    </Layout>
  )
}

export default OrgHomePage
