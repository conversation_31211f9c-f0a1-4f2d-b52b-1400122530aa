import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  forwardRef,
  useImperativeHandle,
  useContext,
  useMemo
} from 'react'
import router from 'next/router'
import { Project } from '../../gen/service/common'
import { useProjectTables, useQuerySql, useSchema } from 'lib/data/use-sql'
import {
  ChatMessage,
  convertApiChartTypeToChartType,
  convertChatMessageToApiMessage,
  parseSQLMessage,
  SessionStatus,
  useSQLAgentAsync,
  useSuggestReply
} from 'lib/data/use-ai-agent-async'
import { ContextScenario, HistoryChat, Message, MessageRole, Resource } from '../../gen/service/ai'
import { GlobalAIGray } from '../sql/FileIcons'
import { ChartChartType } from '../../gen/service/web'
import classNames from 'lib/classnames'
import { QueryResult } from './query-result/QueryResult'
import ScaleLoader from 'react-spinners/ScaleLoader'
import { DashModeContext } from 'lib/dash/context'
import UserMessage from './messages/User'
import AssistantMessage from './messages/Assistant'
import PromptCard from './PromptCard'
import useUser from 'lib/data/use-user'
import { isEqual, isString } from 'lodash'
import { generateUUID } from 'lib/util/uuid'
import ChatInput from './chat-input/ChatInput'
import { ChatContext } from './ChatContext'
import { TableTableType } from '@sentio/service/analytic'
import ToolMessage from './messages/Tool'
import { CommunityFlight, useFlight } from 'lib/data/use-flight'
import { useInsightsCall } from 'lib/data/use-insights'
import MetricChart from 'components/charts/Chart'
import { InsightResult } from './query-result/InsightResult'
import SystemMessage from './messages/System'

function generateErrorMessage(reason?: string, code?: string): ChatMessage {
  return {
    role: MessageRole.ROLE_SYSTEM,
    structured: {
      error: {
        code: code || 'GENERAL_ERROR',
        message: reason || 'Sorry, I encountered an error. Please try again.'
      }
    }
  }
}

interface Props {
  project?: Project
  scenario?: ContextScenario
  onComplete: (sql: string, name?: string, chartType?: ChartChartType, messages?: Message[]) => void
  onClose: () => void
  chatId?: string
  saveHistory?: (messages?: ChatMessage[]) => void
  sqlFileId?: number // patch to existing SQL query
}

export interface AgentModeRef {
  newChat: (initMessage?: string) => void
  selectChat: (chat: HistoryChat) => void
}

const pageSize = 10

export const AgentModeAsync = forwardRef<AgentModeRef, Props>(function AgentMode(
  { project, scenario, onComplete, chatId, saveHistory, sqlFileId, onClose },
  ref
) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const messagesRef = useRef(messages)
  messagesRef.current = messages
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const setInput = useCallback((v: string) => {
    if (inputRef.current) {
      inputRef.current.value = v
    }
  }, [])
  const [isTyping, setIsTyping] = useState(false)
  const [showScrollButton, setShowScrollButton] = useState(false)
  const {
    sqlAgentChatAsync,
    loading: aiLoading,
    cancel,
    status,
    error,
    sessionId,
    response
  } = useSQLAgentAsync(project?.ownerName, project?.slug, scenario)
  const isPulling = status === SessionStatus.CREATING || status === SessionStatus.POLLING
  const { querySql, loading: queryLoading } = useQuerySql(project)
  const { queryInsight, loading: insightsLoading } = useInsightsCall(project)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const introRef = useRef<HTMLDivElement>(null)
  const [expandedDatasets, setExpandedDatasets] = useState<number[]>([])
  const [currentPages, setCurrentPages] = useState<{ [key: number]: number }>({})
  const { suggestions } = useSuggestReply(messages?.slice(-1)[0])
  const chatContext = useContext(ChatContext)
  const chatContextRef = useRef(chatContext)
  chatContextRef.current = chatContext
  const isDashMode = useContext(DashModeContext)
  const { isFlightEnabled } = useFlight()
  const showCommunity = isFlightEnabled(CommunityFlight)

  const tables = useProjectTables(project)
  const projectTables = useMemo(() => {
    const res: string[] = []
    tables.forEach((t) => {
      if (isDashMode && showCommunity) {
        if (
          t.name &&
          t.tableType &&
          [
            TableTableType.DASH_CURATED_ENTITY,
            TableTableType.DASH_CURATED_EVENT,
            TableTableType.DASH_CURATED_SUBGRAPH
          ].includes(t.tableType)
        ) {
          res.push(t.name)
        }
        return
      } else if (
        t.name &&
        t.tableType &&
        [TableTableType.EVENT, TableTableType.METRICS, TableTableType.SUBGRAPH].includes(t.tableType)
      ) {
        res.push(t.name)
      }
    })
    return res
  }, [tables, isDashMode, showCommunity])
  let examplePrompt = ''
  if (projectTables.length > 0) {
    const table = projectTables[Math.floor(Math.random() * projectTables.length)]
    examplePrompt = `Give some insights about ${table} table`
  } else {
    examplePrompt = 'Analyze gas prices on Ethereum over time and show in a bar chart'
  }

  const skipUpdateHistoryRef = useRef(false)
  useEffect(() => {
    if (messages.length > 0 && messages[messages.length - 1].isFinal) {
      if (skipUpdateHistoryRef.current) {
        skipUpdateHistoryRef.current = false
        return
      }
      saveHistory?.(messages)
    }
  }, [messages])

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
    setShowScrollButton(false)
  }, [])

  const preMessagesRef = useRef<any>(null)
  useEffect(() => {
    preMessagesRef.current = null
  }, [chatId])
  useEffect(() => {
    // Only scroll to bottom when there are actual messages
    if (messages.length > 0) {
      if (!preMessagesRef.current || messages.length !== preMessagesRef.current.length) {
        scrollToBottom()
        preMessagesRef.current = messages
      }
    } else {
      const container = document.querySelector('.agent-messages-container')
      if (container) {
        container.scrollTop = 0
      }
    }
  }, [messages])

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const container = e.currentTarget
      if (!container) return
      const { scrollTop, scrollHeight, clientHeight } = container as HTMLElement
      // Show the scroll-to-bottom button when the distance to the bottom is greater than 200px
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 200
      setShowScrollButton(!isNearBottom && messages.length > 0)
    },
    [messages.length]
  )

  useEffect(() => {
    inputRef.current?.focus()
  }, [chatId])

  const toggleSqlVisibility = useCallback((index: number) => {
    setMessages((prev) => {
      const updated = [...prev]
      updated[index] = {
        ...updated[index],
        showSql: !updated[index].showSql
      }
      return updated
    })
  }, [])

  const handleSubmit = useCallback(
    async (
      e?: React.FormEvent,
      forceSubmit?: boolean,
      inputOverride?: string,
      scenario?: ContextScenario,
      resources?: Resource[]
    ) => {
      e?.preventDefault()

      // Use inputOverride if provided, otherwise use the input state
      const currentInput = inputOverride || inputRef.current?.value || ''

      if ((!currentInput.trim() || aiLoading) && !forceSubmit) return

      const userMessage: Message = {
        role: MessageRole.ROLE_USER,
        text: currentInput
      }

      if (resources) {
        userMessage.resources = resources
      }

      if (editingIndex !== null) {
        // If we're in editing mode, replace the message at editingIndex and remove all subsequent messages
        setMessages((prev) => {
          const updatedMessages = prev.slice(0, editingIndex)
          updatedMessages.push(userMessage)
          return updatedMessages
        })
        setEditingIndex(null) // Exit editing mode
      } else {
        // Normal mode - just append the message
        setMessages((prev) => [...prev, userMessage])
      }

      setInput('')
      setIsTyping(true)
      try {
        const messages = messagesRef.current
        const updatedMessages = editingIndex !== null ? messages.slice(0, editingIndex) : messages

        await sqlAgentChatAsync(currentInput, updatedMessages, scenario, resources)
      } catch (error) {
        const errorMessage: Message = generateErrorMessage()
        setMessages((prev) => [...prev, errorMessage])
      } finally {
        setIsTyping(false)
      }
    },
    [aiLoading, editingIndex, sqlAgentChatAsync, setInput]
  )

  const executeQuery = useCallback(
    (msg: ChatMessage) => {
      const sql = msg.structured?.sql?.query
      if (sql) {
        setMessages((prev) => {
          const target = prev.find((m) => m.uuid === msg.uuid)
          if (target) {
            target.isExecuting = true
          }
          return [...prev]
        })
        querySql(sql, 100)
          .then((result) => {
            if (result.error) {
              throw new Error(result.error)
            }

            if (!result.result) {
              throw new Error('No results returned by the query')
            }
            setMessages((prev) => {
              const target = prev.find((m) => m.uuid === msg.uuid)
              if (target) {
                target.isExecuting = false
                target.queryResults = result.result
              }
              return [...prev]
            })
          })
          .catch((error) => {
            setMessages((prev) => {
              const target = prev.find((m) => m.uuid === msg.uuid)
              if (target) {
                target.isExecuting = false
                target.queryError = error instanceof Error ? error.message : error?.body?.message || String(error)
              }
              return [...prev]
            })
          })
      }

      if (msg.structured?.insightQuery) {
        setMessages((prev) => {
          const target = prev.find((m) => m.uuid === msg.uuid)
          if (target) {
            target.isExecuting = true
          }
          return [...prev]
        })
        queryInsight(msg.structured.insightQuery)
          .then((result) => {
            setMessages((prev) => {
              const target = prev.find((m) => m.uuid === msg.uuid)
              if (target) {
                target.isExecuting = false
                target.queryResults = result
              }
              return [...prev]
            })
          })
          .catch((error) => {
            setMessages((prev) => {
              const target = prev.find((m) => m.uuid === msg.uuid)
              if (target) {
                target.isExecuting = false
                target.queryError = error instanceof Error ? error.message : error?.body?.message || String(error)
              }
              return [...prev]
            })
          })
      }
    },
    [querySql]
  )

  const sessionIdRef = useRef(sessionId)
  const responseRef = useRef<any>(null)
  useEffect(() => {
    const prevSessionId = sessionIdRef.current
    const newChatMessages: ChatMessage[] = []
    const pendingExecuteMessages: ChatMessage[] = []
    let shouldQuery = false

    if (responseRef.current && isEqual(responseRef.current, response)) {
      return
    }

    response?.forEach((message, index) => {
      const lastMessage = newChatMessages.length > 0 ? newChatMessages[newChatMessages.length - 1] : null
      const shouldMerge =
        lastMessage &&
        lastMessage.role &&
        [MessageRole.ROLE_ASSISTANT, MessageRole.AI_ROLE_TOOL].includes(lastMessage.role) &&
        lastMessage.role === message.role &&
        isString(lastMessage.text) &&
        isString(message.text)
      if (shouldMerge) {
        if (lastMessage.role === MessageRole.AI_ROLE_TOOL) {
          Object.assign(lastMessage, message)
        } else if (lastMessage.role === MessageRole.ROLE_ASSISTANT) {
          Object.assign(lastMessage, {
            ...message,
            text: (lastMessage.text || '') + message.text
          })
        }
      } else {
        const newMessage = {
          ...message,
          sessionId: sessionId!,
          uuid: generateUUID(16),
          showSql: Boolean(chatContextRef.current?.originalSQL)
        } as ChatMessage
        newChatMessages.push(newMessage)
        if (message.structured?.sql || message.structured?.insightQuery) {
          pendingExecuteMessages.push(newMessage)
        }
      }
      if (index === response.length - 1) {
        shouldQuery = Boolean(message.isFinal)
      }
    })

    if (prevSessionId !== sessionId) {
      sessionIdRef.current = sessionId
      setMessages((prev) => {
        return [...prev, ...newChatMessages]
      })
    } else {
      if (!newChatMessages.length || isEqual(responseRef.current, response)) {
        //ignore
      } else {
        setMessages((prev) => {
          const filteredPrev = prev.filter((msg) => msg.sessionId !== prevSessionId)
          return [...filteredPrev, ...newChatMessages]
        })
      }
    }
    if (shouldQuery) {
      pendingExecuteMessages.forEach(executeQuery)
    }
    responseRef.current = response
  }, [response, sessionId])
  useEffect(() => {
    if (!error) {
      return
    }
    setMessages((prev) => {
      return [...prev, generateErrorMessage(error)]
    })
  }, [error])

  const renderQueryResult = useCallback(
    (message: ChatMessage, index: number) => {
      const isExpanded = expandedDatasets.includes(index)
      const currentPage = currentPages[index] || 1
      const toggleDatasetView = (index: number) => {
        setExpandedDatasets((prev) => {
          if (prev.includes(index)) {
            return prev.filter((i) => i !== index)
          } else {
            return [...prev, index]
          }
        })
      }

      const changePage = (index: number, newPage: number) => {
        setCurrentPages((prev) => ({
          ...prev,
          [index]: newPage
        }))
      }

      return (
        <QueryResult
          message={message}
          index={index}
          currentPage={currentPage}
          pageSize={pageSize}
          isExpanded={isExpanded}
          onPageChange={changePage}
          onToggleExpand={toggleDatasetView}
          onFixQuery={() => {
            const correctionText = 'correct the previous failing query'
            setInput(correctionText)
            handleSubmit(undefined, true, correctionText)
          }}
        />
      )
    },
    [expandedDatasets, currentPages]
  )

  const renderInsightResult = useCallback((message: ChatMessage) => {
    if (message.queryResults) {
      return (
        <InsightResult
          data={message.queryResults}
          error={message.queryError}
          title={message.structured?.insightQuery?.title}
          chartType={message.structured?.insightQuery?.chartType}
        />
      )
    }
  }, [])

  // Function to send a query to the editor without closing the chat
  const sendQueryToEditor = useCallback(
    (index: number, overrideSQL?: string) => {
      if (!messagesRef.current) return
      const message = messagesRef.current[index]
      const { query, title, chartType } = parseSQLMessage(message)
      const sql = overrideSQL || query
      if (sql) {
        const currentPath = router.pathname
        const expectedPath = isDashMode
          ? router.query.owner && router.query.slug
            ? `/${router.query.owner}/${router.query.slug}/dash/sql`
            : '/sql'
          : `/[owner]/[slug]/data-explorer/sql`

        if (currentPath === expectedPath || currentPath === '/[owner]/[slug]/dashboards/[id]/panels/[panel]') {
          onComplete(sql, title, chartType, messages)
          return
        }

        // Navigate to the appropriate SQL Editor tab with query parameters
        const routeConfig = isDashMode
          ? router.query.owner && router.query.slug
            ? {
                pathname: `/${router.query.owner}/${router.query.slug}/dash/sql`,
                query: {
                  owner: router.query.owner,
                  slug: router.query.slug,
                  query: sql,
                  title: title,
                  chartType: chartType,
                  fileId: sqlFileId
                }
              }
            : {
                pathname: '/sql',
                query: {
                  query: sql,
                  title: title,
                  chartType: chartType,
                  fileId: sqlFileId
                }
              }
          : {
              pathname: `/[owner]/[slug]/data-explorer/sql`,
              query: {
                owner: router.query.owner,
                slug: router.query.slug,
                query: sql,
                title: title,
                chartType: chartType,
                fileId: sqlFileId
              }
            }

        router.push(routeConfig)
        onClose()
      }
    },
    [onComplete, isDashMode, onClose]
  )

  const { user } = useUser()
  // Introduction panel to explain what the SQL agent can and cannot do
  const renderIntroduction = () => {
    return (
      <div ref={introRef} className="flex h-full flex-col items-center justify-between p-4">
        <div className="flex-1">
          <div className="flex flex-col gap-8 pt-32">
            <div className={classNames('space-y-2', 'fade-container')}>
              <h2 className="bg-gradient-to-br from-[#BD24B5] via-[#0756D5] to-[#36F7F7] bg-clip-text text-3xl font-semibold text-transparent dark:from-[#0756D5] dark:via-[#BD24B5] dark:to-[#36F7F7]">
                Hello {user ? user.username : ''},
              </h2>
              <h2 className="text-3xl font-semibold text-gray-500">How can I help you analyze blockchain data?</h2>
            </div>

            <div className="mt-8 grid max-w-4xl grid-cols-1 gap-4 sm:grid-cols-2 xl:grid-cols-4">
              <PromptCard
                text={examplePrompt}
                value={examplePrompt}
                onClick={(question: string) => {
                  setInput(question)
                  handleSubmit(undefined, true, question)
                }}
                highlight={projectTables.length > 0}
              />
              <PromptCard
                text="Show me daily transaction counts on Ethereum over the last month"
                value="Show me daily transaction counts on Ethereum over the last month"
                onClick={(question: string) => {
                  setInput(question)
                  handleSubmit(undefined, true, question)
                }}
              />
              <PromptCard
                text="Compare Aptos and Sui transaction volume trends in a line chart"
                value="Compare Aptos and Sui transaction volume trends in a line chart"
                onClick={(question: string) => {
                  setInput(question)
                  handleSubmit(undefined, true, question)
                }}
              />
              <PromptCard
                text="Find transactions with unusually high values on Bitcoin last month"
                value="Find transactions with unusually high values on Bitcoin last month"
                onClick={(question: string) => {
                  setInput(question)
                  handleSubmit(undefined, true, question)
                }}
              />
            </div>
          </div>
        </div>

        <p className="mt-8 text-xs text-gray-600">AI can make mistakes. Check important info.</p>
      </div>
    )
  }

  useEffect(() => {
    // Ensure introduction panel is visible when it's rendered
    if (messages.length === 0 && introRef.current) {
      introRef.current.scrollIntoView({ block: 'start' })
    }
  }, [messages.length])

  const selectChat = useCallback(
    (chat: HistoryChat) => {
      if (chat?.messages) {
        cancel()
        setInput('')
        setMessages(chat.messages)
        // executeQuery executing queries
        chat.messages.forEach((message) => {
          const data = message as ChatMessage
          if (data.isExecuting && (data.structured?.sql?.query || data.structured?.insightQuery)) {
            executeQuery(data)
          }
        })
        skipUpdateHistoryRef.current = true
      }
    },
    [executeQuery, setInput]
  )

  const newChat = useCallback(
    (msg?: string) => {
      cancel()
      setMessages([])
      setInput(msg || '')
      if (msg) {
        setTimeout(() => {
          handleSubmit(undefined, true, msg)
        }, 0)
      }
    },
    [handleSubmit, setInput]
  )

  useImperativeHandle(
    ref,
    () => ({
      newChat,
      selectChat
    }),
    [newChat, selectChat]
  )

  const isAnyDataExecuting = useMemo(() => {
    return messages.some((message) => message.isExecuting)
  }, [messages])

  const onEditMessage = useCallback((index: number) => {
    const message = messagesRef.current?.[index]
    if (message && message.text) {
      setInput(message.text)
      setEditingIndex(index)
    }
  }, [])

  const executeQueryInMessage = useCallback(
    (sql: string) => {
      return handleSubmit(undefined, false, sql)
    },
    [handleSubmit]
  )

  const lastMessageMode = useMemo(() => {
    if (messages.length === 0) {
      return 'unknown'
    }
    const lastMessage = messages[messages.length - 1]
    const lastToolMessage = messages.findLast((msg) => msg.role === MessageRole.AI_ROLE_TOOL)
    if (lastMessage.structured?.sql) {
      return 'sql'
    } else if (lastMessage.structured?.insightQuery) {
      return 'insight'
    } else {
      const matchRegx = /Routing to (sql|insight) agent/
      const match = lastToolMessage?.text?.match(matchRegx)
      if (match) {
        return match[1] as 'sql' | 'insight'
      } else {
        return 'unknown'
      }
    }
  }, [messages])

  const lastMessageResources = useMemo(() => {
    if (messages.length === 0) {
      return []
    }
    const lastMessage = messages[messages.length - 1]
    if (lastMessage.role === MessageRole.ROLE_USER) {
      return []
    }
    if (lastMessage.role === MessageRole.ROLE_ASSISTANT) {
      return lastMessage.resources || []
    }
  }, [messages])

  return (
    <>
      <div
        className="agent-messages-container relative w-full flex-1 overflow-y-auto overscroll-contain pt-14"
        key={chatId}
        onScroll={handleScroll}
      >
        <div className="mx-auto h-full max-w-4xl space-y-6 px-4 sm:px-0">
          {messages.length === 0 && !chatId
            ? renderIntroduction()
            : messages.map((message, index) => {
                // Only render messages up to the editing index if in editing mode
                if (editingIndex !== null && index > editingIndex) return null
                switch (message.role) {
                  case MessageRole.ROLE_USER:
                    return (
                      <UserMessage
                        key={index}
                        index={index}
                        message={message}
                        sendQueryToEditor={sendQueryToEditor}
                        handleSubmit={handleSubmit}
                        setInput={setInput}
                        onEdit={onEditMessage}
                      />
                    )
                  case MessageRole.ROLE_ASSISTANT:
                    return (
                      <AssistantMessage
                        key={index}
                        index={index}
                        message={message}
                        project={project}
                        isDashMode={isDashMode}
                        toggleSqlVisibility={toggleSqlVisibility}
                        sendQueryToEditor={sendQueryToEditor}
                        renderQueryResult={renderQueryResult}
                        renderInsightResult={renderInsightResult}
                        executeQuery={executeQueryInMessage}
                        isStartMessage={index > 0 && messages[index - 1].role === MessageRole.ROLE_USER}
                      />
                    )
                  case MessageRole.AI_ROLE_TOOL:
                    return (
                      <ToolMessage
                        message={message}
                        key={index}
                        isStartMessage={index > 0 && messages[index - 1].role === MessageRole.ROLE_USER}
                        isLastMessage={index === messages.length - 1}
                        isPolling={isPulling && index === messages.length - 1}
                      />
                    )
                  case MessageRole.ROLE_SYSTEM:
                    return <SystemMessage key={index} message={message} />
                  default:
                    return null
                }
              })}
          {(isTyping || isPulling) &&
            messages.length > 0 &&
            messages[messages.length - 1].role === MessageRole.ROLE_USER && (
              <div className="py-4">
                <div className="flex items-center gap-4 text-gray-500">
                  <div className="rounded-md border p-1">
                    <GlobalAIGray className="text-primary-500 dark:text-primary-700 h-5 w-5" />
                  </div>
                  <div className="inline-flex items-center gap-2">
                    <ScaleLoader color="#2e71db" height={20} speedMultiplier={0.8} />
                    <span className="text-sm text-gray-500">Thinking...</span>
                  </div>
                </div>
              </div>
            )}
          <div
            className={messages.length === 0 ? '' : isPulling ? 'h-[calc(100vh-400px)]' : 'h-2'}
            ref={messagesEndRef}
          />
        </div>
      </div>
      <ChatInput
        onSubmit={handleSubmit}
        onCancel={cancel}
        editingIndex={editingIndex}
        setEditingIndex={setEditingIndex}
        running={aiLoading || queryLoading || insightsLoading}
        ref={inputRef}
        suggestions={suggestions}
        isExecuting={isAnyDataExecuting}
        showScrollBottom={showScrollButton}
        scrollToBottom={scrollToBottom}
        lastMessageMode={lastMessageMode}
        lastMessageResources={lastMessageResources}
      />
    </>
  )
})
