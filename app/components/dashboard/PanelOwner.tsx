import useApi from 'lib/data/use-api'
import { WebService, ProjectOwnerAndSlug } from '@sentio/service/web'
import { ImgWithFallback } from 'components/user/ImgWithFallback'
import { useUserInfo } from 'lib/data/use-user'
import { useContext } from 'react'
import { DashModeContext } from 'lib/dash/context'
import type { UserInfo as UserInfoType } from '@sentio/service/common'
import { PopoverTooltip } from 'components/common/tooltip/DivTooltip'

interface Props {
  projectOwner?: string
  projectSlug?: string
  ownerId?: string
  creator?: UserInfoType
  updater?: UserInfoType
}

const USER_CONTAINER_CLASSES = 'inline-flex items-center gap-1 px-2 text-xs cursor-pointer'
const AVATAR_CLASSES = 'h-3 w-3 rounded-full'
const TOOLTIP_CLASSES = 'flex items-center gap-2'
const LABEL_CLASSES = 'text-xs font-semibold text-gray-600 dark:text-gray-400'

const UserInfo = ({
  avatarSrc,
  avatarAlt,
  username,
  containerClassName = USER_CONTAINER_CLASSES
}: {
  avatarSrc?: string
  avatarAlt?: string
  username?: string
  containerClassName?: string
}) => (
  <div className={containerClassName}>
    {avatarSrc && <ImgWithFallback className={AVATAR_CLASSES} src={avatarSrc} alt={avatarAlt || username || ''} />}
    <span className="max-w-[120px] truncate">{username}</span>
  </div>
)

const TooltipUserDisplay = ({ user, label }: { user: UserInfoType; label: string }) => (
  <div className="flex flex-col gap-1 p-1">
    <div className={LABEL_CLASSES}>{label}</div>
    <div className={TOOLTIP_CLASSES}>
      <ImgWithFallback className="h-4 w-4 rounded-full" src={user.picture} alt={user.username} />
      <span className="text-xs font-medium">
        {user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.username}
      </span>
    </div>
  </div>
)

const TooltipBothUsersDisplay = ({ creator, updater }: { creator: UserInfoType; updater: UserInfoType }) => (
  <div className="space-y-3 p-1">
    <TooltipUserDisplay user={creator} label="Created by" />
    <TooltipUserDisplay user={updater} label="Last updated by" />
  </div>
)

const UserWithTooltip = ({
  mainUser,
  tooltipUser,
  tooltipLabel
}: {
  mainUser: UserInfoType
  tooltipUser: UserInfoType
  tooltipLabel: string
}) => (
  <PopoverTooltip
    text={<TooltipUserDisplay user={tooltipUser} label={tooltipLabel} />}
    maxWidth="max-w-[240px]"
    offsetOptions={10}
  >
    <UserInfo avatarSrc={mainUser.picture} avatarAlt={mainUser.username} username={mainUser.username} />
  </PopoverTooltip>
)

const UserWithBothTooltip = ({
  mainUser,
  creator,
  updater
}: {
  mainUser: UserInfoType
  creator: UserInfoType
  updater: UserInfoType
}) => (
  <PopoverTooltip
    text={<TooltipBothUsersDisplay creator={creator} updater={updater} />}
    maxWidth="max-w-[280px]"
    offsetOptions={10}
  >
    <UserInfo avatarSrc={mainUser.picture} avatarAlt={mainUser.username} username={mainUser.username} />
  </PopoverTooltip>
)

export const PanelOwner = ({ projectOwner, projectSlug, ownerId: owner, creator, updater }: Props) => {
  if (!creator && !updater) {
    return <ProjectOwner projectOwner={projectOwner} projectSlug={projectSlug} ownerId={owner} />
  }

  if (creator && updater) {
    return <UserWithBothTooltip mainUser={updater} creator={creator} updater={updater} />
  }

  if (creator) {
    return <UserWithTooltip mainUser={creator} tooltipUser={creator} tooltipLabel="Created by" />
  }

  if (updater) {
    return <UserWithTooltip mainUser={updater} tooltipUser={updater} tooltipLabel="Last updated by" />
  }

  return null
}

export const ProjectOwner = ({ projectOwner, projectSlug, ownerId: owner }: Props) => {
  const isDashMode = useContext(DashModeContext)
  const { data: user } = useUserInfo(isDashMode && owner ? { userId: owner } : null, { onError: () => {} })
  const { data } = useApi(
    WebService.GetProject,
    projectOwner && projectSlug ? ({ ownerName: projectOwner, slug: projectSlug } as ProjectOwnerAndSlug) : undefined,
    undefined,
    {
      onError: () => {},
      errorRetryCount: 0,
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false
    }
  )

  if (isDashMode) {
    return <UserInfo avatarSrc={user?.picture} avatarAlt={user?.username} username={user?.username} />
  }

  const ownerName = data?.project?.ownerName || projectOwner
  let avatarUrl: string | undefined = ''

  if (data?.project?.owner?.organization) {
    avatarUrl = data.project.owner.organization.logoUrl
  } else if (data?.project?.owner?.user) {
    avatarUrl = data.project.owner.user.picture
  }

  return <UserInfo avatarSrc={avatarUrl} avatarAlt={ownerName} username={ownerName} />
}
