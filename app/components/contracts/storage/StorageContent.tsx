import { StorageViewer } from './StorageViewer'
import { StorageValueViewer } from './StorageValueViewer'
import { StorageSummary } from './StorageSummary'
import { GetStorageSummaryResponse } from '@sentio/service/solidity/protos/service.pb'
import { useContext, useMemo } from 'react'
import { StorageStateVariableType } from 'lib/debug/types'
import { ContractTitle, ContractVariableTitle } from './StorageTitle'
import { ChainIdContext } from 'lib/debug/context'
import { StorageHistory } from './StorageHistory'

interface Props {
  data: GetStorageSummaryResponse
  onClick?: (name: string) => void
  mutate?: () => Promise<void>
  currentPath: string | null | undefined
  isVariable: boolean
}

export const StorageContent = ({ data, onClick, mutate, isVariable, currentPath }: Props) => {
  const summaryData = useMemo(() => {
    const list: StorageStateVariableType[] = []
    data?.results?.forEach((item) => {
      if (item.stateVariables) {
        list.push(...item.stateVariables)
      }
    })
    return list[0]
  }, [data])

  const chainId = useContext(ChainIdContext)

  if (isVariable) {
    return (
      <>
        <ContractVariableTitle
          title={summaryData?.type.name}
          address={data?.address}
          chainId={chainId}
          blockNumber={(data as any)?.blockNumber}
          refresh={mutate}
        />
        {data?.address ? <StorageSummary address={data.address} data={summaryData} /> : null}
        {data?.address ? (
          <StorageHistory data={summaryData} address={data.address} currentPath={currentPath} onClick={onClick} />
        ) : null}
        <StorageValueViewer data={data} onClick={onClick} />
      </>
    )
  }

  return (
    <>
      <ContractTitle
        chainId={chainId}
        address={data?.address}
        proxy={data?.implAddress}
        blockNumber={(data as any)?.blockNumber}
        refresh={mutate}
      />
      <StorageViewer data={data} onClick={onClick} />
    </>
  )
}
