import { ChevronDownIcon, ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/20/solid'
import { OverCapLimitRequest, BillingService } from '@sentio/service/billing/protos/service.pb'
import NewButton from 'components/common/buttons/NewButton'
import { PopupMenuButton } from 'components/menu/PopupMenuButton'
import classNames from 'lib/classnames'
import { useBillingWarning } from 'lib/data/use-billing'
import { OverLimitFlight, useFlightWith } from 'lib/data/use-flight'
import { NotificationContext } from 'lib/data/use-notification'
import withJsonApi from 'lib/data/with-json-api'
import { money } from 'lib/money'
import { isNumber } from 'lodash'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useRef, useState, useContext, useEffect } from 'react'

export function OverQuotaLimitInput({
  limit: _limit,
  onChange,
  submit
}: {
  limit?: string
  onChange?: (limit?: string) => void
  submit?: () => void
}) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [limit, setLimit] = useState(_limit)
  const [isValid, setValid] = useState(true)
  useEffect(() => {
    setLimit((v) => v || _limit)
  }, [_limit])
  useEffect(() => {
    const value = limit
    if (value === '' || (value && isNumber(Number(value)) && Number(value) >= 0)) {
      setValid(true)
      onChange?.(value)
      return
    }
    setValid(false)
  }, [limit])
  return (
    <div
      className={classNames(
        ' flex h-[30px] items-center gap-1 rounded-md border focus-within:ring-1 focus-within:ring-opacity-50',
        isValid
          ? 'focus-within:border-primary-600 focus-within:ring-primary-600 border-gray-200'
          : 'border-red-500 focus-within:border-red-500 focus-within:ring-red-500'
      )}
    >
      <div className="text-icontent flex h-full items-center gap-1 border-r border-gray-200 px-2 font-medium">
        <PopupMenuButton
          selectedKey={limit ? 'limit' : 'no-limit'}
          onSelect={(v: string) => {
            if (v === 'no-limit') {
              setLimit('')
            } else {
              setLimit((v) => v || '10')
              inputRef.current?.focus()
            }
          }}
          items={[
            [
              {
                label: 'Limit',
                key: 'limit'
              },
              {
                label: 'No Limit',
                key: 'no-limit'
              }
            ]
          ]}
          buttonIcon={
            <span className="inline-flex w-20 items-center justify-between">
              {!limit ? 'No Limit' : 'Limit'}
              <ChevronDownIcon className="h-4 w-4" />
            </span>
          }
        ></PopupMenuButton>
      </div>
      <input
        className="text-icontent w-full border-none outline-transparent focus:ring-transparent disabled:bg-gray-100/50"
        placeholder="Current no limit, provide a limit to set"
        value={limit}
        type="number"
        min="0"
        step={1}
        onChange={(evt) => {
          const value = evt.target.value
          setLimit(value)
        }}
        onKeyDown={(evt) => {
          if (evt.key === 'Enter') {
            evt.preventDefault()
            submit?.()
          }
        }}
        ref={inputRef}
      />
      <div className="text-icontent p-2 font-medium">Dollars</div>
    </div>
  )
}

export function OverQuotaLimit({ accountId, limit }: { accountId?: string; limit?: string }) {
  const inputRef = useRef<HTMLInputElement>(null)
  const btnRef = useRef<HTMLButtonElement>(null)
  const [hasLimit, setHasLimit] = useState(true)
  const [saving, setSaving] = useState(false)
  const { showNotification } = useContext(NotificationContext)
  const [isValid, setValid] = useState(true)
  useEffect(() => {
    if (limit === undefined) {
      return
    }
    if (limit) {
      setHasLimit(true)
      if (inputRef.current) {
        inputRef.current.value = limit
      }
    } else {
      setHasLimit(false)
      if (inputRef.current) {
        inputRef.current.value = ''
      }
    }
  }, [limit])
  return (
    <div className="space-y-4">
      <div className="text-base font-bold">
        <label className="text-gray-900">Set a max auto-scaling spend limit</label>
      </div>
      <div className="flex items-center gap-2">
        <div
          className={classNames(
            ' flex h-[30px] items-center gap-1 rounded-md border focus-within:ring-1 focus-within:ring-opacity-50',
            isValid
              ? 'focus-within:border-primary-600 focus-within:ring-primary-600 border-gray-200'
              : 'border-red-500 focus-within:border-red-500 focus-within:ring-red-500'
          )}
        >
          <div className="text-icontent flex h-full items-center gap-1 border-r border-gray-200 px-2 font-medium">
            <PopupMenuButton
              onSelect={(v: string) => {
                setHasLimit(v === 'limit')
                if (v === 'no-limit') {
                  setValid(true)
                }
              }}
              items={[
                [
                  {
                    label: 'Limit',
                    key: 'limit'
                  },
                  {
                    label: 'No Limit',
                    key: 'no-limit'
                  }
                ]
              ]}
              buttonIcon={
                <span className="inline-flex items-center gap-1">
                  {hasLimit ? 'Limit' : 'No Limit'}
                  <ChevronDownIcon className="h-4 w-4" />
                </span>
              }
            ></PopupMenuButton>
          </div>
          <div className="relative flex flex-1 items-center">
            <input
              ref={inputRef}
              className={classNames(
                'text-icontent w-20 outline-transparent disabled:bg-gray-100/50 sm:w-40',
                hasLimit ? '' : 'invisible'
              )}
              disabled={!hasLimit}
              placeholder={hasLimit ? 'Enter your limit' : 'No limit'}
              onChange={(evt) => {
                const value = evt.target.value
                if (value && !money(value).isZero()) {
                  setValid(true)
                  return
                }
                setValid(false)
              }}
              onKeyDown={(evt) => {
                if (evt.key === 'Enter') {
                  evt.preventDefault()
                  btnRef.current?.click()
                }
              }}
            />
            {hasLimit ? (
              <div className="text-icontent p-2 font-medium">Dollars</div>
            ) : (
              <div className="text-icontent absolute inset-0 flex items-center px-1">/</div>
            )}
          </div>
        </div>
        <NewButton
          size="sm"
          role="primary"
          processing={saving}
          disabled={!isValid}
          ref={btnRef}
          onClick={() => {
            setSaving(true)
            let request: OverCapLimitRequest
            if (hasLimit) {
              request = {
                accountId,
                limit: money(inputRef.current?.value || '0'),
                noLimit: false
              }
            } else {
              request = {
                accountId,
                limit: money('0'),
                noLimit: true
              }
            }
            withJsonApi(BillingService.SetAccountOverCapLimit)(request)
              .then(
                () => {
                  showNotification(
                    {
                      message: 'Auto-scaling limit updated successfully',
                      type: 'success',
                      title: 'Success'
                    },
                    3
                  )
                },
                (e) => {
                  showNotification(
                    {
                      message: e?.body?.message || 'Failed to update auto-scaling limit',
                      type: 'error',
                      title: 'Error'
                    },
                    3
                  )
                }
              )
              .finally(() => {
                setSaving(false)
              })
          }}
        >
          Save
        </NewButton>
      </div>
    </div>
  )
}

export function OverQuotaLimitAlert() {
  const { query } = useRouter()
  const { isOverLimit, overLimitLink, isDismissed, dismissAlert, overLimit } = useBillingWarning(
    undefined,
    query?.owner as string,
    query?.slug as string
  )
  const isEnabled = useFlightWith(OverLimitFlight)

  if (!query?.owner || !query?.slug || !isEnabled) {
    return null
  }

  if (isOverLimit && !isDismissed) {
    return (
      <div className="relative z-[1]">
        <div className="inset-0 flex h-8 w-full items-center justify-center bg-orange-200 px-4 text-center text-sm font-medium leading-8 text-orange-900 opacity-90 sm:px-0">
          <span className="flex flex-1 items-center justify-center gap-1 xl:hidden">
            <ExclamationTriangleIcon className="flex-0 h-4 w-4 text-orange-900 dark:text-orange-500" />
            Limit exceeded.
            <Link href={overLimitLink} className="underline">
              Upgrade or scale
            </Link>{' '}
            to avoid downtime.
          </span>
          <span className="hidden flex-1 flex-wrap items-center justify-center gap-1 xl:flex">
            <ExclamationTriangleIcon className="flex-0 h-4 w-4 text-orange-900 dark:text-orange-500" />
            Monthly usage limit exceeded. Please{' '}
            <Link href={overLimitLink} className="underline">
              adjust your plan or auto-scaling settings
            </Link>{' '}
            to maintain uninterrupted service.
          </span>
          <button
            onClick={dismissAlert}
            className="ml-2 rounded p-1 transition-colors hover:bg-orange-300"
            aria-label="Dismiss alert"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    )
  }

  return null
}
