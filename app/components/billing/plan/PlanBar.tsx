import React, { useMemo } from 'react'
import classNames from 'lib/classnames'
import { Plan } from '@sentio/service/billing/protos/service.pb'
import { money } from 'lib/money'
import BigDecimal from '@sentio/bigdecimal'
import { formatQuantity } from './Utils'

interface BarProps {
  value: number
  selected?: boolean
  current?: boolean
  onSelect?: (plan: string) => void
  plan?: Plan
  period?: 'month' | 'year'
  price?: BigDecimal
  overQuota?: boolean
  currentSave?: number
}

const Bar: React.FC<BarProps> = ({
  value,
  selected,
  current,
  onSelect,
  plan,
  period = 'month',
  price,
  overQuota,
  currentSave = 0
}) => {
  const clampedValue = overQuota ? 100 : Math.min(100, Math.max(0, value))
  const calculatedFlatFee = useMemo(() => {
    if (!price || !plan) return '$0'
    if (period === 'month') return `$${price.toFormat(0)}`
    if (period === 'year') return `$${price.multipliedBy(plan.yearlyDiscount ?? 1).toFormat(0)}`
    return '$0'
  }, [price, period])
  const annualDiscount = useMemo(() => {
    if (plan?.flatFee && money(plan.flatFee).isZero()) {
      return 0
    }
    if (plan?.yearlyDiscount && period === 'year') {
      return Math.floor(100 - plan.yearlyDiscount * 100)
    }
    return 0
  }, [plan, period])
  const borderClass = selected
    ? 'border-primary-500 dark:border-primary-700'
    : overQuota
      ? 'border-gray-400 dark:border-gray-200'
      : 'border-gray-600 dark:border-gray-300'
  const bgClass = selected
    ? 'bg-primary-500 dark:bg-primary-700'
    : overQuota
      ? 'bg-gray-400 dark:bg-gray-200'
      : 'bg-gray-600 dark:bg-gray-300'

  return (
    <div
      className={classNames('flex h-full w-full cursor-pointer flex-col items-center border-2', borderClass)}
      onClick={() => {
        if (plan?.id && onSelect) {
          onSelect(plan.id)
        }
      }}
    >
      <div className={classNames('flex w-full flex-col gap-2 border-b-2 px-4 py-3', borderClass)}>
        <div className="flex w-full justify-between">
          <label className="text-base font-bold text-gray-900">{plan?.name}</label>
          {current && <span className="text-icontent font-medium italic text-gray-900">Current</span>}
        </div>
        <div className="flex w-full justify-between">
          <div className="relative text-left text-gray-900">
            <span>
              <span className="text-xl font-bold">{calculatedFlatFee}</span>
              <span className="text-xs font-medium">/ mo</span>
            </span>
          </div>
          <div className="grid items-center justify-items-center">
            {annualDiscount > 0 && (
              <span className="bg-lake-blue-600 text-lake-blue-50 text-icontent whitespace-nowrap rounded px-1 font-medium">
                {annualDiscount}% off
              </span>
            )}
          </div>
        </div>
      </div>
      <div className={classNames('relative flex h-full w-full items-end')}>
        <div
          className={classNames('w-full transition-all duration-300', bgClass)}
          style={{ height: `${clampedValue}%` }}
        ></div>
        {overQuota ? (
          <div className="absolute left-0 top-0 h-full w-full bg-gray-400/50">
            <div className="flex h-full flex-col items-center justify-center">
              <span className="text-sm font-medium text-white dark:text-gray-700">
                Over Quota ({formatQuantity(plan?.unitCap ?? '')})
              </span>
            </div>
          </div>
        ) : currentSave > 10 ? (
          <div
            className={classNames(
              'absolute text-sm font-semibold transition-all duration-300',
              selected ? 'text-primary-500 dark:text-primary-700' : 'text-gray-600 dark:text-gray-300'
            )}
            style={{
              bottom: `${clampedValue}%`,
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 1
            }}
          >
            Save {currentSave}%
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default Bar
