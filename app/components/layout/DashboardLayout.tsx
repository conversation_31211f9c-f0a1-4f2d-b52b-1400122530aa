import { ReactElement } from 'react'
import { BaseLayout } from './BaseLayout'
import { ProjectMenu, ReadOnlyProjectMenu } from 'components/project/ProjectMenu'
import Navbar from 'components/navbar/Navbar'
import useUser from 'lib/data/use-user'

export function DashboardLayout({ title, children }: { title?: string; children: ReactElement }) {
  const { user } = useUser()
  return (
    <BaseLayout
      title={title || 'Dashboard'}
      nav={
        // supoort public pages
        user ? (
          <>
            <ProjectMenu />
            <div>
              <Navbar />
            </div>
          </>
        ) : (
          <>
            <div className="pl-2">
              <ReadOnlyProjectMenu />
            </div>
            <div>
              <Navbar />
            </div>
          </>
        )
      }
    >
      {children}
    </BaseLayout>
  )
}

export default DashboardLayout
